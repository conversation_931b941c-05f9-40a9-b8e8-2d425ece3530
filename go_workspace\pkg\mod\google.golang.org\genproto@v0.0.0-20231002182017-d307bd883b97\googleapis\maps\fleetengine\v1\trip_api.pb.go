// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.21.9
// source: google/maps/fleetengine/v1/trip_api.proto

package fleetengine

import (
	context "context"
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Selector for different solution types of a reported trip.
type ReportBillableTripRequest_SolutionType int32

const (
	// The default value. For backwards-compatibility, the API will use
	// `ON_DEMAND_RIDESHARING_AND_DELIVERIES` by default which is the first
	// supported solution type.
	ReportBillableTripRequest_SOLUTION_TYPE_UNSPECIFIED ReportBillableTripRequest_SolutionType = 0
	// The solution is an on-demand ridesharing and deliveries trip.
	ReportBillableTripRequest_ON_DEMAND_RIDESHARING_AND_DELIVERIES ReportBillableTripRequest_SolutionType = 1
)

// Enum value maps for ReportBillableTripRequest_SolutionType.
var (
	ReportBillableTripRequest_SolutionType_name = map[int32]string{
		0: "SOLUTION_TYPE_UNSPECIFIED",
		1: "ON_DEMAND_RIDESHARING_AND_DELIVERIES",
	}
	ReportBillableTripRequest_SolutionType_value = map[string]int32{
		"SOLUTION_TYPE_UNSPECIFIED":            0,
		"ON_DEMAND_RIDESHARING_AND_DELIVERIES": 1,
	}
)

func (x ReportBillableTripRequest_SolutionType) Enum() *ReportBillableTripRequest_SolutionType {
	p := new(ReportBillableTripRequest_SolutionType)
	*p = x
	return p
}

func (x ReportBillableTripRequest_SolutionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReportBillableTripRequest_SolutionType) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_v1_trip_api_proto_enumTypes[0].Descriptor()
}

func (ReportBillableTripRequest_SolutionType) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_v1_trip_api_proto_enumTypes[0]
}

func (x ReportBillableTripRequest_SolutionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReportBillableTripRequest_SolutionType.Descriptor instead.
func (ReportBillableTripRequest_SolutionType) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_trip_api_proto_rawDescGZIP(), []int{2, 0}
}

// CreateTrip request message.
type CreateTripRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The standard Fleet Engine request header.
	Header *RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Required. Must be in the format `providers/{provider}`.
	// The provider must be the Project ID (for example, `sample-cloud-project`)
	// of the Google Cloud Project of which the service account making
	// this call is a member.
	Parent string `protobuf:"bytes,3,opt,name=parent,proto3" json:"parent,omitempty"`
	// Required. Unique Trip ID.
	// Subject to the following restrictions:
	//
	// * Must be a valid Unicode string.
	// * Limited to a maximum length of 64 characters.
	// * Normalized according to [Unicode Normalization Form C]
	// (http://www.unicode.org/reports/tr15/).
	// * May not contain any of the following ASCII characters: '/', ':', '?',
	// ',', or '#'.
	TripId string `protobuf:"bytes,5,opt,name=trip_id,json=tripId,proto3" json:"trip_id,omitempty"`
	// Required. Trip entity to create.
	//
	// When creating a Trip, the following fields are required:
	//
	// * `trip_type`
	// * `pickup_point`
	//
	// The following fields are used if you provide them:
	//
	// * `number_of_passengers`
	// * `vehicle_id`
	// * `dropoff_point`
	// * `intermediate_destinations`
	//
	// Only `EXCLUSIVE` trips support multiple destinations.
	//
	// When `vehicle_id` is set for a shared trip, you must supply
	// the list of `Trip.vehicle_waypoints` to specify the order of the remaining
	// waypoints for the vehicle, otherwise the waypoint order will be
	// undetermined.
	//
	// When you specify `Trip.vehicle_waypoints`, the list must contain all
	// the remaining waypoints of the vehicle's trips, with no extra waypoints.
	// You must order these waypoints such that for a given trip, the pickup
	// point is before intermediate destinations, and all intermediate
	// destinations come before the drop-off point. An `EXCLUSIVE` trip's
	// waypoints must not interleave with any other trips.
	//
	// The `trip_id`, `waypoint_type` and `location` fields are used, and all
	// other TripWaypoint fields in `vehicle_waypoints` are ignored.
	//
	// All other Trip fields are ignored.
	Trip *Trip `protobuf:"bytes,4,opt,name=trip,proto3" json:"trip,omitempty"`
}

func (x *CreateTripRequest) Reset() {
	*x = CreateTripRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_trip_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTripRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTripRequest) ProtoMessage() {}

func (x *CreateTripRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_trip_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTripRequest.ProtoReflect.Descriptor instead.
func (*CreateTripRequest) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_trip_api_proto_rawDescGZIP(), []int{0}
}

func (x *CreateTripRequest) GetHeader() *RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CreateTripRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *CreateTripRequest) GetTripId() string {
	if x != nil {
		return x.TripId
	}
	return ""
}

func (x *CreateTripRequest) GetTrip() *Trip {
	if x != nil {
		return x.Trip
	}
	return nil
}

// GetTrip request message.
type GetTripRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The standard Fleet Engine request header.
	Header *RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Required. Must be in the format `providers/{provider}/trips/{trip}`.
	// The provider must be the Project ID (for example, `sample-cloud-project`)
	// of the Google Cloud Project of which the service account making
	// this call is a member.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// The subset of Trip fields that should be returned and their interpretation.
	View TripView `protobuf:"varint,11,opt,name=view,proto3,enum=maps.fleetengine.v1.TripView" json:"view,omitempty"`
	// Indicates the minimum timestamp (exclusive) for which `Trip.route` or
	// `Trip.current_route_segment` data are retrieved. If route data are
	// unchanged since this timestamp, the route field is not set in the response.
	// If a minimum is unspecified, the route data are always retrieved.
	CurrentRouteSegmentVersion *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=current_route_segment_version,json=currentRouteSegmentVersion,proto3" json:"current_route_segment_version,omitempty"`
	// Indicates the minimum timestamp (exclusive) for which
	// `Trip.remaining_waypoints` are retrieved. If they are unchanged since this
	// timestamp, the `remaining_waypoints` are not set in the response. If this
	// field is unspecified, `remaining_waypoints` is always retrieved.
	RemainingWaypointsVersion *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=remaining_waypoints_version,json=remainingWaypointsVersion,proto3" json:"remaining_waypoints_version,omitempty"`
	// The returned current route format, `LAT_LNG_LIST_TYPE` (in `Trip.route`),
	// or `ENCODED_POLYLINE_TYPE` (in `Trip.current_route_segment`). The default
	// is `LAT_LNG_LIST_TYPE`.
	RouteFormatType PolylineFormatType `protobuf:"varint,8,opt,name=route_format_type,json=routeFormatType,proto3,enum=maps.fleetengine.v1.PolylineFormatType" json:"route_format_type,omitempty"`
	// Indicates the minimum timestamp (exclusive) for which
	// `Trip.current_route_segment_traffic` is retrieved. If traffic data are
	// unchanged since this timestamp, the `current_route_segment_traffic` field
	// is not set in the response. If a minimum is unspecified, the traffic data
	// are always retrieved. Note that traffic is only available for On-Demand
	// Rides and Deliveries Solution customers.
	CurrentRouteSegmentTrafficVersion *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=current_route_segment_traffic_version,json=currentRouteSegmentTrafficVersion,proto3" json:"current_route_segment_traffic_version,omitempty"`
	// Indicates the minimum timestamp (exclusive) for which
	// `Trip.remaining_waypoints.traffic_to_waypoint` and
	// `Trip.remaining_waypoints.path_to_waypoint` data are retrieved. If data are
	// unchanged since this timestamp, the fields above are
	// not set in the response. If `remaining_waypoints_route_version` is
	// unspecified, traffic and path are always retrieved.
	RemainingWaypointsRouteVersion *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=remaining_waypoints_route_version,json=remainingWaypointsRouteVersion,proto3" json:"remaining_waypoints_route_version,omitempty"`
}

func (x *GetTripRequest) Reset() {
	*x = GetTripRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_trip_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTripRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTripRequest) ProtoMessage() {}

func (x *GetTripRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_trip_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTripRequest.ProtoReflect.Descriptor instead.
func (*GetTripRequest) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_trip_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetTripRequest) GetHeader() *RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetTripRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetTripRequest) GetView() TripView {
	if x != nil {
		return x.View
	}
	return TripView_TRIP_VIEW_UNSPECIFIED
}

func (x *GetTripRequest) GetCurrentRouteSegmentVersion() *timestamppb.Timestamp {
	if x != nil {
		return x.CurrentRouteSegmentVersion
	}
	return nil
}

func (x *GetTripRequest) GetRemainingWaypointsVersion() *timestamppb.Timestamp {
	if x != nil {
		return x.RemainingWaypointsVersion
	}
	return nil
}

func (x *GetTripRequest) GetRouteFormatType() PolylineFormatType {
	if x != nil {
		return x.RouteFormatType
	}
	return PolylineFormatType_UNKNOWN_FORMAT_TYPE
}

func (x *GetTripRequest) GetCurrentRouteSegmentTrafficVersion() *timestamppb.Timestamp {
	if x != nil {
		return x.CurrentRouteSegmentTrafficVersion
	}
	return nil
}

func (x *GetTripRequest) GetRemainingWaypointsRouteVersion() *timestamppb.Timestamp {
	if x != nil {
		return x.RemainingWaypointsRouteVersion
	}
	return nil
}

// ReportBillableTrip request message.
type ReportBillableTripRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. Must be in the format
	// `providers/{provider}/billableTrips/{billable_trip}`. The
	// provider must be the Project ID (for example, `sample-cloud-project`) of
	// the Google Cloud Project of which the service account making this call is a
	// member.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// Required. Two letter country code of the country where the trip takes
	// place. Price is defined according to country code.
	CountryCode string `protobuf:"bytes,3,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	// The platform upon which the request was issued.
	Platform BillingPlatformIdentifier `protobuf:"varint,5,opt,name=platform,proto3,enum=maps.fleetengine.v1.BillingPlatformIdentifier" json:"platform,omitempty"`
	// The identifiers that are directly related to the trip being reported. These
	// are usually IDs (for example, session IDs) of pre-booking operations done
	// before the trip ID is available. The number of `related_ids` is
	// limited to 50.
	RelatedIds []string `protobuf:"bytes,6,rep,name=related_ids,json=relatedIds,proto3" json:"related_ids,omitempty"`
	// The type of GMP product solution (for example,
	// `ON_DEMAND_RIDESHARING_AND_DELIVERIES`) used for the reported trip.
	SolutionType ReportBillableTripRequest_SolutionType `protobuf:"varint,7,opt,name=solution_type,json=solutionType,proto3,enum=maps.fleetengine.v1.ReportBillableTripRequest_SolutionType" json:"solution_type,omitempty"`
}

func (x *ReportBillableTripRequest) Reset() {
	*x = ReportBillableTripRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_trip_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportBillableTripRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportBillableTripRequest) ProtoMessage() {}

func (x *ReportBillableTripRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_trip_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportBillableTripRequest.ProtoReflect.Descriptor instead.
func (*ReportBillableTripRequest) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_trip_api_proto_rawDescGZIP(), []int{2}
}

func (x *ReportBillableTripRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ReportBillableTripRequest) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *ReportBillableTripRequest) GetPlatform() BillingPlatformIdentifier {
	if x != nil {
		return x.Platform
	}
	return BillingPlatformIdentifier_BILLING_PLATFORM_IDENTIFIER_UNSPECIFIED
}

func (x *ReportBillableTripRequest) GetRelatedIds() []string {
	if x != nil {
		return x.RelatedIds
	}
	return nil
}

func (x *ReportBillableTripRequest) GetSolutionType() ReportBillableTripRequest_SolutionType {
	if x != nil {
		return x.SolutionType
	}
	return ReportBillableTripRequest_SOLUTION_TYPE_UNSPECIFIED
}

// UpdateTrip request message.
type UpdateTripRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The standard Fleet Engine request header.
	Header *RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Required. Must be in the format
	// `providers/{provider}/trips/{trip}`. The provider must
	// be the Project ID (for example, `sample-consumer-project`) of the Google
	// Cloud Project of which the service account making this call is a member.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// Required. The Trip associated with the update.
	//
	// The following fields are maintained by the Fleet Engine. Do not update
	// them using Trip.update.
	//
	// * `current_route_segment`
	// * `current_route_segment_end_point`
	// * `current_route_segment_traffic`
	// * `current_route_segment_traffic_version`
	// * `current_route_segment_version`
	// * `dropoff_time`
	// * `eta_to_next_waypoint`
	// * `intermediate_destinations_version`
	// * `last_location`
	// * `name`
	// * `number_of_passengers`
	// * `pickup_time`
	// * `remaining_distance_meters`
	// * `remaining_time_to_first_waypoint`
	// * `remaining_waypoints`
	// * `remaining_waypoints_version`
	// * `route`
	//
	// When you update the `Trip.vehicle_id` for a shared trip, you must supply
	// the list of `Trip.vehicle_waypoints` to specify the order of the remaining
	// waypoints, otherwise the order will be undetermined.
	//
	// When you specify `Trip.vehicle_waypoints`, the list must contain all
	// the remaining waypoints of the vehicle's trips, with no extra waypoints.
	// You must order these waypoints such that for a given trip, the pickup
	// point is before intermediate destinations, and all intermediate
	// destinations come before the drop-off point. An `EXCLUSIVE` trip's
	// waypoints must not interleave with any other trips.
	// The `trip_id`, `waypoint_type` and `location` fields are used, and all
	// other TripWaypoint fields in `vehicle_waypoints` are ignored.
	//
	// To avoid a race condition for trips with multiple destinations, you
	// should provide `Trip.intermediate_destinations_version` when updating
	// the trip status to `ENROUTE_TO_INTERMEDIATE_DESTINATION`. The
	// `Trip.intermediate_destinations_version` passed must be consistent with
	// Fleet Engine's version. If it isn't, the request fails.
	Trip *Trip `protobuf:"bytes,4,opt,name=trip,proto3" json:"trip,omitempty"`
	// Required. The field mask indicating which fields in Trip to update.
	// The `update_mask` must contain at least one field.
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,5,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *UpdateTripRequest) Reset() {
	*x = UpdateTripRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_trip_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTripRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTripRequest) ProtoMessage() {}

func (x *UpdateTripRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_trip_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTripRequest.ProtoReflect.Descriptor instead.
func (*UpdateTripRequest) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_trip_api_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateTripRequest) GetHeader() *RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *UpdateTripRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateTripRequest) GetTrip() *Trip {
	if x != nil {
		return x.Trip
	}
	return nil
}

func (x *UpdateTripRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// SearchTrips request message.
type SearchTripsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The standard Fleet Engine request header.
	Header *RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Required. Must be in the format `providers/{provider}`.
	// The provider must be the Project ID (for example, `sample-cloud-project`)
	// of the Google Cloud Project of which the service account making
	// this call is a member.
	Parent string `protobuf:"bytes,3,opt,name=parent,proto3" json:"parent,omitempty"`
	// The vehicle associated with the trips in the request. If unspecified, the
	// returned trips do not contain:
	//
	// * `current_route_segment`
	// * `remaining_waypoints`
	// * `remaining_distance_meters`
	// * `eta_to_first_waypoint`
	VehicleId string `protobuf:"bytes,4,opt,name=vehicle_id,json=vehicleId,proto3" json:"vehicle_id,omitempty"`
	// If set to true, the response includes Trips that influence a driver's
	// route.
	ActiveTripsOnly bool `protobuf:"varint,5,opt,name=active_trips_only,json=activeTripsOnly,proto3" json:"active_trips_only,omitempty"`
	// If not set, the server decides the number of results to return.
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// Set this to a value previously returned in the `SearchTripsResponse` to
	// continue from previous results.
	PageToken string `protobuf:"bytes,7,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// If specified, returns the trips that have not been updated after the time
	// `(current - minimum_staleness)`.
	MinimumStaleness *durationpb.Duration `protobuf:"bytes,8,opt,name=minimum_staleness,json=minimumStaleness,proto3" json:"minimum_staleness,omitempty"`
}

func (x *SearchTripsRequest) Reset() {
	*x = SearchTripsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_trip_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchTripsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTripsRequest) ProtoMessage() {}

func (x *SearchTripsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_trip_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTripsRequest.ProtoReflect.Descriptor instead.
func (*SearchTripsRequest) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_trip_api_proto_rawDescGZIP(), []int{4}
}

func (x *SearchTripsRequest) GetHeader() *RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SearchTripsRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *SearchTripsRequest) GetVehicleId() string {
	if x != nil {
		return x.VehicleId
	}
	return ""
}

func (x *SearchTripsRequest) GetActiveTripsOnly() bool {
	if x != nil {
		return x.ActiveTripsOnly
	}
	return false
}

func (x *SearchTripsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SearchTripsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *SearchTripsRequest) GetMinimumStaleness() *durationpb.Duration {
	if x != nil {
		return x.MinimumStaleness
	}
	return nil
}

// SearchTrips response message.
type SearchTripsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of trips for the requested vehicle.
	Trips []*Trip `protobuf:"bytes,1,rep,name=trips,proto3" json:"trips,omitempty"`
	// Pass this token in the SearchTripsRequest to continue to
	// list results. If all results have been returned, this field is an empty
	// string or not present in the response.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *SearchTripsResponse) Reset() {
	*x = SearchTripsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_trip_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchTripsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTripsResponse) ProtoMessage() {}

func (x *SearchTripsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_trip_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTripsResponse.ProtoReflect.Descriptor instead.
func (*SearchTripsResponse) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_trip_api_proto_rawDescGZIP(), []int{5}
}

func (x *SearchTripsResponse) GetTrips() []*Trip {
	if x != nil {
		return x.Trips
	}
	return nil
}

func (x *SearchTripsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

var File_google_maps_fleetengine_v1_trip_api_proto protoreflect.FileDescriptor

var file_google_maps_fleetengine_v1_trip_api_proto_rawDesc = []byte{
	0x0a, 0x29, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72, 0x69,
	0x70, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x6d, 0x61, 0x70,
	0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x66, 0x6c, 0x65, 0x65, 0x74,
	0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70,
	0x73, 0x2f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2f, 0x76, 0x31,
	0x2f, 0x74, 0x72, 0x69, 0x70, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe2, 0x01, 0x0a,
	0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3a, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3f,
	0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x27,
	0xe0, 0x41, 0x02, 0xfa, 0x41, 0x21, 0x0a, 0x1f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x54, 0x72, 0x69, 0x70, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12,
	0x1c, 0x0a, 0x07, 0x74, 0x72, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x06, 0x74, 0x72, 0x69, 0x70, 0x49, 0x64, 0x12, 0x32, 0x0a,
	0x04, 0x74, 0x72, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61,
	0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x72, 0x69, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x74, 0x72, 0x69,
	0x70, 0x22, 0xa1, 0x05, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x69, 0x70, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65,
	0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x3b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x27,
	0xe0, 0x41, 0x02, 0xfa, 0x41, 0x21, 0x0a, 0x1f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x54, 0x72, 0x69, 0x70, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a,
	0x04, 0x76, 0x69, 0x65, 0x77, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6d, 0x61,
	0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x72, 0x69, 0x70, 0x56, 0x69, 0x65, 0x77, 0x52, 0x04, 0x76, 0x69, 0x65, 0x77,
	0x12, 0x5d, 0x0a, 0x1d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x6f, 0x75, 0x74,
	0x65, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x1a, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x5a, 0x0a, 0x1b, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x77, 0x61, 0x79,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x19, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x79, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x53, 0x0a, 0x11, 0x72,
	0x6f, 0x75, 0x74, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x6c,
	0x79, 0x6c, 0x69, 0x6e, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x6c, 0x0a, 0x25, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x6f, 0x75, 0x74,
	0x65, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69,
	0x63, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x21, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x65,
	0x0a, 0x21, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x77, 0x61, 0x79, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x1e, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67,
	0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x84, 0x03, 0x0a, 0x19, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x42, 0x69, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x72, 0x69, 0x70, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0c,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x4a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x49, 0x64,
	0x73, 0x12, 0x60, 0x0a, 0x0d, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e,
	0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x72, 0x69,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x57, 0x0a, 0x0c, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x4f, 0x4c, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x28, 0x0a, 0x24, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x4d, 0x41, 0x4e, 0x44, 0x5f,
	0x52, 0x49, 0x44, 0x45, 0x53, 0x48, 0x41, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x4e, 0x44, 0x5f,
	0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x49, 0x45, 0x53, 0x10, 0x01, 0x22, 0xde, 0x01, 0x0a,
	0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3a, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x04, 0x74, 0x72, 0x69, 0x70, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x70,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x74, 0x72, 0x69, 0x70, 0x12, 0x40, 0x0a, 0x0b, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x6b, 0x22, 0xbc, 0x02,
	0x0a, 0x12, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x72, 0x69, 0x70, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65,
	0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x1b, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x72, 0x69, 0x70, 0x73, 0x5f, 0x6f, 0x6e, 0x6c,
	0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54,
	0x72, 0x69, 0x70, 0x73, 0x4f, 0x6e, 0x6c, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x46, 0x0a, 0x11, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x5f,
	0x73, 0x74, 0x61, 0x6c, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x6d, 0x69, 0x6e, 0x69,
	0x6d, 0x75, 0x6d, 0x53, 0x74, 0x61, 0x6c, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x22, 0x6e, 0x0a, 0x13,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x72, 0x69, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x05, 0x74, 0x72, 0x69, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x70, 0x52, 0x05, 0x74,
	0x72, 0x69, 0x70, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e,
	0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x32, 0x9f, 0x07, 0x0a,
	0x0b, 0x54, 0x72, 0x69, 0x70, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xa8, 0x01, 0x0a,
	0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x70, 0x12, 0x26, 0x2e, 0x6d, 0x61,
	0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x70, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74,
	0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x70, 0x22, 0x57,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x22, 0x1e, 0x2f, 0x76, 0x31, 0x2f, 0x7b, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d,
	0x2f, 0x74, 0x72, 0x69, 0x70, 0x73, 0x3a, 0x04, 0x74, 0x72, 0x69, 0x70, 0x8a, 0xd3, 0xe4, 0x93,
	0x02, 0x25, 0x12, 0x23, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x7b, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x12, 0x9a, 0x01, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x69, 0x70, 0x12, 0x23, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74,
	0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x69,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e,
	0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x72, 0x69, 0x70, 0x22, 0x4f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x12, 0x1e, 0x2f, 0x76, 0x31,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73,
	0x2f, 0x2a, 0x2f, 0x74, 0x72, 0x69, 0x70, 0x73, 0x2f, 0x2a, 0x7d, 0x8a, 0xd3, 0xe4, 0x93, 0x02,
	0x23, 0x12, 0x21, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x7b, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x73, 0x2f, 0x2a, 0x7d, 0x12, 0xbf, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x42,
	0x69, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x72, 0x69, 0x70, 0x12, 0x2e, 0x2e, 0x6d, 0x61,
	0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65,
	0x54, 0x72, 0x69, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x61, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x32, 0x22, 0x2d, 0x2f, 0x76, 0x31,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73,
	0x2f, 0x2a, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x72, 0x69, 0x70, 0x73,
	0x2f, 0x2a, 0x7d, 0x3a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x3a, 0x01, 0x2a, 0x8a, 0xd3, 0xe4,
	0x93, 0x02, 0x23, 0x12, 0x21, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x7b, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x12, 0xbd, 0x01, 0x0a, 0x0b, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x54, 0x72, 0x69, 0x70, 0x73, 0x12, 0x27, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x54, 0x72, 0x69, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x28, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x72, 0x69, 0x70,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x5b, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x2a, 0x22, 0x25, 0x2f, 0x76, 0x31, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x74, 0x72, 0x69, 0x70,
	0x73, 0x3a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x3a, 0x01, 0x2a, 0x8a, 0xd3, 0xe4, 0x93, 0x02,
	0x25, 0x12, 0x23, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x7b, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x12, 0xa6, 0x01, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x72, 0x69, 0x70, 0x12, 0x26, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x72, 0x69, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e,
	0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x70, 0x22, 0x55, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26,
	0x1a, 0x1e, 0x2f, 0x76, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x74, 0x72, 0x69, 0x70, 0x73, 0x2f, 0x2a, 0x7d,
	0x3a, 0x04, 0x74, 0x72, 0x69, 0x70, 0x8a, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x12, 0x21, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x7b, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x1a,
	0x1d, 0xca, 0x41, 0x1a, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x42, 0x74,
	0x0a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x42, 0x07, 0x54, 0x72,
	0x69, 0x70, 0x41, 0x70, 0x69, 0x50, 0x01, 0x5a, 0x45, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x6d,
	0x61, 0x70, 0x73, 0x2f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2f,
	0x76, 0x31, 0x3b, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0xa2, 0x02,
	0x03, 0x43, 0x46, 0x45, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_maps_fleetengine_v1_trip_api_proto_rawDescOnce sync.Once
	file_google_maps_fleetengine_v1_trip_api_proto_rawDescData = file_google_maps_fleetengine_v1_trip_api_proto_rawDesc
)

func file_google_maps_fleetengine_v1_trip_api_proto_rawDescGZIP() []byte {
	file_google_maps_fleetengine_v1_trip_api_proto_rawDescOnce.Do(func() {
		file_google_maps_fleetengine_v1_trip_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_maps_fleetengine_v1_trip_api_proto_rawDescData)
	})
	return file_google_maps_fleetengine_v1_trip_api_proto_rawDescData
}

var file_google_maps_fleetengine_v1_trip_api_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_google_maps_fleetengine_v1_trip_api_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_google_maps_fleetengine_v1_trip_api_proto_goTypes = []interface{}{
	(ReportBillableTripRequest_SolutionType)(0), // 0: maps.fleetengine.v1.ReportBillableTripRequest.SolutionType
	(*CreateTripRequest)(nil),                   // 1: maps.fleetengine.v1.CreateTripRequest
	(*GetTripRequest)(nil),                      // 2: maps.fleetengine.v1.GetTripRequest
	(*ReportBillableTripRequest)(nil),           // 3: maps.fleetengine.v1.ReportBillableTripRequest
	(*UpdateTripRequest)(nil),                   // 4: maps.fleetengine.v1.UpdateTripRequest
	(*SearchTripsRequest)(nil),                  // 5: maps.fleetengine.v1.SearchTripsRequest
	(*SearchTripsResponse)(nil),                 // 6: maps.fleetengine.v1.SearchTripsResponse
	(*RequestHeader)(nil),                       // 7: maps.fleetengine.v1.RequestHeader
	(*Trip)(nil),                                // 8: maps.fleetengine.v1.Trip
	(TripView)(0),                               // 9: maps.fleetengine.v1.TripView
	(*timestamppb.Timestamp)(nil),               // 10: google.protobuf.Timestamp
	(PolylineFormatType)(0),                     // 11: maps.fleetengine.v1.PolylineFormatType
	(BillingPlatformIdentifier)(0),              // 12: maps.fleetengine.v1.BillingPlatformIdentifier
	(*fieldmaskpb.FieldMask)(nil),               // 13: google.protobuf.FieldMask
	(*durationpb.Duration)(nil),                 // 14: google.protobuf.Duration
	(*emptypb.Empty)(nil),                       // 15: google.protobuf.Empty
}
var file_google_maps_fleetengine_v1_trip_api_proto_depIdxs = []int32{
	7,  // 0: maps.fleetengine.v1.CreateTripRequest.header:type_name -> maps.fleetengine.v1.RequestHeader
	8,  // 1: maps.fleetengine.v1.CreateTripRequest.trip:type_name -> maps.fleetengine.v1.Trip
	7,  // 2: maps.fleetengine.v1.GetTripRequest.header:type_name -> maps.fleetengine.v1.RequestHeader
	9,  // 3: maps.fleetengine.v1.GetTripRequest.view:type_name -> maps.fleetengine.v1.TripView
	10, // 4: maps.fleetengine.v1.GetTripRequest.current_route_segment_version:type_name -> google.protobuf.Timestamp
	10, // 5: maps.fleetengine.v1.GetTripRequest.remaining_waypoints_version:type_name -> google.protobuf.Timestamp
	11, // 6: maps.fleetengine.v1.GetTripRequest.route_format_type:type_name -> maps.fleetengine.v1.PolylineFormatType
	10, // 7: maps.fleetengine.v1.GetTripRequest.current_route_segment_traffic_version:type_name -> google.protobuf.Timestamp
	10, // 8: maps.fleetengine.v1.GetTripRequest.remaining_waypoints_route_version:type_name -> google.protobuf.Timestamp
	12, // 9: maps.fleetengine.v1.ReportBillableTripRequest.platform:type_name -> maps.fleetengine.v1.BillingPlatformIdentifier
	0,  // 10: maps.fleetengine.v1.ReportBillableTripRequest.solution_type:type_name -> maps.fleetengine.v1.ReportBillableTripRequest.SolutionType
	7,  // 11: maps.fleetengine.v1.UpdateTripRequest.header:type_name -> maps.fleetengine.v1.RequestHeader
	8,  // 12: maps.fleetengine.v1.UpdateTripRequest.trip:type_name -> maps.fleetengine.v1.Trip
	13, // 13: maps.fleetengine.v1.UpdateTripRequest.update_mask:type_name -> google.protobuf.FieldMask
	7,  // 14: maps.fleetengine.v1.SearchTripsRequest.header:type_name -> maps.fleetengine.v1.RequestHeader
	14, // 15: maps.fleetengine.v1.SearchTripsRequest.minimum_staleness:type_name -> google.protobuf.Duration
	8,  // 16: maps.fleetengine.v1.SearchTripsResponse.trips:type_name -> maps.fleetengine.v1.Trip
	1,  // 17: maps.fleetengine.v1.TripService.CreateTrip:input_type -> maps.fleetengine.v1.CreateTripRequest
	2,  // 18: maps.fleetengine.v1.TripService.GetTrip:input_type -> maps.fleetengine.v1.GetTripRequest
	3,  // 19: maps.fleetengine.v1.TripService.ReportBillableTrip:input_type -> maps.fleetengine.v1.ReportBillableTripRequest
	5,  // 20: maps.fleetengine.v1.TripService.SearchTrips:input_type -> maps.fleetengine.v1.SearchTripsRequest
	4,  // 21: maps.fleetengine.v1.TripService.UpdateTrip:input_type -> maps.fleetengine.v1.UpdateTripRequest
	8,  // 22: maps.fleetengine.v1.TripService.CreateTrip:output_type -> maps.fleetengine.v1.Trip
	8,  // 23: maps.fleetengine.v1.TripService.GetTrip:output_type -> maps.fleetengine.v1.Trip
	15, // 24: maps.fleetengine.v1.TripService.ReportBillableTrip:output_type -> google.protobuf.Empty
	6,  // 25: maps.fleetengine.v1.TripService.SearchTrips:output_type -> maps.fleetengine.v1.SearchTripsResponse
	8,  // 26: maps.fleetengine.v1.TripService.UpdateTrip:output_type -> maps.fleetengine.v1.Trip
	22, // [22:27] is the sub-list for method output_type
	17, // [17:22] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_google_maps_fleetengine_v1_trip_api_proto_init() }
func file_google_maps_fleetengine_v1_trip_api_proto_init() {
	if File_google_maps_fleetengine_v1_trip_api_proto != nil {
		return
	}
	file_google_maps_fleetengine_v1_fleetengine_proto_init()
	file_google_maps_fleetengine_v1_header_proto_init()
	file_google_maps_fleetengine_v1_trips_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_maps_fleetengine_v1_trip_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTripRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_trip_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTripRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_trip_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportBillableTripRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_trip_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTripRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_trip_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchTripsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_trip_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchTripsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_maps_fleetengine_v1_trip_api_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_google_maps_fleetengine_v1_trip_api_proto_goTypes,
		DependencyIndexes: file_google_maps_fleetengine_v1_trip_api_proto_depIdxs,
		EnumInfos:         file_google_maps_fleetengine_v1_trip_api_proto_enumTypes,
		MessageInfos:      file_google_maps_fleetengine_v1_trip_api_proto_msgTypes,
	}.Build()
	File_google_maps_fleetengine_v1_trip_api_proto = out.File
	file_google_maps_fleetengine_v1_trip_api_proto_rawDesc = nil
	file_google_maps_fleetengine_v1_trip_api_proto_goTypes = nil
	file_google_maps_fleetengine_v1_trip_api_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// TripServiceClient is the client API for TripService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TripServiceClient interface {
	// Creates a trip in the Fleet Engine and returns the new trip.
	CreateTrip(ctx context.Context, in *CreateTripRequest, opts ...grpc.CallOption) (*Trip, error)
	// Get information about a single trip.
	GetTrip(ctx context.Context, in *GetTripRequest, opts ...grpc.CallOption) (*Trip, error)
	// Report billable trip usage.
	ReportBillableTrip(ctx context.Context, in *ReportBillableTripRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Get all the trips for a specific vehicle.
	SearchTrips(ctx context.Context, in *SearchTripsRequest, opts ...grpc.CallOption) (*SearchTripsResponse, error)
	// Updates trip data.
	UpdateTrip(ctx context.Context, in *UpdateTripRequest, opts ...grpc.CallOption) (*Trip, error)
}

type tripServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTripServiceClient(cc grpc.ClientConnInterface) TripServiceClient {
	return &tripServiceClient{cc}
}

func (c *tripServiceClient) CreateTrip(ctx context.Context, in *CreateTripRequest, opts ...grpc.CallOption) (*Trip, error) {
	out := new(Trip)
	err := c.cc.Invoke(ctx, "/maps.fleetengine.v1.TripService/CreateTrip", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tripServiceClient) GetTrip(ctx context.Context, in *GetTripRequest, opts ...grpc.CallOption) (*Trip, error) {
	out := new(Trip)
	err := c.cc.Invoke(ctx, "/maps.fleetengine.v1.TripService/GetTrip", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tripServiceClient) ReportBillableTrip(ctx context.Context, in *ReportBillableTripRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/maps.fleetengine.v1.TripService/ReportBillableTrip", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tripServiceClient) SearchTrips(ctx context.Context, in *SearchTripsRequest, opts ...grpc.CallOption) (*SearchTripsResponse, error) {
	out := new(SearchTripsResponse)
	err := c.cc.Invoke(ctx, "/maps.fleetengine.v1.TripService/SearchTrips", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tripServiceClient) UpdateTrip(ctx context.Context, in *UpdateTripRequest, opts ...grpc.CallOption) (*Trip, error) {
	out := new(Trip)
	err := c.cc.Invoke(ctx, "/maps.fleetengine.v1.TripService/UpdateTrip", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TripServiceServer is the server API for TripService service.
type TripServiceServer interface {
	// Creates a trip in the Fleet Engine and returns the new trip.
	CreateTrip(context.Context, *CreateTripRequest) (*Trip, error)
	// Get information about a single trip.
	GetTrip(context.Context, *GetTripRequest) (*Trip, error)
	// Report billable trip usage.
	ReportBillableTrip(context.Context, *ReportBillableTripRequest) (*emptypb.Empty, error)
	// Get all the trips for a specific vehicle.
	SearchTrips(context.Context, *SearchTripsRequest) (*SearchTripsResponse, error)
	// Updates trip data.
	UpdateTrip(context.Context, *UpdateTripRequest) (*Trip, error)
}

// UnimplementedTripServiceServer can be embedded to have forward compatible implementations.
type UnimplementedTripServiceServer struct {
}

func (*UnimplementedTripServiceServer) CreateTrip(context.Context, *CreateTripRequest) (*Trip, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTrip not implemented")
}
func (*UnimplementedTripServiceServer) GetTrip(context.Context, *GetTripRequest) (*Trip, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTrip not implemented")
}
func (*UnimplementedTripServiceServer) ReportBillableTrip(context.Context, *ReportBillableTripRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportBillableTrip not implemented")
}
func (*UnimplementedTripServiceServer) SearchTrips(context.Context, *SearchTripsRequest) (*SearchTripsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchTrips not implemented")
}
func (*UnimplementedTripServiceServer) UpdateTrip(context.Context, *UpdateTripRequest) (*Trip, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTrip not implemented")
}

func RegisterTripServiceServer(s *grpc.Server, srv TripServiceServer) {
	s.RegisterService(&_TripService_serviceDesc, srv)
}

func _TripService_CreateTrip_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTripRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TripServiceServer).CreateTrip(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/maps.fleetengine.v1.TripService/CreateTrip",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TripServiceServer).CreateTrip(ctx, req.(*CreateTripRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TripService_GetTrip_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTripRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TripServiceServer).GetTrip(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/maps.fleetengine.v1.TripService/GetTrip",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TripServiceServer).GetTrip(ctx, req.(*GetTripRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TripService_ReportBillableTrip_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportBillableTripRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TripServiceServer).ReportBillableTrip(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/maps.fleetengine.v1.TripService/ReportBillableTrip",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TripServiceServer).ReportBillableTrip(ctx, req.(*ReportBillableTripRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TripService_SearchTrips_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchTripsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TripServiceServer).SearchTrips(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/maps.fleetengine.v1.TripService/SearchTrips",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TripServiceServer).SearchTrips(ctx, req.(*SearchTripsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TripService_UpdateTrip_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTripRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TripServiceServer).UpdateTrip(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/maps.fleetengine.v1.TripService/UpdateTrip",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TripServiceServer).UpdateTrip(ctx, req.(*UpdateTripRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _TripService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "maps.fleetengine.v1.TripService",
	HandlerType: (*TripServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateTrip",
			Handler:    _TripService_CreateTrip_Handler,
		},
		{
			MethodName: "GetTrip",
			Handler:    _TripService_GetTrip_Handler,
		},
		{
			MethodName: "ReportBillableTrip",
			Handler:    _TripService_ReportBillableTrip_Handler,
		},
		{
			MethodName: "SearchTrips",
			Handler:    _TripService_SearchTrips_Handler,
		},
		{
			MethodName: "UpdateTrip",
			Handler:    _TripService_UpdateTrip_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "google/maps/fleetengine/v1/trip_api.proto",
}
