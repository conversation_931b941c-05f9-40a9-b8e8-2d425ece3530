  Application android.app  ActivityResultLauncher android.app.Activity  CompoundBarcodeView android.app.Activity  Intent android.app.Activity  Unit android.app.Activity  Job android.app.Service  ParcelFileDescriptor android.app.Service  	ProxyNode android.app.Service  ProxyVpnService android.app.Service  SingBoxCore android.app.Service  
TunToSocks android.app.Service  Context android.content  Intent android.content  ActivityResultLauncher android.content.Context  CompoundBarcodeView android.content.Context  Intent android.content.Context  Job android.content.Context  ParcelFileDescriptor android.content.Context  	ProxyNode android.content.Context  ProxyVpnService android.content.Context  SingBoxCore android.content.Context  
TunToSocks android.content.Context  Unit android.content.Context  ActivityResultLauncher android.content.ContextWrapper  CompoundBarcodeView android.content.ContextWrapper  Intent android.content.ContextWrapper  Job android.content.ContextWrapper  ParcelFileDescriptor android.content.ContextWrapper  	ProxyNode android.content.ContextWrapper  ProxyVpnService android.content.ContextWrapper  SingBoxCore android.content.ContextWrapper  
TunToSocks android.content.ContextWrapper  Unit android.content.ContextWrapper  
VpnService android.net  Job android.net.VpnService  ParcelFileDescriptor android.net.VpnService  	ProxyNode android.net.VpnService  ProxyVpnService android.net.VpnService  SingBoxCore android.net.VpnService  
TunToSocks android.net.VpnService  ParcelFileDescriptor 
android.os  ActivityResultLauncher  android.view.ContextThemeWrapper  CompoundBarcodeView  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  Unit  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  ActivityResultLauncher #androidx.activity.ComponentActivity  CompoundBarcodeView #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  Unit #androidx.activity.ComponentActivity  ActivityResultLauncher androidx.activity.result  ActivityResultLauncher #androidx.core.app.ComponentActivity  CompoundBarcodeView #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  Unit #androidx.core.app.ComponentActivity  AndroidViewModel androidx.lifecycle  LiveData androidx.lifecycle  	ViewModel androidx.lifecycle  Application #androidx.lifecycle.AndroidViewModel  Boolean #androidx.lifecycle.AndroidViewModel  ConnectionState #androidx.lifecycle.AndroidViewModel  ConnectionStats #androidx.lifecycle.AndroidViewModel  Intent #androidx.lifecycle.AndroidViewModel  List #androidx.lifecycle.AndroidViewModel  LiveData #androidx.lifecycle.AndroidViewModel  NodeManager #androidx.lifecycle.AndroidViewModel  ProxyConfig #androidx.lifecycle.AndroidViewModel  ProxyConfigDao #androidx.lifecycle.AndroidViewModel  	ProxyMode #androidx.lifecycle.AndroidViewModel  	ProxyNode #androidx.lifecycle.AndroidViewModel  	StateFlow #androidx.lifecycle.AndroidViewModel  String #androidx.lifecycle.AndroidViewModel  Application androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  
ConfigManager androidx.lifecycle.ViewModel  ConnectionState androidx.lifecycle.ViewModel  ConnectionStats androidx.lifecycle.ViewModel  ImportResult androidx.lifecycle.ViewModel  Intent androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  LiveData androidx.lifecycle.ViewModel  NodeManager androidx.lifecycle.ViewModel  ProxyConfig androidx.lifecycle.ViewModel  ProxyConfigDao androidx.lifecycle.ViewModel  	ProxyMode androidx.lifecycle.ViewModel  	ProxyNode androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  com androidx.lifecycle.ViewModel  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  AppDatabase androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  	Migration androidx.room.RoomDatabase  ProxyConfigDao androidx.room.RoomDatabase  ProxyNodeDao androidx.room.RoomDatabase  SupportSQLiteDatabase androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  	Migration androidx.room.migration  SupportSQLiteDatabase !androidx.room.migration.Migration  SupportSQLiteDatabase androidx.sqlite.db  execSQL (androidx.sqlite.db.SupportSQLiteDatabase  Unit com.fuck.fuckinggooo  ActivityResultLauncher !com.fuck.fuckinggooo.MainActivity  Intent !com.fuck.fuckinggooo.MainActivity  Unit !com.fuck.fuckinggooo.MainActivity  DatagramSocket com.fuck.fuckinggooo.core  DirectSocksProxy com.fuck.fuckinggooo.core  Int com.fuck.fuckinggooo.core  Job com.fuck.fuckinggooo.core  LibcoreManager com.fuck.fuckinggooo.core  Long com.fuck.fuckinggooo.core  ServerSocket com.fuck.fuckinggooo.core  SingBoxCore com.fuck.fuckinggooo.core  Socket com.fuck.fuckinggooo.core  String com.fuck.fuckinggooo.core  
TunToSocks com.fuck.fuckinggooo.core  Int *com.fuck.fuckinggooo.core.DirectSocksProxy  Job *com.fuck.fuckinggooo.core.DirectSocksProxy  	ProxyNode *com.fuck.fuckinggooo.core.DirectSocksProxy  ServerSocket *com.fuck.fuckinggooo.core.DirectSocksProxy  Int 4com.fuck.fuckinggooo.core.DirectSocksProxy.Companion  Job 4com.fuck.fuckinggooo.core.DirectSocksProxy.Companion  	ProxyNode 4com.fuck.fuckinggooo.core.DirectSocksProxy.Companion  ServerSocket 4com.fuck.fuckinggooo.core.DirectSocksProxy.Companion  ServerSocket /com.fuck.fuckinggooo.core.EnhancedFallbackProxy  	ProxyNode 'com.fuck.fuckinggooo.core.FallbackProxy  ServerSocket 'com.fuck.fuckinggooo.core.FallbackProxy  Long (com.fuck.fuckinggooo.core.LibcoreManager  Int )com.fuck.fuckinggooo.core.LocalSocksProxy  Job )com.fuck.fuckinggooo.core.LocalSocksProxy  ServerSocket )com.fuck.fuckinggooo.core.LocalSocksProxy  String )com.fuck.fuckinggooo.core.LocalSocksProxy  Int 3com.fuck.fuckinggooo.core.LocalSocksProxy.Companion  Job 3com.fuck.fuckinggooo.core.LocalSocksProxy.Companion  ServerSocket 3com.fuck.fuckinggooo.core.LocalSocksProxy.Companion  String 3com.fuck.fuckinggooo.core.LocalSocksProxy.Companion  Int )com.fuck.fuckinggooo.core.SimpleHttpProxy  Job )com.fuck.fuckinggooo.core.SimpleHttpProxy  ServerSocket )com.fuck.fuckinggooo.core.SimpleHttpProxy  Int 3com.fuck.fuckinggooo.core.SimpleHttpProxy.Companion  Job 3com.fuck.fuckinggooo.core.SimpleHttpProxy.Companion  ServerSocket 3com.fuck.fuckinggooo.core.SimpleHttpProxy.Companion  Int *com.fuck.fuckinggooo.core.SimpleTunToProxy  Job *com.fuck.fuckinggooo.core.SimpleTunToProxy  Long *com.fuck.fuckinggooo.core.SimpleTunToProxy  Socket *com.fuck.fuckinggooo.core.SimpleTunToProxy  String *com.fuck.fuckinggooo.core.SimpleTunToProxy  Int 4com.fuck.fuckinggooo.core.SimpleTunToProxy.Companion  Job 4com.fuck.fuckinggooo.core.SimpleTunToProxy.Companion  Long 4com.fuck.fuckinggooo.core.SimpleTunToProxy.Companion  Socket 4com.fuck.fuckinggooo.core.SimpleTunToProxy.Companion  String 4com.fuck.fuckinggooo.core.SimpleTunToProxy.Companion  Int 8com.fuck.fuckinggooo.core.SimpleTunToProxy.TcpConnection  Long 8com.fuck.fuckinggooo.core.SimpleTunToProxy.TcpConnection  Socket 8com.fuck.fuckinggooo.core.SimpleTunToProxy.TcpConnection  String 8com.fuck.fuckinggooo.core.SimpleTunToProxy.TcpConnection  Context %com.fuck.fuckinggooo.core.SingBoxCore  DirectSocksProxy %com.fuck.fuckinggooo.core.SingBoxCore  LibcoreManager %com.fuck.fuckinggooo.core.SingBoxCore  Context /com.fuck.fuckinggooo.core.SingBoxCore.Companion  DirectSocksProxy /com.fuck.fuckinggooo.core.SingBoxCore.Companion  LibcoreManager /com.fuck.fuckinggooo.core.SingBoxCore.Companion  DatagramSocket $com.fuck.fuckinggooo.core.TunToSocks  Int $com.fuck.fuckinggooo.core.TunToSocks  Job $com.fuck.fuckinggooo.core.TunToSocks  Long $com.fuck.fuckinggooo.core.TunToSocks  Socket $com.fuck.fuckinggooo.core.TunToSocks  String $com.fuck.fuckinggooo.core.TunToSocks  DatagramSocket .com.fuck.fuckinggooo.core.TunToSocks.Companion  Int .com.fuck.fuckinggooo.core.TunToSocks.Companion  Job .com.fuck.fuckinggooo.core.TunToSocks.Companion  Long .com.fuck.fuckinggooo.core.TunToSocks.Companion  Socket .com.fuck.fuckinggooo.core.TunToSocks.Companion  String .com.fuck.fuckinggooo.core.TunToSocks.Companion  Int 2com.fuck.fuckinggooo.core.TunToSocks.TcpConnection  Long 2com.fuck.fuckinggooo.core.TunToSocks.TcpConnection  Socket 2com.fuck.fuckinggooo.core.TunToSocks.TcpConnection  String 2com.fuck.fuckinggooo.core.TunToSocks.TcpConnection  DatagramSocket /com.fuck.fuckinggooo.core.TunToSocks.UdpSession  Int /com.fuck.fuckinggooo.core.TunToSocks.UdpSession  Long /com.fuck.fuckinggooo.core.TunToSocks.UdpSession  String /com.fuck.fuckinggooo.core.TunToSocks.UdpSession  AppDatabase com.fuck.fuckinggooo.model  Boolean com.fuck.fuckinggooo.model  ConnectionState com.fuck.fuckinggooo.model  
Converters com.fuck.fuckinggooo.model  ImportResult com.fuck.fuckinggooo.model  Int com.fuck.fuckinggooo.model  List com.fuck.fuckinggooo.model  Long com.fuck.fuckinggooo.model  OnConflictStrategy com.fuck.fuckinggooo.model  ProxyConfig com.fuck.fuckinggooo.model  ProxyConfigDao com.fuck.fuckinggooo.model  	ProxyNode com.fuck.fuckinggooo.model  ProxyNodeDao com.fuck.fuckinggooo.model  String com.fuck.fuckinggooo.model  Volatile com.fuck.fuckinggooo.model  kotlinx com.fuck.fuckinggooo.model  AppDatabase &com.fuck.fuckinggooo.model.AppDatabase  Context &com.fuck.fuckinggooo.model.AppDatabase  	Migration &com.fuck.fuckinggooo.model.AppDatabase  ProxyConfigDao &com.fuck.fuckinggooo.model.AppDatabase  ProxyNodeDao &com.fuck.fuckinggooo.model.AppDatabase  SupportSQLiteDatabase &com.fuck.fuckinggooo.model.AppDatabase  Volatile &com.fuck.fuckinggooo.model.AppDatabase  AppDatabase 0com.fuck.fuckinggooo.model.AppDatabase.Companion  Context 0com.fuck.fuckinggooo.model.AppDatabase.Companion  	Migration 0com.fuck.fuckinggooo.model.AppDatabase.Companion  ProxyConfigDao 0com.fuck.fuckinggooo.model.AppDatabase.Companion  ProxyNodeDao 0com.fuck.fuckinggooo.model.AppDatabase.Companion  SupportSQLiteDatabase 0com.fuck.fuckinggooo.model.AppDatabase.Companion  Volatile 0com.fuck.fuckinggooo.model.AppDatabase.Companion  List %com.fuck.fuckinggooo.model.Converters  	ProxyNode %com.fuck.fuckinggooo.model.Converters  String %com.fuck.fuckinggooo.model.Converters  
TypeConverter %com.fuck.fuckinggooo.model.Converters  List &com.fuck.fuckinggooo.model.ProxyConfig  Long &com.fuck.fuckinggooo.model.ProxyConfig  
PrimaryKey &com.fuck.fuckinggooo.model.ProxyConfig  	ProxyNode &com.fuck.fuckinggooo.model.ProxyConfig  String &com.fuck.fuckinggooo.model.ProxyConfig  Delete )com.fuck.fuckinggooo.model.ProxyConfigDao  Insert )com.fuck.fuckinggooo.model.ProxyConfigDao  List )com.fuck.fuckinggooo.model.ProxyConfigDao  OnConflictStrategy )com.fuck.fuckinggooo.model.ProxyConfigDao  ProxyConfig )com.fuck.fuckinggooo.model.ProxyConfigDao  Query )com.fuck.fuckinggooo.model.ProxyConfigDao  String )com.fuck.fuckinggooo.model.ProxyConfigDao  Update )com.fuck.fuckinggooo.model.ProxyConfigDao  kotlinx )com.fuck.fuckinggooo.model.ProxyConfigDao  Boolean $com.fuck.fuckinggooo.model.ProxyNode  Int $com.fuck.fuckinggooo.model.ProxyNode  Long $com.fuck.fuckinggooo.model.ProxyNode  
PrimaryKey $com.fuck.fuckinggooo.model.ProxyNode  String $com.fuck.fuckinggooo.model.ProxyNode  Insert 'com.fuck.fuckinggooo.model.ProxyNodeDao  List 'com.fuck.fuckinggooo.model.ProxyNodeDao  OnConflictStrategy 'com.fuck.fuckinggooo.model.ProxyNodeDao  	ProxyNode 'com.fuck.fuckinggooo.model.ProxyNodeDao  Query 'com.fuck.fuckinggooo.model.ProxyNodeDao  String 'com.fuck.fuckinggooo.model.ProxyNodeDao  kotlinx 'com.fuck.fuckinggooo.model.ProxyNodeDao  
ConfigManager com.fuck.fuckinggooo.service  Job com.fuck.fuckinggooo.service  NodeManager com.fuck.fuckinggooo.service  ProxyVpnService com.fuck.fuckinggooo.service  ServerSocket com.fuck.fuckinggooo.service  Volatile com.fuck.fuckinggooo.service  
VpnManager com.fuck.fuckinggooo.service  Job &com.fuck.fuckinggooo.service.ProxyCore  ProxyConfig &com.fuck.fuckinggooo.service.ProxyCore  ServerSocket &com.fuck.fuckinggooo.service.ProxyCore  Job ,com.fuck.fuckinggooo.service.ProxyVpnService  ParcelFileDescriptor ,com.fuck.fuckinggooo.service.ProxyVpnService  	ProxyNode ,com.fuck.fuckinggooo.service.ProxyVpnService  ProxyVpnService ,com.fuck.fuckinggooo.service.ProxyVpnService  SingBoxCore ,com.fuck.fuckinggooo.service.ProxyVpnService  
TunToSocks ,com.fuck.fuckinggooo.service.ProxyVpnService  Job 6com.fuck.fuckinggooo.service.ProxyVpnService.Companion  ParcelFileDescriptor 6com.fuck.fuckinggooo.service.ProxyVpnService.Companion  	ProxyNode 6com.fuck.fuckinggooo.service.ProxyVpnService.Companion  ProxyVpnService 6com.fuck.fuckinggooo.service.ProxyVpnService.Companion  SingBoxCore 6com.fuck.fuckinggooo.service.ProxyVpnService.Companion  
TunToSocks 6com.fuck.fuckinggooo.service.ProxyVpnService.Companion  ConnectionStats 'com.fuck.fuckinggooo.service.VpnManager  Context 'com.fuck.fuckinggooo.service.VpnManager  Job 'com.fuck.fuckinggooo.service.VpnManager  LiveData 'com.fuck.fuckinggooo.service.VpnManager  	ProxyNode 'com.fuck.fuckinggooo.service.VpnManager  Volatile 'com.fuck.fuckinggooo.service.VpnManager  
VpnManager 'com.fuck.fuckinggooo.service.VpnManager  VpnState 'com.fuck.fuckinggooo.service.VpnManager  Context 1com.fuck.fuckinggooo.service.VpnManager.Companion  Job 1com.fuck.fuckinggooo.service.VpnManager.Companion  LiveData 1com.fuck.fuckinggooo.service.VpnManager.Companion  	ProxyNode 1com.fuck.fuckinggooo.service.VpnManager.Companion  Volatile 1com.fuck.fuckinggooo.service.VpnManager.Companion  
VpnManager 1com.fuck.fuckinggooo.service.VpnManager.Companion  CompoundBarcodeView /com.fuck.fuckinggooo.ui.activity.QrScanActivity  CompoundBarcodeView 9com.fuck.fuckinggooo.ui.activity.QrScanActivity.Companion  Boolean !com.fuck.fuckinggooo.ui.dashboard  String !com.fuck.fuckinggooo.ui.dashboard  Application 4com.fuck.fuckinggooo.ui.dashboard.DashboardViewModel  Boolean 4com.fuck.fuckinggooo.ui.dashboard.DashboardViewModel  LiveData 4com.fuck.fuckinggooo.ui.dashboard.DashboardViewModel  	ProxyMode 4com.fuck.fuckinggooo.ui.dashboard.DashboardViewModel  	ProxyNode 4com.fuck.fuckinggooo.ui.dashboard.DashboardViewModel  String 4com.fuck.fuckinggooo.ui.dashboard.DashboardViewModel  DarkColorScheme com.fuck.fuckinggooo.ui.theme  LightColorScheme com.fuck.fuckinggooo.ui.theme  Pink40 com.fuck.fuckinggooo.ui.theme  Pink80 com.fuck.fuckinggooo.ui.theme  Purple40 com.fuck.fuckinggooo.ui.theme  Purple80 com.fuck.fuckinggooo.ui.theme  PurpleGrey40 com.fuck.fuckinggooo.ui.theme  PurpleGrey80 com.fuck.fuckinggooo.ui.theme  
Typography com.fuck.fuckinggooo.ui.theme  Boolean com.fuck.fuckinggooo.viewmodel  ConnectionStats com.fuck.fuckinggooo.viewmodel  List com.fuck.fuckinggooo.viewmodel  	ProxyMode com.fuck.fuckinggooo.viewmodel  String com.fuck.fuckinggooo.viewmodel  com com.fuck.fuckinggooo.viewmodel  
ConfigManager .com.fuck.fuckinggooo.viewmodel.ConfigViewModel  ImportResult .com.fuck.fuckinggooo.viewmodel.ConfigViewModel  List .com.fuck.fuckinggooo.viewmodel.ConfigViewModel  LiveData .com.fuck.fuckinggooo.viewmodel.ConfigViewModel  ProxyConfig .com.fuck.fuckinggooo.viewmodel.ConfigViewModel  com .com.fuck.fuckinggooo.viewmodel.ConfigViewModel  Application 1com.fuck.fuckinggooo.viewmodel.DashboardViewModel  Boolean 1com.fuck.fuckinggooo.viewmodel.DashboardViewModel  ConnectionStats 1com.fuck.fuckinggooo.viewmodel.DashboardViewModel  ProxyConfig 1com.fuck.fuckinggooo.viewmodel.DashboardViewModel  ProxyConfigDao 1com.fuck.fuckinggooo.viewmodel.DashboardViewModel  	ProxyMode 1com.fuck.fuckinggooo.viewmodel.DashboardViewModel  	StateFlow 1com.fuck.fuckinggooo.viewmodel.DashboardViewModel  Application ,com.fuck.fuckinggooo.viewmodel.MainViewModel  ConnectionState ,com.fuck.fuckinggooo.viewmodel.MainViewModel  Intent ,com.fuck.fuckinggooo.viewmodel.MainViewModel  List ,com.fuck.fuckinggooo.viewmodel.MainViewModel  LiveData ,com.fuck.fuckinggooo.viewmodel.MainViewModel  NodeManager ,com.fuck.fuckinggooo.viewmodel.MainViewModel  	ProxyNode ,com.fuck.fuckinggooo.viewmodel.MainViewModel  String ,com.fuck.fuckinggooo.viewmodel.MainViewModel  List ,com.fuck.fuckinggooo.viewmodel.NodeViewModel  LiveData ,com.fuck.fuckinggooo.viewmodel.NodeViewModel  NodeManager ,com.fuck.fuckinggooo.viewmodel.NodeViewModel  	ProxyNode ,com.fuck.fuckinggooo.viewmodel.NodeViewModel  CompoundBarcodeView com.journeyapps.barcodescanner  DatagramSocket java.io  Job java.io  ServerSocket java.io  Socket java.io  
Converters 	java.lang  OnConflictStrategy 	java.lang  ProxyConfig 	java.lang  	ProxyNode 	java.lang  com 	java.lang  kotlinx 	java.lang  DatagramSocket java.net  Job java.net  ServerSocket java.net  Socket java.net  Job java.nio.channels  ServerSocket java.nio.channels  Array kotlin  Boolean kotlin  
Converters kotlin  Int kotlin  Long kotlin  OnConflictStrategy kotlin  ProxyConfig kotlin  	ProxyNode kotlin  String kotlin  Unit kotlin  Volatile kotlin  arrayOf kotlin  com kotlin  kotlinx kotlin  
Converters kotlin.annotation  OnConflictStrategy kotlin.annotation  ProxyConfig kotlin.annotation  	ProxyNode kotlin.annotation  Volatile kotlin.annotation  com kotlin.annotation  kotlinx kotlin.annotation  
Converters kotlin.collections  List kotlin.collections  OnConflictStrategy kotlin.collections  ProxyConfig kotlin.collections  	ProxyNode kotlin.collections  Volatile kotlin.collections  com kotlin.collections  kotlinx kotlin.collections  
Converters kotlin.comparisons  OnConflictStrategy kotlin.comparisons  ProxyConfig kotlin.comparisons  	ProxyNode kotlin.comparisons  Volatile kotlin.comparisons  com kotlin.comparisons  kotlinx kotlin.comparisons  
Converters 	kotlin.io  OnConflictStrategy 	kotlin.io  ProxyConfig 	kotlin.io  	ProxyNode 	kotlin.io  Volatile 	kotlin.io  com 	kotlin.io  kotlinx 	kotlin.io  
Converters 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  ProxyConfig 
kotlin.jvm  	ProxyNode 
kotlin.jvm  Volatile 
kotlin.jvm  com 
kotlin.jvm  kotlinx 
kotlin.jvm  
Converters 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  ProxyConfig 
kotlin.ranges  	ProxyNode 
kotlin.ranges  Volatile 
kotlin.ranges  com 
kotlin.ranges  kotlinx 
kotlin.ranges  KClass kotlin.reflect  
Converters kotlin.sequences  OnConflictStrategy kotlin.sequences  ProxyConfig kotlin.sequences  	ProxyNode kotlin.sequences  Volatile kotlin.sequences  com kotlin.sequences  kotlinx kotlin.sequences  
Converters kotlin.text  OnConflictStrategy kotlin.text  ProxyConfig kotlin.text  	ProxyNode kotlin.text  Volatile kotlin.text  com kotlin.text  kotlinx kotlin.text  DatagramSocket kotlinx.coroutines  Job kotlinx.coroutines  ServerSocket kotlinx.coroutines  Socket kotlinx.coroutines  Volatile kotlinx.coroutines  Flow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 