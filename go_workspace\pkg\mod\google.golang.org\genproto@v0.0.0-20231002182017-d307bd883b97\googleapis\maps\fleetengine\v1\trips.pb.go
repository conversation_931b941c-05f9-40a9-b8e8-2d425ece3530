// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.21.9
// source: google/maps/fleetengine/v1/trips.proto

package fleetengine

import (
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The status of a trip indicating its progression.
type TripStatus int32

const (
	// Default, used for unspecified or unrecognized trip status.
	TripStatus_UNKNOWN_TRIP_STATUS TripStatus = 0
	// Newly created trip.
	TripStatus_NEW TripStatus = 1
	// The driver is on their way to the pickup point.
	TripStatus_ENROUTE_TO_PICKUP TripStatus = 2
	// The driver has arrived at the pickup point.
	TripStatus_ARRIVED_AT_PICKUP TripStatus = 3
	// The driver has arrived at an intermediate destination and is waiting for
	// the rider.
	TripStatus_ARRIVED_AT_INTERMEDIATE_DESTINATION TripStatus = 7
	// The driver is on their way to an intermediate destination
	// (not the dropoff point).
	TripStatus_ENROUTE_TO_INTERMEDIATE_DESTINATION TripStatus = 8
	// The driver has picked up the rider and is on their way to the
	// next destination.
	TripStatus_ENROUTE_TO_DROPOFF TripStatus = 4
	// The rider has been dropped off and the trip is complete.
	TripStatus_COMPLETE TripStatus = 5
	// The trip was canceled prior to pickup by the driver, rider, or
	// rideshare provider.
	TripStatus_CANCELED TripStatus = 6
)

// Enum value maps for TripStatus.
var (
	TripStatus_name = map[int32]string{
		0: "UNKNOWN_TRIP_STATUS",
		1: "NEW",
		2: "ENROUTE_TO_PICKUP",
		3: "ARRIVED_AT_PICKUP",
		7: "ARRIVED_AT_INTERMEDIATE_DESTINATION",
		8: "ENROUTE_TO_INTERMEDIATE_DESTINATION",
		4: "ENROUTE_TO_DROPOFF",
		5: "COMPLETE",
		6: "CANCELED",
	}
	TripStatus_value = map[string]int32{
		"UNKNOWN_TRIP_STATUS":                 0,
		"NEW":                                 1,
		"ENROUTE_TO_PICKUP":                   2,
		"ARRIVED_AT_PICKUP":                   3,
		"ARRIVED_AT_INTERMEDIATE_DESTINATION": 7,
		"ENROUTE_TO_INTERMEDIATE_DESTINATION": 8,
		"ENROUTE_TO_DROPOFF":                  4,
		"COMPLETE":                            5,
		"CANCELED":                            6,
	}
)

func (x TripStatus) Enum() *TripStatus {
	p := new(TripStatus)
	*p = x
	return p
}

func (x TripStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TripStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_v1_trips_proto_enumTypes[0].Descriptor()
}

func (TripStatus) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_v1_trips_proto_enumTypes[0]
}

func (x TripStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TripStatus.Descriptor instead.
func (TripStatus) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_trips_proto_rawDescGZIP(), []int{0}
}

// A set of values that indicate upon which platform the request was issued.
type BillingPlatformIdentifier int32

const (
	// Default. Used for unspecified platforms.
	BillingPlatformIdentifier_BILLING_PLATFORM_IDENTIFIER_UNSPECIFIED BillingPlatformIdentifier = 0
	// The platform is a client server.
	BillingPlatformIdentifier_SERVER BillingPlatformIdentifier = 1
	// The platform is a web browser.
	BillingPlatformIdentifier_WEB BillingPlatformIdentifier = 2
	// The platform is an Android mobile device.
	BillingPlatformIdentifier_ANDROID BillingPlatformIdentifier = 3
	// The platform is an IOS mobile device.
	BillingPlatformIdentifier_IOS BillingPlatformIdentifier = 4
	// Other platforms that are not listed in this enumeration.
	BillingPlatformIdentifier_OTHERS BillingPlatformIdentifier = 5
)

// Enum value maps for BillingPlatformIdentifier.
var (
	BillingPlatformIdentifier_name = map[int32]string{
		0: "BILLING_PLATFORM_IDENTIFIER_UNSPECIFIED",
		1: "SERVER",
		2: "WEB",
		3: "ANDROID",
		4: "IOS",
		5: "OTHERS",
	}
	BillingPlatformIdentifier_value = map[string]int32{
		"BILLING_PLATFORM_IDENTIFIER_UNSPECIFIED": 0,
		"SERVER":  1,
		"WEB":     2,
		"ANDROID": 3,
		"IOS":     4,
		"OTHERS":  5,
	}
)

func (x BillingPlatformIdentifier) Enum() *BillingPlatformIdentifier {
	p := new(BillingPlatformIdentifier)
	*p = x
	return p
}

func (x BillingPlatformIdentifier) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BillingPlatformIdentifier) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_v1_trips_proto_enumTypes[1].Descriptor()
}

func (BillingPlatformIdentifier) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_v1_trips_proto_enumTypes[1]
}

func (x BillingPlatformIdentifier) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BillingPlatformIdentifier.Descriptor instead.
func (BillingPlatformIdentifier) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_trips_proto_rawDescGZIP(), []int{1}
}

// Selector for different sets of Trip fields in a `GetTrip` response.  See
// [AIP-157](https://google.aip.dev/157) for context. Additional views are
// likely to be added.
type TripView int32

const (
	// The default value. For backwards-compatibility, the API will default to an
	// SDK view. To ensure stability and support, customers are
	// advised to select a `TripView` other than `SDK`.
	TripView_TRIP_VIEW_UNSPECIFIED TripView = 0
	// Includes fields that may not be interpretable or supportable using
	// publicly available libraries.
	TripView_SDK TripView = 1
	// Trip fields are populated for the Journey Sharing use case. This view is
	// intended for server-to-server communications.
	TripView_JOURNEY_SHARING_V1S TripView = 2
)

// Enum value maps for TripView.
var (
	TripView_name = map[int32]string{
		0: "TRIP_VIEW_UNSPECIFIED",
		1: "SDK",
		2: "JOURNEY_SHARING_V1S",
	}
	TripView_value = map[string]int32{
		"TRIP_VIEW_UNSPECIFIED": 0,
		"SDK":                   1,
		"JOURNEY_SHARING_V1S":   2,
	}
)

func (x TripView) Enum() *TripView {
	p := new(TripView)
	*p = x
	return p
}

func (x TripView) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TripView) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_v1_trips_proto_enumTypes[2].Descriptor()
}

func (TripView) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_v1_trips_proto_enumTypes[2]
}

func (x TripView) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TripView.Descriptor instead.
func (TripView) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_trips_proto_rawDescGZIP(), []int{2}
}

// Trip metadata.
type Trip struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Output only. In the format "providers/{provider}/trips/{trip}"
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// ID of the vehicle making this trip.
	VehicleId string `protobuf:"bytes,2,opt,name=vehicle_id,json=vehicleId,proto3" json:"vehicle_id,omitempty"`
	// Current status of the trip.
	TripStatus TripStatus `protobuf:"varint,3,opt,name=trip_status,json=tripStatus,proto3,enum=maps.fleetengine.v1.TripStatus" json:"trip_status,omitempty"`
	// The type of the trip.
	TripType TripType `protobuf:"varint,4,opt,name=trip_type,json=tripType,proto3,enum=maps.fleetengine.v1.TripType" json:"trip_type,omitempty"`
	// Location where customer indicates they will be picked up.
	PickupPoint *TerminalLocation `protobuf:"bytes,5,opt,name=pickup_point,json=pickupPoint,proto3" json:"pickup_point,omitempty"`
	// Input only. The actual location when and where customer was picked up.
	// This field is for provider to provide feedback on actual pickup
	// information.
	ActualPickupPoint *StopLocation `protobuf:"bytes,22,opt,name=actual_pickup_point,json=actualPickupPoint,proto3" json:"actual_pickup_point,omitempty"`
	// Input only. The actual time and location of the driver arrival at
	// the pickup point.
	// This field is for provider to provide feedback on actual arrival
	// information at the pickup point.
	ActualPickupArrivalPoint *StopLocation `protobuf:"bytes,32,opt,name=actual_pickup_arrival_point,json=actualPickupArrivalPoint,proto3" json:"actual_pickup_arrival_point,omitempty"`
	// Output only. Either the estimated future time when the rider(s) will be
	// picked up, or the actual time when they were picked up.
	PickupTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=pickup_time,json=pickupTime,proto3" json:"pickup_time,omitempty"`
	// Intermediate stops in order that the trip requests (in addition
	// to pickup and dropoff). Initially this will not be supported for shared
	// trips.
	IntermediateDestinations []*TerminalLocation `protobuf:"bytes,14,rep,name=intermediate_destinations,json=intermediateDestinations,proto3" json:"intermediate_destinations,omitempty"`
	// Indicates the last time the `intermediate_destinations` was modified.
	// Your server should cache this value and pass it in `UpdateTripRequest`
	// when update `intermediate_destination_index` to ensure the
	// `intermediate_destinations` is not changed.
	IntermediateDestinationsVersion *timestamppb.Timestamp `protobuf:"bytes,25,opt,name=intermediate_destinations_version,json=intermediateDestinationsVersion,proto3" json:"intermediate_destinations_version,omitempty"`
	// When `TripStatus` is `ENROUTE_TO_INTERMEDIATE_DESTINATION`, a number
	// between [0..N-1] indicating which intermediate destination the vehicle will
	// cross next. When `TripStatus` is `ARRIVED_AT_INTERMEDIATE_DESTINATION`, a
	// number between [0..N-1] indicating which intermediate destination the
	// vehicle is at. The provider sets this value. If there are no
	// `intermediate_destinations`, this field is ignored.
	IntermediateDestinationIndex int32 `protobuf:"varint,15,opt,name=intermediate_destination_index,json=intermediateDestinationIndex,proto3" json:"intermediate_destination_index,omitempty"`
	// Input only. The actual time and location of the driver's arrival at
	// an intermediate destination.
	// This field is for provider to provide feedback on actual arrival
	// information at intermediate destinations.
	ActualIntermediateDestinationArrivalPoints []*StopLocation `protobuf:"bytes,33,rep,name=actual_intermediate_destination_arrival_points,json=actualIntermediateDestinationArrivalPoints,proto3" json:"actual_intermediate_destination_arrival_points,omitempty"`
	// Input only. The actual time and location when and where the customer was
	// picked up from an intermediate destination. This field is for provider to
	// provide feedback on actual pickup information at intermediate destinations.
	ActualIntermediateDestinations []*StopLocation `protobuf:"bytes,34,rep,name=actual_intermediate_destinations,json=actualIntermediateDestinations,proto3" json:"actual_intermediate_destinations,omitempty"`
	// Location where customer indicates they will be dropped off.
	DropoffPoint *TerminalLocation `protobuf:"bytes,7,opt,name=dropoff_point,json=dropoffPoint,proto3" json:"dropoff_point,omitempty"`
	// Input only. The actual time and location when and where customer was
	// dropped off. This field is for provider to provide feedback on actual
	// dropoff information.
	ActualDropoffPoint *StopLocation `protobuf:"bytes,23,opt,name=actual_dropoff_point,json=actualDropoffPoint,proto3" json:"actual_dropoff_point,omitempty"`
	// Output only. Either the estimated future time when the rider(s) will be
	// dropped off at the final destination, or the actual time when they were
	// dropped off.
	DropoffTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=dropoff_time,json=dropoffTime,proto3" json:"dropoff_time,omitempty"`
	// Output only. The full path from the current location to the dropoff point,
	// inclusive. This path could include waypoints from other trips.
	RemainingWaypoints []*TripWaypoint `protobuf:"bytes,16,rep,name=remaining_waypoints,json=remainingWaypoints,proto3" json:"remaining_waypoints,omitempty"`
	// This field supports manual ordering of the waypoints for the trip. It
	// contains all of the remaining waypoints for the assigned vehicle, as well
	// as the pickup and drop-off waypoints for this trip. If the trip hasn't been
	// assigned to a vehicle, then this field is ignored. For privacy reasons,
	// this field is only populated by the server on UpdateTrip and CreateTrip
	// calls, NOT on GetTrip calls.
	VehicleWaypoints []*TripWaypoint `protobuf:"bytes,20,rep,name=vehicle_waypoints,json=vehicleWaypoints,proto3" json:"vehicle_waypoints,omitempty"`
	// Output only. Anticipated route for this trip to the first entry in
	// remaining_waypoints. Note that the first waypoint may belong to a different
	// trip.
	Route []*latlng.LatLng `protobuf:"bytes,9,rep,name=route,proto3" json:"route,omitempty"`
	// Output only. An encoded path to the next waypoint.
	//
	// Note: This field is intended only for use by the Driver SDK and Consumer
	// SDK. Decoding is not yet supported.
	CurrentRouteSegment string `protobuf:"bytes,21,opt,name=current_route_segment,json=currentRouteSegment,proto3" json:"current_route_segment,omitempty"`
	// Output only. Indicates the last time the route was modified.
	//
	// Note: This field is intended only for use by the Driver SDK and Consumer
	// SDK.
	CurrentRouteSegmentVersion *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=current_route_segment_version,json=currentRouteSegmentVersion,proto3" json:"current_route_segment_version,omitempty"`
	// Output only. Indicates the traffic conditions along the
	// `current_route_segment` when they're available.
	//
	// Note: This field is intended only for use by the Driver SDK and Consumer
	// SDK.
	CurrentRouteSegmentTraffic *ConsumableTrafficPolyline `protobuf:"bytes,28,opt,name=current_route_segment_traffic,json=currentRouteSegmentTraffic,proto3" json:"current_route_segment_traffic,omitempty"`
	// Output only. Indicates the last time the `current_route_segment_traffic`
	// was modified.
	//
	// Note: This field is intended only for use by the Driver SDK and Consumer
	// SDK.
	CurrentRouteSegmentTrafficVersion *timestamppb.Timestamp `protobuf:"bytes,30,opt,name=current_route_segment_traffic_version,json=currentRouteSegmentTrafficVersion,proto3" json:"current_route_segment_traffic_version,omitempty"`
	// Output only. The waypoint where `current_route_segment` ends.
	CurrentRouteSegmentEndPoint *TripWaypoint `protobuf:"bytes,24,opt,name=current_route_segment_end_point,json=currentRouteSegmentEndPoint,proto3" json:"current_route_segment_end_point,omitempty"`
	// Output only. The remaining driving distance in the `current_route_segment`
	// field. The value is unspecified if the trip is not assigned to a vehicle,
	// or the trip is completed or cancelled.
	RemainingDistanceMeters *wrapperspb.Int32Value `protobuf:"bytes,12,opt,name=remaining_distance_meters,json=remainingDistanceMeters,proto3" json:"remaining_distance_meters,omitempty"`
	// Output only. The ETA to the next waypoint (the first entry in the
	// `remaining_waypoints` field). The value is unspecified if the trip is not
	// assigned to a vehicle, or the trip is inactive (completed or cancelled).
	EtaToFirstWaypoint *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=eta_to_first_waypoint,json=etaToFirstWaypoint,proto3" json:"eta_to_first_waypoint,omitempty"`
	// Output only. The duration from when the Trip data is returned to the time
	// in `Trip.eta_to_first_waypoint`. The value is unspecified if the trip is
	// not assigned to a vehicle, or the trip is inactive (completed or
	// cancelled).
	RemainingTimeToFirstWaypoint *durationpb.Duration `protobuf:"bytes,27,opt,name=remaining_time_to_first_waypoint,json=remainingTimeToFirstWaypoint,proto3" json:"remaining_time_to_first_waypoint,omitempty"`
	// Output only. Indicates the last time that `remaining_waypoints` was changed
	// (a waypoint was added, removed, or changed).
	RemainingWaypointsVersion *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=remaining_waypoints_version,json=remainingWaypointsVersion,proto3" json:"remaining_waypoints_version,omitempty"`
	// Output only. Indicates the last time the
	// `remaining_waypoints.path_to_waypoint` and
	// `remaining_waypoints.traffic_to_waypoint` were modified. Your client app
	// should cache this value and pass it in `GetTripRequest` to ensure the
	// paths and traffic for `remaining_waypoints` are only returned if updated.
	RemainingWaypointsRouteVersion *timestamppb.Timestamp `protobuf:"bytes,29,opt,name=remaining_waypoints_route_version,json=remainingWaypointsRouteVersion,proto3" json:"remaining_waypoints_route_version,omitempty"`
	// Immutable. Indicates the number of passengers on this trip and does not
	// include the driver. A vehicle must have available capacity to be returned
	// in SearchVehicles.
	NumberOfPassengers int32 `protobuf:"varint,10,opt,name=number_of_passengers,json=numberOfPassengers,proto3" json:"number_of_passengers,omitempty"`
	// Output only. Indicates the last reported location of the vehicle along the
	// route.
	LastLocation *VehicleLocation `protobuf:"bytes,11,opt,name=last_location,json=lastLocation,proto3" json:"last_location,omitempty"`
	// Output only. Indicates whether the vehicle's `last_location` can be snapped
	// to the current_route_segment. False if `last_location` or
	// `current_route_segment` doesn't exist.
	// It is computed by Fleet Engine. Any update from clients will be ignored.
	LastLocationSnappable bool `protobuf:"varint,26,opt,name=last_location_snappable,json=lastLocationSnappable,proto3" json:"last_location_snappable,omitempty"`
	// The subset of Trip fields that are populated and how they should be
	// interpreted.
	View TripView `protobuf:"varint,31,opt,name=view,proto3,enum=maps.fleetengine.v1.TripView" json:"view,omitempty"`
}

func (x *Trip) Reset() {
	*x = Trip{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_trips_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Trip) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Trip) ProtoMessage() {}

func (x *Trip) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_trips_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Trip.ProtoReflect.Descriptor instead.
func (*Trip) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_trips_proto_rawDescGZIP(), []int{0}
}

func (x *Trip) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Trip) GetVehicleId() string {
	if x != nil {
		return x.VehicleId
	}
	return ""
}

func (x *Trip) GetTripStatus() TripStatus {
	if x != nil {
		return x.TripStatus
	}
	return TripStatus_UNKNOWN_TRIP_STATUS
}

func (x *Trip) GetTripType() TripType {
	if x != nil {
		return x.TripType
	}
	return TripType_UNKNOWN_TRIP_TYPE
}

func (x *Trip) GetPickupPoint() *TerminalLocation {
	if x != nil {
		return x.PickupPoint
	}
	return nil
}

func (x *Trip) GetActualPickupPoint() *StopLocation {
	if x != nil {
		return x.ActualPickupPoint
	}
	return nil
}

func (x *Trip) GetActualPickupArrivalPoint() *StopLocation {
	if x != nil {
		return x.ActualPickupArrivalPoint
	}
	return nil
}

func (x *Trip) GetPickupTime() *timestamppb.Timestamp {
	if x != nil {
		return x.PickupTime
	}
	return nil
}

func (x *Trip) GetIntermediateDestinations() []*TerminalLocation {
	if x != nil {
		return x.IntermediateDestinations
	}
	return nil
}

func (x *Trip) GetIntermediateDestinationsVersion() *timestamppb.Timestamp {
	if x != nil {
		return x.IntermediateDestinationsVersion
	}
	return nil
}

func (x *Trip) GetIntermediateDestinationIndex() int32 {
	if x != nil {
		return x.IntermediateDestinationIndex
	}
	return 0
}

func (x *Trip) GetActualIntermediateDestinationArrivalPoints() []*StopLocation {
	if x != nil {
		return x.ActualIntermediateDestinationArrivalPoints
	}
	return nil
}

func (x *Trip) GetActualIntermediateDestinations() []*StopLocation {
	if x != nil {
		return x.ActualIntermediateDestinations
	}
	return nil
}

func (x *Trip) GetDropoffPoint() *TerminalLocation {
	if x != nil {
		return x.DropoffPoint
	}
	return nil
}

func (x *Trip) GetActualDropoffPoint() *StopLocation {
	if x != nil {
		return x.ActualDropoffPoint
	}
	return nil
}

func (x *Trip) GetDropoffTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DropoffTime
	}
	return nil
}

func (x *Trip) GetRemainingWaypoints() []*TripWaypoint {
	if x != nil {
		return x.RemainingWaypoints
	}
	return nil
}

func (x *Trip) GetVehicleWaypoints() []*TripWaypoint {
	if x != nil {
		return x.VehicleWaypoints
	}
	return nil
}

func (x *Trip) GetRoute() []*latlng.LatLng {
	if x != nil {
		return x.Route
	}
	return nil
}

func (x *Trip) GetCurrentRouteSegment() string {
	if x != nil {
		return x.CurrentRouteSegment
	}
	return ""
}

func (x *Trip) GetCurrentRouteSegmentVersion() *timestamppb.Timestamp {
	if x != nil {
		return x.CurrentRouteSegmentVersion
	}
	return nil
}

func (x *Trip) GetCurrentRouteSegmentTraffic() *ConsumableTrafficPolyline {
	if x != nil {
		return x.CurrentRouteSegmentTraffic
	}
	return nil
}

func (x *Trip) GetCurrentRouteSegmentTrafficVersion() *timestamppb.Timestamp {
	if x != nil {
		return x.CurrentRouteSegmentTrafficVersion
	}
	return nil
}

func (x *Trip) GetCurrentRouteSegmentEndPoint() *TripWaypoint {
	if x != nil {
		return x.CurrentRouteSegmentEndPoint
	}
	return nil
}

func (x *Trip) GetRemainingDistanceMeters() *wrapperspb.Int32Value {
	if x != nil {
		return x.RemainingDistanceMeters
	}
	return nil
}

func (x *Trip) GetEtaToFirstWaypoint() *timestamppb.Timestamp {
	if x != nil {
		return x.EtaToFirstWaypoint
	}
	return nil
}

func (x *Trip) GetRemainingTimeToFirstWaypoint() *durationpb.Duration {
	if x != nil {
		return x.RemainingTimeToFirstWaypoint
	}
	return nil
}

func (x *Trip) GetRemainingWaypointsVersion() *timestamppb.Timestamp {
	if x != nil {
		return x.RemainingWaypointsVersion
	}
	return nil
}

func (x *Trip) GetRemainingWaypointsRouteVersion() *timestamppb.Timestamp {
	if x != nil {
		return x.RemainingWaypointsRouteVersion
	}
	return nil
}

func (x *Trip) GetNumberOfPassengers() int32 {
	if x != nil {
		return x.NumberOfPassengers
	}
	return 0
}

func (x *Trip) GetLastLocation() *VehicleLocation {
	if x != nil {
		return x.LastLocation
	}
	return nil
}

func (x *Trip) GetLastLocationSnappable() bool {
	if x != nil {
		return x.LastLocationSnappable
	}
	return false
}

func (x *Trip) GetView() TripView {
	if x != nil {
		return x.View
	}
	return TripView_TRIP_VIEW_UNSPECIFIED
}

// The actual location where a stop (pickup/dropoff) happened.
type StopLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. Denotes the actual location.
	Point *latlng.LatLng `protobuf:"bytes,1,opt,name=point,proto3" json:"point,omitempty"`
	// Indicates when the stop happened.
	Timestamp *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// Input only. Deprecated.  Use the timestamp field.
	//
	// Deprecated: Do not use.
	StopTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=stop_time,json=stopTime,proto3" json:"stop_time,omitempty"`
}

func (x *StopLocation) Reset() {
	*x = StopLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_trips_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopLocation) ProtoMessage() {}

func (x *StopLocation) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_trips_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopLocation.ProtoReflect.Descriptor instead.
func (*StopLocation) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_trips_proto_rawDescGZIP(), []int{1}
}

func (x *StopLocation) GetPoint() *latlng.LatLng {
	if x != nil {
		return x.Point
	}
	return nil
}

func (x *StopLocation) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

// Deprecated: Do not use.
func (x *StopLocation) GetStopTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StopTime
	}
	return nil
}

var File_google_maps_fleetengine_v1_trips_proto protoreflect.FileDescriptor

var file_google_maps_fleetengine_v1_trips_proto_rawDesc = []byte{
	0x0a, 0x26, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72, 0x69,
	0x70, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66,
	0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x6d, 0x61, 0x70, 0x73, 0x2f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f,
	0x6c, 0x61, 0x74, 0x6c, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf8, 0x15, 0x0a,
	0x04, 0x54, 0x72, 0x69, 0x70, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x40, 0x0a,
	0x0b, 0x74, 0x72, 0x69, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x70, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x0a, 0x74, 0x72, 0x69, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x3a, 0x0a, 0x09, 0x74, 0x72, 0x69, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x70, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x08, 0x74, 0x72, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x48, 0x0a, 0x0c, 0x70,
	0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x56, 0x0a, 0x13, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x5f,
	0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x11, 0x61, 0x63, 0x74, 0x75,
	0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x65, 0x0a,
	0x1b, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x61,
	0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x20, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x18, 0x61, 0x63, 0x74, 0x75,
	0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50,
	0x6f, 0x69, 0x6e, 0x74, 0x12, 0x40, 0x0a, 0x0b, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a, 0x70, 0x69, 0x63, 0x6b,
	0x75, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x62, 0x0a, 0x19, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x61, 0x70, 0x73,
	0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x18, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x44, 0x65,
	0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x66, 0x0a, 0x21, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x74, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x1f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x44,
	0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x1e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x74, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x1c, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x8a, 0x01, 0x0a, 0x2e, 0x61, 0x63, 0x74,
	0x75, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65,
	0x5f, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x72, 0x72,
	0x69, 0x76, 0x61, 0x6c, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x21, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x2a, 0x61, 0x63, 0x74, 0x75, 0x61,
	0x6c, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x44, 0x65, 0x73,
	0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x70, 0x0a, 0x20, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x5f,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x65, 0x73,
	0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x22, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x1e, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x44, 0x65, 0x73, 0x74, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4a, 0x0a, 0x0d, 0x64, 0x72, 0x6f, 0x70, 0x6f,
	0x66, 0x66, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x64, 0x72, 0x6f, 0x70, 0x6f, 0x66, 0x66, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x12, 0x58, 0x0a, 0x14, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x64, 0x72,
	0x6f, 0x70, 0x6f, 0x66, 0x66, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x12, 0x61, 0x63, 0x74, 0x75, 0x61,
	0x6c, 0x44, 0x72, 0x6f, 0x70, 0x6f, 0x66, 0x66, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x42, 0x0a,
	0x0c, 0x64, 0x72, 0x6f, 0x70, 0x6f, 0x66, 0x66, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42,
	0x03, 0xe0, 0x41, 0x03, 0x52, 0x0b, 0x64, 0x72, 0x6f, 0x70, 0x6f, 0x66, 0x66, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x57, 0x0a, 0x13, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x77,
	0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x70, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x12, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e,
	0x67, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x4e, 0x0a, 0x11, 0x76, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18,
	0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x70,
	0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x10, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x2e, 0x0a, 0x05, 0x72, 0x6f,
	0x75, 0x74, 0x65, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x42, 0x03,
	0xe0, 0x41, 0x03, 0x52, 0x05, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x15, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x13,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x53, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x62, 0x0a, 0x1d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x72,
	0x6f, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x1a, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x76, 0x0a, 0x1d, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x61, 0x62, 0x6c, 0x65, 0x54,
	0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x50, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x03,
	0xe0, 0x41, 0x03, 0x52, 0x1a, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x12,
	0x71, 0x0a, 0x25, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65,
	0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52,
	0x21, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x53, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x6c, 0x0a, 0x1f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x6f,
	0x75, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x61,
	0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x72, 0x69, 0x70, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x42, 0x03,
	0xe0, 0x41, 0x03, 0x52, 0x1b, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74,
	0x12, 0x5c, 0x0a, 0x19, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x17, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67,
	0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x52,
	0x0a, 0x15, 0x65, 0x74, 0x61, 0x5f, 0x74, 0x6f, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x77,
	0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x12,
	0x65, 0x74, 0x61, 0x54, 0x6f, 0x46, 0x69, 0x72, 0x73, 0x74, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x12, 0x66, 0x0a, 0x20, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x77, 0x61,
	0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x1c, 0x72, 0x65,
	0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x54, 0x6f, 0x46, 0x69, 0x72,
	0x73, 0x74, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x5f, 0x0a, 0x1b, 0x72, 0x65,
	0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03,
	0x52, 0x19, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x79, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x6a, 0x0a, 0x21, 0x72,
	0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x73, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x1e, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69,
	0x6e, 0x67, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x6f, 0x75, 0x74, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x14, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x73, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41, 0x05, 0x52, 0x12, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x4f, 0x66, 0x50, 0x61, 0x73, 0x73, 0x65, 0x6e, 0x67, 0x65, 0x72, 0x73, 0x12, 0x4e,
	0x0a, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x03, 0xe0, 0x41, 0x03,
	0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b,
	0x0a, 0x17, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x6e, 0x61, 0x70, 0x70, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x08, 0x42,
	0x03, 0xe0, 0x41, 0x03, 0x52, 0x15, 0x6c, 0x61, 0x73, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x6e, 0x61, 0x70, 0x70, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x31, 0x0a, 0x04, 0x76,
	0x69, 0x65, 0x77, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6d, 0x61, 0x70, 0x73,
	0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x72, 0x69, 0x70, 0x56, 0x69, 0x65, 0x77, 0x52, 0x04, 0x76, 0x69, 0x65, 0x77, 0x3a, 0x47,
	0xea, 0x41, 0x44, 0x0a, 0x1f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x54, 0x72, 0x69, 0x70, 0x12, 0x21, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x2f,
	0x7b, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x7d, 0x2f, 0x74, 0x72, 0x69, 0x70, 0x73,
	0x2f, 0x7b, 0x74, 0x72, 0x69, 0x70, 0x7d, 0x22, 0xb8, 0x01, 0x0a, 0x0c, 0x53, 0x74, 0x6f, 0x70,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x05, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x05, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x3e, 0x0a, 0x09, 0x73, 0x74, 0x6f, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x42, 0x05, 0x18, 0x01, 0xe0, 0x41, 0x04, 0x52, 0x08, 0x73, 0x74, 0x6f, 0x70, 0x54, 0x69,
	0x6d, 0x65, 0x2a, 0xe2, 0x01, 0x0a, 0x0a, 0x54, 0x72, 0x69, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x17, 0x0a, 0x13, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x54, 0x52, 0x49,
	0x50, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x4e, 0x45,
	0x57, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x45, 0x4e, 0x52, 0x4f, 0x55, 0x54, 0x45, 0x5f, 0x54,
	0x4f, 0x5f, 0x50, 0x49, 0x43, 0x4b, 0x55, 0x50, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x52,
	0x52, 0x49, 0x56, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x5f, 0x50, 0x49, 0x43, 0x4b, 0x55, 0x50, 0x10,
	0x03, 0x12, 0x27, 0x0a, 0x23, 0x41, 0x52, 0x52, 0x49, 0x56, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x5f,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x4d, 0x45, 0x44, 0x49, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x53,
	0x54, 0x49, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x07, 0x12, 0x27, 0x0a, 0x23, 0x45, 0x4e,
	0x52, 0x4f, 0x55, 0x54, 0x45, 0x5f, 0x54, 0x4f, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4d, 0x45,
	0x44, 0x49, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x53, 0x54, 0x49, 0x4e, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x08, 0x12, 0x16, 0x0a, 0x12, 0x45, 0x4e, 0x52, 0x4f, 0x55, 0x54, 0x45, 0x5f, 0x54,
	0x4f, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x4f, 0x46, 0x46, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x43,
	0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x41, 0x4e,
	0x43, 0x45, 0x4c, 0x45, 0x44, 0x10, 0x06, 0x2a, 0x7f, 0x0a, 0x19, 0x42, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x27, 0x42, 0x49, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x5f,
	0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46,
	0x49, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x10, 0x01, 0x12, 0x07, 0x0a,
	0x03, 0x57, 0x45, 0x42, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49,
	0x44, 0x10, 0x03, 0x12, 0x07, 0x0a, 0x03, 0x49, 0x4f, 0x53, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06,
	0x4f, 0x54, 0x48, 0x45, 0x52, 0x53, 0x10, 0x05, 0x2a, 0x47, 0x0a, 0x08, 0x54, 0x72, 0x69, 0x70,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x56, 0x49, 0x45,
	0x57, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x07, 0x0a, 0x03, 0x53, 0x44, 0x4b, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x4a, 0x4f, 0x55, 0x52,
	0x4e, 0x45, 0x59, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x56, 0x31, 0x53, 0x10,
	0x02, 0x42, 0x72, 0x0a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73,
	0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x42,
	0x05, 0x54, 0x72, 0x69, 0x70, 0x73, 0x50, 0x01, 0x5a, 0x45, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x6d, 0x61, 0x70, 0x73, 0x2f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x2f, 0x76, 0x31, 0x3b, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0xa2,
	0x02, 0x03, 0x43, 0x46, 0x45, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_maps_fleetengine_v1_trips_proto_rawDescOnce sync.Once
	file_google_maps_fleetengine_v1_trips_proto_rawDescData = file_google_maps_fleetengine_v1_trips_proto_rawDesc
)

func file_google_maps_fleetengine_v1_trips_proto_rawDescGZIP() []byte {
	file_google_maps_fleetengine_v1_trips_proto_rawDescOnce.Do(func() {
		file_google_maps_fleetengine_v1_trips_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_maps_fleetengine_v1_trips_proto_rawDescData)
	})
	return file_google_maps_fleetengine_v1_trips_proto_rawDescData
}

var file_google_maps_fleetengine_v1_trips_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_google_maps_fleetengine_v1_trips_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_google_maps_fleetengine_v1_trips_proto_goTypes = []interface{}{
	(TripStatus)(0),                   // 0: maps.fleetengine.v1.TripStatus
	(BillingPlatformIdentifier)(0),    // 1: maps.fleetengine.v1.BillingPlatformIdentifier
	(TripView)(0),                     // 2: maps.fleetengine.v1.TripView
	(*Trip)(nil),                      // 3: maps.fleetengine.v1.Trip
	(*StopLocation)(nil),              // 4: maps.fleetengine.v1.StopLocation
	(TripType)(0),                     // 5: maps.fleetengine.v1.TripType
	(*TerminalLocation)(nil),          // 6: maps.fleetengine.v1.TerminalLocation
	(*timestamppb.Timestamp)(nil),     // 7: google.protobuf.Timestamp
	(*TripWaypoint)(nil),              // 8: maps.fleetengine.v1.TripWaypoint
	(*latlng.LatLng)(nil),             // 9: google.type.LatLng
	(*ConsumableTrafficPolyline)(nil), // 10: maps.fleetengine.v1.ConsumableTrafficPolyline
	(*wrapperspb.Int32Value)(nil),     // 11: google.protobuf.Int32Value
	(*durationpb.Duration)(nil),       // 12: google.protobuf.Duration
	(*VehicleLocation)(nil),           // 13: maps.fleetengine.v1.VehicleLocation
}
var file_google_maps_fleetengine_v1_trips_proto_depIdxs = []int32{
	0,  // 0: maps.fleetengine.v1.Trip.trip_status:type_name -> maps.fleetengine.v1.TripStatus
	5,  // 1: maps.fleetengine.v1.Trip.trip_type:type_name -> maps.fleetengine.v1.TripType
	6,  // 2: maps.fleetengine.v1.Trip.pickup_point:type_name -> maps.fleetengine.v1.TerminalLocation
	4,  // 3: maps.fleetengine.v1.Trip.actual_pickup_point:type_name -> maps.fleetengine.v1.StopLocation
	4,  // 4: maps.fleetengine.v1.Trip.actual_pickup_arrival_point:type_name -> maps.fleetengine.v1.StopLocation
	7,  // 5: maps.fleetengine.v1.Trip.pickup_time:type_name -> google.protobuf.Timestamp
	6,  // 6: maps.fleetengine.v1.Trip.intermediate_destinations:type_name -> maps.fleetengine.v1.TerminalLocation
	7,  // 7: maps.fleetengine.v1.Trip.intermediate_destinations_version:type_name -> google.protobuf.Timestamp
	4,  // 8: maps.fleetengine.v1.Trip.actual_intermediate_destination_arrival_points:type_name -> maps.fleetengine.v1.StopLocation
	4,  // 9: maps.fleetengine.v1.Trip.actual_intermediate_destinations:type_name -> maps.fleetengine.v1.StopLocation
	6,  // 10: maps.fleetengine.v1.Trip.dropoff_point:type_name -> maps.fleetengine.v1.TerminalLocation
	4,  // 11: maps.fleetengine.v1.Trip.actual_dropoff_point:type_name -> maps.fleetengine.v1.StopLocation
	7,  // 12: maps.fleetengine.v1.Trip.dropoff_time:type_name -> google.protobuf.Timestamp
	8,  // 13: maps.fleetengine.v1.Trip.remaining_waypoints:type_name -> maps.fleetengine.v1.TripWaypoint
	8,  // 14: maps.fleetengine.v1.Trip.vehicle_waypoints:type_name -> maps.fleetengine.v1.TripWaypoint
	9,  // 15: maps.fleetengine.v1.Trip.route:type_name -> google.type.LatLng
	7,  // 16: maps.fleetengine.v1.Trip.current_route_segment_version:type_name -> google.protobuf.Timestamp
	10, // 17: maps.fleetengine.v1.Trip.current_route_segment_traffic:type_name -> maps.fleetengine.v1.ConsumableTrafficPolyline
	7,  // 18: maps.fleetengine.v1.Trip.current_route_segment_traffic_version:type_name -> google.protobuf.Timestamp
	8,  // 19: maps.fleetengine.v1.Trip.current_route_segment_end_point:type_name -> maps.fleetengine.v1.TripWaypoint
	11, // 20: maps.fleetengine.v1.Trip.remaining_distance_meters:type_name -> google.protobuf.Int32Value
	7,  // 21: maps.fleetengine.v1.Trip.eta_to_first_waypoint:type_name -> google.protobuf.Timestamp
	12, // 22: maps.fleetengine.v1.Trip.remaining_time_to_first_waypoint:type_name -> google.protobuf.Duration
	7,  // 23: maps.fleetengine.v1.Trip.remaining_waypoints_version:type_name -> google.protobuf.Timestamp
	7,  // 24: maps.fleetengine.v1.Trip.remaining_waypoints_route_version:type_name -> google.protobuf.Timestamp
	13, // 25: maps.fleetengine.v1.Trip.last_location:type_name -> maps.fleetengine.v1.VehicleLocation
	2,  // 26: maps.fleetengine.v1.Trip.view:type_name -> maps.fleetengine.v1.TripView
	9,  // 27: maps.fleetengine.v1.StopLocation.point:type_name -> google.type.LatLng
	7,  // 28: maps.fleetengine.v1.StopLocation.timestamp:type_name -> google.protobuf.Timestamp
	7,  // 29: maps.fleetengine.v1.StopLocation.stop_time:type_name -> google.protobuf.Timestamp
	30, // [30:30] is the sub-list for method output_type
	30, // [30:30] is the sub-list for method input_type
	30, // [30:30] is the sub-list for extension type_name
	30, // [30:30] is the sub-list for extension extendee
	0,  // [0:30] is the sub-list for field type_name
}

func init() { file_google_maps_fleetengine_v1_trips_proto_init() }
func file_google_maps_fleetengine_v1_trips_proto_init() {
	if File_google_maps_fleetengine_v1_trips_proto != nil {
		return
	}
	file_google_maps_fleetengine_v1_fleetengine_proto_init()
	file_google_maps_fleetengine_v1_traffic_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_maps_fleetengine_v1_trips_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Trip); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_trips_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_maps_fleetengine_v1_trips_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_maps_fleetengine_v1_trips_proto_goTypes,
		DependencyIndexes: file_google_maps_fleetengine_v1_trips_proto_depIdxs,
		EnumInfos:         file_google_maps_fleetengine_v1_trips_proto_enumTypes,
		MessageInfos:      file_google_maps_fleetengine_v1_trips_proto_msgTypes,
	}.Build()
	File_google_maps_fleetengine_v1_trips_proto = out.File
	file_google_maps_fleetengine_v1_trips_proto_rawDesc = nil
	file_google_maps_fleetengine_v1_trips_proto_goTypes = nil
	file_google_maps_fleetengine_v1_trips_proto_depIdxs = nil
}
