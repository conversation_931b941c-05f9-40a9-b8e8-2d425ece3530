// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.21.9
// source: google/maps/fleetengine/delivery/v1/common.proto

package delivery

import (
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The sensor or methodology used to determine the location.
type DeliveryVehicleLocationSensor int32

const (
	// The sensor is unspecified or unknown.
	DeliveryVehicleLocationSensor_UNKNOWN_SENSOR DeliveryVehicleLocationSensor = 0
	// GPS or Assisted GPS.
	DeliveryVehicleLocationSensor_GPS DeliveryVehicleLocationSensor = 1
	// Assisted GPS, cell tower ID, or WiFi access point.
	DeliveryVehicleLocationSensor_NETWORK DeliveryVehicleLocationSensor = 2
	// Cell tower ID or WiFi access point.
	DeliveryVehicleLocationSensor_PASSIVE DeliveryVehicleLocationSensor = 3
	// A location signal snapped to the best road position.
	DeliveryVehicleLocationSensor_ROAD_SNAPPED_LOCATION_PROVIDER DeliveryVehicleLocationSensor = 4
	// The fused location provider in Google Play services.
	DeliveryVehicleLocationSensor_FUSED_LOCATION_PROVIDER DeliveryVehicleLocationSensor = 100
	// The location provider on Apple operating systems.
	DeliveryVehicleLocationSensor_CORE_LOCATION DeliveryVehicleLocationSensor = 200
)

// Enum value maps for DeliveryVehicleLocationSensor.
var (
	DeliveryVehicleLocationSensor_name = map[int32]string{
		0:   "UNKNOWN_SENSOR",
		1:   "GPS",
		2:   "NETWORK",
		3:   "PASSIVE",
		4:   "ROAD_SNAPPED_LOCATION_PROVIDER",
		100: "FUSED_LOCATION_PROVIDER",
		200: "CORE_LOCATION",
	}
	DeliveryVehicleLocationSensor_value = map[string]int32{
		"UNKNOWN_SENSOR":                 0,
		"GPS":                            1,
		"NETWORK":                        2,
		"PASSIVE":                        3,
		"ROAD_SNAPPED_LOCATION_PROVIDER": 4,
		"FUSED_LOCATION_PROVIDER":        100,
		"CORE_LOCATION":                  200,
	}
)

func (x DeliveryVehicleLocationSensor) Enum() *DeliveryVehicleLocationSensor {
	p := new(DeliveryVehicleLocationSensor)
	*p = x
	return p
}

func (x DeliveryVehicleLocationSensor) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeliveryVehicleLocationSensor) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_delivery_v1_common_proto_enumTypes[0].Descriptor()
}

func (DeliveryVehicleLocationSensor) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_delivery_v1_common_proto_enumTypes[0]
}

func (x DeliveryVehicleLocationSensor) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeliveryVehicleLocationSensor.Descriptor instead.
func (DeliveryVehicleLocationSensor) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_delivery_v1_common_proto_rawDescGZIP(), []int{0}
}

// The vehicle's navigation status.
type DeliveryVehicleNavigationStatus int32

const (
	// Unspecified navigation status.
	DeliveryVehicleNavigationStatus_UNKNOWN_NAVIGATION_STATUS DeliveryVehicleNavigationStatus = 0
	// The Driver app's navigation is in `FREE_NAV` mode.
	DeliveryVehicleNavigationStatus_NO_GUIDANCE DeliveryVehicleNavigationStatus = 1
	// Turn-by-turn navigation is available and the Driver app navigation has
	// entered `GUIDED_NAV` mode.
	DeliveryVehicleNavigationStatus_ENROUTE_TO_DESTINATION DeliveryVehicleNavigationStatus = 2
	// The vehicle has gone off the suggested route.
	DeliveryVehicleNavigationStatus_OFF_ROUTE DeliveryVehicleNavigationStatus = 3
	// The vehicle is within approximately 50m of the destination.
	DeliveryVehicleNavigationStatus_ARRIVED_AT_DESTINATION DeliveryVehicleNavigationStatus = 4
)

// Enum value maps for DeliveryVehicleNavigationStatus.
var (
	DeliveryVehicleNavigationStatus_name = map[int32]string{
		0: "UNKNOWN_NAVIGATION_STATUS",
		1: "NO_GUIDANCE",
		2: "ENROUTE_TO_DESTINATION",
		3: "OFF_ROUTE",
		4: "ARRIVED_AT_DESTINATION",
	}
	DeliveryVehicleNavigationStatus_value = map[string]int32{
		"UNKNOWN_NAVIGATION_STATUS": 0,
		"NO_GUIDANCE":               1,
		"ENROUTE_TO_DESTINATION":    2,
		"OFF_ROUTE":                 3,
		"ARRIVED_AT_DESTINATION":    4,
	}
)

func (x DeliveryVehicleNavigationStatus) Enum() *DeliveryVehicleNavigationStatus {
	p := new(DeliveryVehicleNavigationStatus)
	*p = x
	return p
}

func (x DeliveryVehicleNavigationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeliveryVehicleNavigationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_delivery_v1_common_proto_enumTypes[1].Descriptor()
}

func (DeliveryVehicleNavigationStatus) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_delivery_v1_common_proto_enumTypes[1]
}

func (x DeliveryVehicleNavigationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeliveryVehicleNavigationStatus.Descriptor instead.
func (DeliveryVehicleNavigationStatus) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_delivery_v1_common_proto_rawDescGZIP(), []int{1}
}

// Describes a vehicle attribute as a key-value pair. The "key:value" string
// length cannot exceed 256 characters.
type DeliveryVehicleAttribute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The attribute's key. Keys may not contain the colon character (:).
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// The attribute's value.
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *DeliveryVehicleAttribute) Reset() {
	*x = DeliveryVehicleAttribute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_delivery_v1_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliveryVehicleAttribute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliveryVehicleAttribute) ProtoMessage() {}

func (x *DeliveryVehicleAttribute) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_delivery_v1_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliveryVehicleAttribute.ProtoReflect.Descriptor instead.
func (*DeliveryVehicleAttribute) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_delivery_v1_common_proto_rawDescGZIP(), []int{0}
}

func (x *DeliveryVehicleAttribute) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *DeliveryVehicleAttribute) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// The location, speed, and heading of a vehicle at a point in time.
type DeliveryVehicleLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The location of the vehicle.
	// When it is sent to Fleet Engine, the vehicle's location is a GPS location.
	// When you receive it in a response, the vehicle's location can be either a
	// GPS location, a supplemental location, or some other estimated location.
	// The source is specified in `location_sensor`.
	Location *latlng.LatLng `protobuf:"bytes,1,opt,name=location,proto3" json:"location,omitempty"`
	// Deprecated: Use `latlng_accuracy` instead.
	//
	// Deprecated: Do not use.
	HorizontalAccuracy *wrapperspb.DoubleValue `protobuf:"bytes,8,opt,name=horizontal_accuracy,json=horizontalAccuracy,proto3" json:"horizontal_accuracy,omitempty"`
	// Accuracy of `location` in meters as a radius.
	LatlngAccuracy *wrapperspb.DoubleValue `protobuf:"bytes,22,opt,name=latlng_accuracy,json=latlngAccuracy,proto3" json:"latlng_accuracy,omitempty"`
	// Direction the vehicle is moving in degrees.  0 represents North.
	// The valid range is [0,360).
	Heading *wrapperspb.Int32Value `protobuf:"bytes,2,opt,name=heading,proto3" json:"heading,omitempty"`
	// Deprecated: Use `heading_accuracy` instead.
	//
	// Deprecated: Do not use.
	BearingAccuracy *wrapperspb.DoubleValue `protobuf:"bytes,10,opt,name=bearing_accuracy,json=bearingAccuracy,proto3" json:"bearing_accuracy,omitempty"`
	// Accuracy of `heading` in degrees.
	HeadingAccuracy *wrapperspb.DoubleValue `protobuf:"bytes,23,opt,name=heading_accuracy,json=headingAccuracy,proto3" json:"heading_accuracy,omitempty"`
	// Altitude in meters above WGS84.
	Altitude *wrapperspb.DoubleValue `protobuf:"bytes,5,opt,name=altitude,proto3" json:"altitude,omitempty"`
	// Deprecated: Use `altitude_accuracy` instead.
	//
	// Deprecated: Do not use.
	VerticalAccuracy *wrapperspb.DoubleValue `protobuf:"bytes,9,opt,name=vertical_accuracy,json=verticalAccuracy,proto3" json:"vertical_accuracy,omitempty"`
	// Accuracy of `altitude` in meters.
	AltitudeAccuracy *wrapperspb.DoubleValue `protobuf:"bytes,24,opt,name=altitude_accuracy,json=altitudeAccuracy,proto3" json:"altitude_accuracy,omitempty"`
	// Speed of the vehicle in kilometers per hour.
	// Deprecated: Use `speed` instead.
	//
	// Deprecated: Do not use.
	SpeedKmph *wrapperspb.Int32Value `protobuf:"bytes,3,opt,name=speed_kmph,json=speedKmph,proto3" json:"speed_kmph,omitempty"`
	// Speed of the vehicle in meters/second
	Speed *wrapperspb.DoubleValue `protobuf:"bytes,6,opt,name=speed,proto3" json:"speed,omitempty"`
	// Accuracy of `speed` in meters/second.
	SpeedAccuracy *wrapperspb.DoubleValue `protobuf:"bytes,7,opt,name=speed_accuracy,json=speedAccuracy,proto3" json:"speed_accuracy,omitempty"`
	// The time when `location` was reported by the sensor according to the
	// sensor's clock.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// Output only. The time when the server received the location information.
	ServerTime *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	// Provider of location data (for example, `GPS`).
	LocationSensor DeliveryVehicleLocationSensor `protobuf:"varint,11,opt,name=location_sensor,json=locationSensor,proto3,enum=maps.fleetengine.delivery.v1.DeliveryVehicleLocationSensor" json:"location_sensor,omitempty"`
	// Whether `location` is snapped to a road.
	IsRoadSnapped *wrapperspb.BoolValue `protobuf:"bytes,27,opt,name=is_road_snapped,json=isRoadSnapped,proto3" json:"is_road_snapped,omitempty"`
	// Input only. Indicates whether the GPS sensor is enabled on the mobile
	// device.
	IsGpsSensorEnabled *wrapperspb.BoolValue `protobuf:"bytes,12,opt,name=is_gps_sensor_enabled,json=isGpsSensorEnabled,proto3" json:"is_gps_sensor_enabled,omitempty"`
	// Input only. Time (in seconds) since this location was first sent to the
	// server. This will be zero for the first update. If the time is unknown (for
	// example, when the app restarts), this value resets to zero.
	TimeSinceUpdate *wrapperspb.Int32Value `protobuf:"bytes,14,opt,name=time_since_update,json=timeSinceUpdate,proto3" json:"time_since_update,omitempty"`
	// Input only. Number of additional attempts to send this location to the
	// server. If this value is zero, then it is not stale.
	NumStaleUpdates *wrapperspb.Int32Value `protobuf:"bytes,15,opt,name=num_stale_updates,json=numStaleUpdates,proto3" json:"num_stale_updates,omitempty"`
	// Raw vehicle location (unprocessed by road-snapper).
	RawLocation *latlng.LatLng `protobuf:"bytes,16,opt,name=raw_location,json=rawLocation,proto3" json:"raw_location,omitempty"`
	// Input only. Timestamp associated with the raw location.
	RawLocationTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=raw_location_time,json=rawLocationTime,proto3" json:"raw_location_time,omitempty"`
	// Input only. Source of the raw location.
	RawLocationSensor DeliveryVehicleLocationSensor `protobuf:"varint,28,opt,name=raw_location_sensor,json=rawLocationSensor,proto3,enum=maps.fleetengine.delivery.v1.DeliveryVehicleLocationSensor" json:"raw_location_sensor,omitempty"`
	// Input only. Accuracy of `raw_location` as a radius, in meters.
	RawLocationAccuracy *wrapperspb.DoubleValue `protobuf:"bytes,25,opt,name=raw_location_accuracy,json=rawLocationAccuracy,proto3" json:"raw_location_accuracy,omitempty"`
	// Input only. Supplemental location provided by the integrating app.
	SupplementalLocation *latlng.LatLng `protobuf:"bytes,18,opt,name=supplemental_location,json=supplementalLocation,proto3" json:"supplemental_location,omitempty"`
	// Input only. Timestamp associated with the supplemental location.
	SupplementalLocationTime *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=supplemental_location_time,json=supplementalLocationTime,proto3" json:"supplemental_location_time,omitempty"`
	// Input only. Source of the supplemental location.
	SupplementalLocationSensor DeliveryVehicleLocationSensor `protobuf:"varint,20,opt,name=supplemental_location_sensor,json=supplementalLocationSensor,proto3,enum=maps.fleetengine.delivery.v1.DeliveryVehicleLocationSensor" json:"supplemental_location_sensor,omitempty"`
	// Input only. Accuracy of `supplemental_location` as a radius, in meters.
	SupplementalLocationAccuracy *wrapperspb.DoubleValue `protobuf:"bytes,21,opt,name=supplemental_location_accuracy,json=supplementalLocationAccuracy,proto3" json:"supplemental_location_accuracy,omitempty"`
	// Deprecated: Use `is_road_snapped` instead.
	//
	// Deprecated: Do not use.
	RoadSnapped bool `protobuf:"varint,26,opt,name=road_snapped,json=roadSnapped,proto3" json:"road_snapped,omitempty"`
}

func (x *DeliveryVehicleLocation) Reset() {
	*x = DeliveryVehicleLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_delivery_v1_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeliveryVehicleLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeliveryVehicleLocation) ProtoMessage() {}

func (x *DeliveryVehicleLocation) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_delivery_v1_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeliveryVehicleLocation.ProtoReflect.Descriptor instead.
func (*DeliveryVehicleLocation) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_delivery_v1_common_proto_rawDescGZIP(), []int{1}
}

func (x *DeliveryVehicleLocation) GetLocation() *latlng.LatLng {
	if x != nil {
		return x.Location
	}
	return nil
}

// Deprecated: Do not use.
func (x *DeliveryVehicleLocation) GetHorizontalAccuracy() *wrapperspb.DoubleValue {
	if x != nil {
		return x.HorizontalAccuracy
	}
	return nil
}

func (x *DeliveryVehicleLocation) GetLatlngAccuracy() *wrapperspb.DoubleValue {
	if x != nil {
		return x.LatlngAccuracy
	}
	return nil
}

func (x *DeliveryVehicleLocation) GetHeading() *wrapperspb.Int32Value {
	if x != nil {
		return x.Heading
	}
	return nil
}

// Deprecated: Do not use.
func (x *DeliveryVehicleLocation) GetBearingAccuracy() *wrapperspb.DoubleValue {
	if x != nil {
		return x.BearingAccuracy
	}
	return nil
}

func (x *DeliveryVehicleLocation) GetHeadingAccuracy() *wrapperspb.DoubleValue {
	if x != nil {
		return x.HeadingAccuracy
	}
	return nil
}

func (x *DeliveryVehicleLocation) GetAltitude() *wrapperspb.DoubleValue {
	if x != nil {
		return x.Altitude
	}
	return nil
}

// Deprecated: Do not use.
func (x *DeliveryVehicleLocation) GetVerticalAccuracy() *wrapperspb.DoubleValue {
	if x != nil {
		return x.VerticalAccuracy
	}
	return nil
}

func (x *DeliveryVehicleLocation) GetAltitudeAccuracy() *wrapperspb.DoubleValue {
	if x != nil {
		return x.AltitudeAccuracy
	}
	return nil
}

// Deprecated: Do not use.
func (x *DeliveryVehicleLocation) GetSpeedKmph() *wrapperspb.Int32Value {
	if x != nil {
		return x.SpeedKmph
	}
	return nil
}

func (x *DeliveryVehicleLocation) GetSpeed() *wrapperspb.DoubleValue {
	if x != nil {
		return x.Speed
	}
	return nil
}

func (x *DeliveryVehicleLocation) GetSpeedAccuracy() *wrapperspb.DoubleValue {
	if x != nil {
		return x.SpeedAccuracy
	}
	return nil
}

func (x *DeliveryVehicleLocation) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *DeliveryVehicleLocation) GetServerTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ServerTime
	}
	return nil
}

func (x *DeliveryVehicleLocation) GetLocationSensor() DeliveryVehicleLocationSensor {
	if x != nil {
		return x.LocationSensor
	}
	return DeliveryVehicleLocationSensor_UNKNOWN_SENSOR
}

func (x *DeliveryVehicleLocation) GetIsRoadSnapped() *wrapperspb.BoolValue {
	if x != nil {
		return x.IsRoadSnapped
	}
	return nil
}

func (x *DeliveryVehicleLocation) GetIsGpsSensorEnabled() *wrapperspb.BoolValue {
	if x != nil {
		return x.IsGpsSensorEnabled
	}
	return nil
}

func (x *DeliveryVehicleLocation) GetTimeSinceUpdate() *wrapperspb.Int32Value {
	if x != nil {
		return x.TimeSinceUpdate
	}
	return nil
}

func (x *DeliveryVehicleLocation) GetNumStaleUpdates() *wrapperspb.Int32Value {
	if x != nil {
		return x.NumStaleUpdates
	}
	return nil
}

func (x *DeliveryVehicleLocation) GetRawLocation() *latlng.LatLng {
	if x != nil {
		return x.RawLocation
	}
	return nil
}

func (x *DeliveryVehicleLocation) GetRawLocationTime() *timestamppb.Timestamp {
	if x != nil {
		return x.RawLocationTime
	}
	return nil
}

func (x *DeliveryVehicleLocation) GetRawLocationSensor() DeliveryVehicleLocationSensor {
	if x != nil {
		return x.RawLocationSensor
	}
	return DeliveryVehicleLocationSensor_UNKNOWN_SENSOR
}

func (x *DeliveryVehicleLocation) GetRawLocationAccuracy() *wrapperspb.DoubleValue {
	if x != nil {
		return x.RawLocationAccuracy
	}
	return nil
}

func (x *DeliveryVehicleLocation) GetSupplementalLocation() *latlng.LatLng {
	if x != nil {
		return x.SupplementalLocation
	}
	return nil
}

func (x *DeliveryVehicleLocation) GetSupplementalLocationTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SupplementalLocationTime
	}
	return nil
}

func (x *DeliveryVehicleLocation) GetSupplementalLocationSensor() DeliveryVehicleLocationSensor {
	if x != nil {
		return x.SupplementalLocationSensor
	}
	return DeliveryVehicleLocationSensor_UNKNOWN_SENSOR
}

func (x *DeliveryVehicleLocation) GetSupplementalLocationAccuracy() *wrapperspb.DoubleValue {
	if x != nil {
		return x.SupplementalLocationAccuracy
	}
	return nil
}

// Deprecated: Do not use.
func (x *DeliveryVehicleLocation) GetRoadSnapped() bool {
	if x != nil {
		return x.RoadSnapped
	}
	return false
}

// A time range.
type TimeWindow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The start time of the time window (inclusive).
	StartTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// Required. The end time of the time window (inclusive).
	EndTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (x *TimeWindow) Reset() {
	*x = TimeWindow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_delivery_v1_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeWindow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeWindow) ProtoMessage() {}

func (x *TimeWindow) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_delivery_v1_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeWindow.ProtoReflect.Descriptor instead.
func (*TimeWindow) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_delivery_v1_common_proto_rawDescGZIP(), []int{2}
}

func (x *TimeWindow) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *TimeWindow) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

var File_google_maps_fleetengine_delivery_v1_common_proto protoreflect.FileDescriptor

var file_google_maps_fleetengine_delivery_v1_common_proto_rawDesc = []byte{
	0x0a, 0x30, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x76, 0x31,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f,
	0x6c, 0x61, 0x74, 0x6c, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x42, 0x0a, 0x18,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x22, 0xe8, 0x10, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x08,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74,
	0x4c, 0x6e, 0x67, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x51, 0x0a,
	0x13, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x63, 0x63, 0x75,
	0x72, 0x61, 0x63, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75,
	0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x12, 0x68, 0x6f,
	0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79,
	0x12, 0x45, 0x0a, 0x0f, 0x6c, 0x61, 0x74, 0x6c, 0x6e, 0x67, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x72,
	0x61, 0x63, 0x79, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0e, 0x6c, 0x61, 0x74, 0x6c, 0x6e, 0x67, 0x41,
	0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x12, 0x35, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x69,
	0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x4b,
	0x0a, 0x10, 0x62, 0x65, 0x61, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61,
	0x63, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0f, 0x62, 0x65, 0x61, 0x72,
	0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x12, 0x47, 0x0a, 0x10, 0x68,
	0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x0f, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x75,
	0x72, 0x61, 0x63, 0x79, 0x12, 0x38, 0x0a, 0x08, 0x61, 0x6c, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x61, 0x6c, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x4d,
	0x0a, 0x11, 0x76, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x72,
	0x61, 0x63, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x10, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x63, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x12, 0x49, 0x0a,
	0x11, 0x61, 0x6c, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61,
	0x63, 0x79, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c,
	0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x10, 0x61, 0x6c, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65,
	0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x12, 0x3e, 0x0a, 0x0a, 0x73, 0x70, 0x65, 0x65,
	0x64, 0x5f, 0x6b, 0x6d, 0x70, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x73,
	0x70, 0x65, 0x65, 0x64, 0x4b, 0x6d, 0x70, 0x68, 0x12, 0x32, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x65,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x12, 0x43, 0x0a, 0x0e,
	0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x65, 0x64, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63,
	0x79, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x40,
	0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42,
	0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x64, 0x0a, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x6d, 0x61, 0x70, 0x73,
	0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x64, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x0e, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x42, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x72, 0x6f, 0x61,
	0x64, 0x5f, 0x73, 0x6e, 0x61, 0x70, 0x70, 0x65, 0x64, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x69, 0x73, 0x52,
	0x6f, 0x61, 0x64, 0x53, 0x6e, 0x61, 0x70, 0x70, 0x65, 0x64, 0x12, 0x52, 0x0a, 0x15, 0x69, 0x73,
	0x5f, 0x67, 0x70, 0x73, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x12, 0x69, 0x73, 0x47, 0x70,
	0x73, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x4c,
	0x0a, 0x11, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x0f, 0x74, 0x69, 0x6d,
	0x65, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x4c, 0x0a, 0x11,
	0x6e, 0x75, 0x6d, 0x5f, 0x73, 0x74, 0x61, 0x6c, 0x65, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x0f, 0x6e, 0x75, 0x6d, 0x53, 0x74,
	0x61, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x12, 0x36, 0x0a, 0x0c, 0x72, 0x61,
	0x77, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c,
	0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x0b, 0x72, 0x61, 0x77, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x4b, 0x0a, 0x11, 0x72, 0x61, 0x77, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x0f,
	0x72, 0x61, 0x77, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x70, 0x0a, 0x13, 0x72, 0x61, 0x77, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x6d,
	0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e,
	0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x11,
	0x72, 0x61, 0x77, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x12, 0x55, 0x0a, 0x15, 0x72, 0x61, 0x77, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x03,
	0xe0, 0x41, 0x04, 0x52, 0x13, 0x72, 0x61, 0x77, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x12, 0x4d, 0x0a, 0x15, 0x73, 0x75, 0x70, 0x70,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x42, 0x03, 0xe0, 0x41,
	0x04, 0x52, 0x14, 0x73, 0x75, 0x70, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5d, 0x0a, 0x1a, 0x73, 0x75, 0x70, 0x70, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x18, 0x73, 0x75,
	0x70, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x1c, 0x73, 0x75, 0x70, 0x70, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e,
	0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x2e, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52,
	0x1a, 0x73, 0x75, 0x70, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x67, 0x0a, 0x1e, 0x73,
	0x75, 0x70, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x1c, 0x73, 0x75, 0x70, 0x70, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x63, 0x75,
	0x72, 0x61, 0x63, 0x79, 0x12, 0x25, 0x0a, 0x0c, 0x72, 0x6f, 0x61, 0x64, 0x5f, 0x73, 0x6e, 0x61,
	0x70, 0x70, 0x65, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0b,
	0x72, 0x6f, 0x61, 0x64, 0x53, 0x6e, 0x61, 0x70, 0x70, 0x65, 0x64, 0x22, 0x88, 0x01, 0x0a, 0x0a,
	0x54, 0x69, 0x6d, 0x65, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x3e, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x2a, 0xab, 0x01, 0x0a, 0x1d, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x0e, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x5f, 0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03,
	0x47, 0x50, 0x53, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b,
	0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x41, 0x53, 0x53, 0x49, 0x56, 0x45, 0x10, 0x03, 0x12,
	0x22, 0x0a, 0x1e, 0x52, 0x4f, 0x41, 0x44, 0x5f, 0x53, 0x4e, 0x41, 0x50, 0x50, 0x45, 0x44, 0x5f,
	0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45,
	0x52, 0x10, 0x04, 0x12, 0x1b, 0x0a, 0x17, 0x46, 0x55, 0x53, 0x45, 0x44, 0x5f, 0x4c, 0x4f, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x52, 0x10, 0x64,
	0x12, 0x12, 0x0a, 0x0d, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0xc8, 0x01, 0x2a, 0x98, 0x01, 0x0a, 0x1f, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x19, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x5f, 0x4e, 0x41, 0x56, 0x49, 0x47, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x4f, 0x5f, 0x47, 0x55,
	0x49, 0x44, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x45, 0x4e, 0x52, 0x4f,
	0x55, 0x54, 0x45, 0x5f, 0x54, 0x4f, 0x5f, 0x44, 0x45, 0x53, 0x54, 0x49, 0x4e, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x4f, 0x46, 0x46, 0x5f, 0x52, 0x4f, 0x55, 0x54,
	0x45, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x52, 0x52, 0x49, 0x56, 0x45, 0x44, 0x5f, 0x41,
	0x54, 0x5f, 0x44, 0x45, 0x53, 0x54, 0x49, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x42,
	0x83, 0x01, 0x0a, 0x23, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e,
	0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x2e, 0x76, 0x31, 0x42, 0x06, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x50,
	0x01, 0x5a, 0x4b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67,
	0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x2f, 0x76, 0x31, 0x3b, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0xa2, 0x02,
	0x04, 0x43, 0x46, 0x45, 0x44, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_maps_fleetengine_delivery_v1_common_proto_rawDescOnce sync.Once
	file_google_maps_fleetengine_delivery_v1_common_proto_rawDescData = file_google_maps_fleetengine_delivery_v1_common_proto_rawDesc
)

func file_google_maps_fleetengine_delivery_v1_common_proto_rawDescGZIP() []byte {
	file_google_maps_fleetengine_delivery_v1_common_proto_rawDescOnce.Do(func() {
		file_google_maps_fleetengine_delivery_v1_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_maps_fleetengine_delivery_v1_common_proto_rawDescData)
	})
	return file_google_maps_fleetengine_delivery_v1_common_proto_rawDescData
}

var file_google_maps_fleetengine_delivery_v1_common_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_google_maps_fleetengine_delivery_v1_common_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_google_maps_fleetengine_delivery_v1_common_proto_goTypes = []interface{}{
	(DeliveryVehicleLocationSensor)(0),   // 0: maps.fleetengine.delivery.v1.DeliveryVehicleLocationSensor
	(DeliveryVehicleNavigationStatus)(0), // 1: maps.fleetengine.delivery.v1.DeliveryVehicleNavigationStatus
	(*DeliveryVehicleAttribute)(nil),     // 2: maps.fleetengine.delivery.v1.DeliveryVehicleAttribute
	(*DeliveryVehicleLocation)(nil),      // 3: maps.fleetengine.delivery.v1.DeliveryVehicleLocation
	(*TimeWindow)(nil),                   // 4: maps.fleetengine.delivery.v1.TimeWindow
	(*latlng.LatLng)(nil),                // 5: google.type.LatLng
	(*wrapperspb.DoubleValue)(nil),       // 6: google.protobuf.DoubleValue
	(*wrapperspb.Int32Value)(nil),        // 7: google.protobuf.Int32Value
	(*timestamppb.Timestamp)(nil),        // 8: google.protobuf.Timestamp
	(*wrapperspb.BoolValue)(nil),         // 9: google.protobuf.BoolValue
}
var file_google_maps_fleetengine_delivery_v1_common_proto_depIdxs = []int32{
	5,  // 0: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.location:type_name -> google.type.LatLng
	6,  // 1: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.horizontal_accuracy:type_name -> google.protobuf.DoubleValue
	6,  // 2: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.latlng_accuracy:type_name -> google.protobuf.DoubleValue
	7,  // 3: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.heading:type_name -> google.protobuf.Int32Value
	6,  // 4: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.bearing_accuracy:type_name -> google.protobuf.DoubleValue
	6,  // 5: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.heading_accuracy:type_name -> google.protobuf.DoubleValue
	6,  // 6: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.altitude:type_name -> google.protobuf.DoubleValue
	6,  // 7: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.vertical_accuracy:type_name -> google.protobuf.DoubleValue
	6,  // 8: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.altitude_accuracy:type_name -> google.protobuf.DoubleValue
	7,  // 9: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.speed_kmph:type_name -> google.protobuf.Int32Value
	6,  // 10: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.speed:type_name -> google.protobuf.DoubleValue
	6,  // 11: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.speed_accuracy:type_name -> google.protobuf.DoubleValue
	8,  // 12: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.update_time:type_name -> google.protobuf.Timestamp
	8,  // 13: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.server_time:type_name -> google.protobuf.Timestamp
	0,  // 14: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.location_sensor:type_name -> maps.fleetengine.delivery.v1.DeliveryVehicleLocationSensor
	9,  // 15: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.is_road_snapped:type_name -> google.protobuf.BoolValue
	9,  // 16: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.is_gps_sensor_enabled:type_name -> google.protobuf.BoolValue
	7,  // 17: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.time_since_update:type_name -> google.protobuf.Int32Value
	7,  // 18: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.num_stale_updates:type_name -> google.protobuf.Int32Value
	5,  // 19: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.raw_location:type_name -> google.type.LatLng
	8,  // 20: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.raw_location_time:type_name -> google.protobuf.Timestamp
	0,  // 21: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.raw_location_sensor:type_name -> maps.fleetengine.delivery.v1.DeliveryVehicleLocationSensor
	6,  // 22: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.raw_location_accuracy:type_name -> google.protobuf.DoubleValue
	5,  // 23: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.supplemental_location:type_name -> google.type.LatLng
	8,  // 24: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.supplemental_location_time:type_name -> google.protobuf.Timestamp
	0,  // 25: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.supplemental_location_sensor:type_name -> maps.fleetengine.delivery.v1.DeliveryVehicleLocationSensor
	6,  // 26: maps.fleetengine.delivery.v1.DeliveryVehicleLocation.supplemental_location_accuracy:type_name -> google.protobuf.DoubleValue
	8,  // 27: maps.fleetengine.delivery.v1.TimeWindow.start_time:type_name -> google.protobuf.Timestamp
	8,  // 28: maps.fleetengine.delivery.v1.TimeWindow.end_time:type_name -> google.protobuf.Timestamp
	29, // [29:29] is the sub-list for method output_type
	29, // [29:29] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_google_maps_fleetengine_delivery_v1_common_proto_init() }
func file_google_maps_fleetengine_delivery_v1_common_proto_init() {
	if File_google_maps_fleetengine_delivery_v1_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_maps_fleetengine_delivery_v1_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliveryVehicleAttribute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_delivery_v1_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeliveryVehicleLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_delivery_v1_common_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeWindow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_maps_fleetengine_delivery_v1_common_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_maps_fleetengine_delivery_v1_common_proto_goTypes,
		DependencyIndexes: file_google_maps_fleetengine_delivery_v1_common_proto_depIdxs,
		EnumInfos:         file_google_maps_fleetengine_delivery_v1_common_proto_enumTypes,
		MessageInfos:      file_google_maps_fleetengine_delivery_v1_common_proto_msgTypes,
	}.Build()
	File_google_maps_fleetengine_delivery_v1_common_proto = out.File
	file_google_maps_fleetengine_delivery_v1_common_proto_rawDesc = nil
	file_google_maps_fleetengine_delivery_v1_common_proto_goTypes = nil
	file_google_maps_fleetengine_delivery_v1_common_proto_depIdxs = nil
}
