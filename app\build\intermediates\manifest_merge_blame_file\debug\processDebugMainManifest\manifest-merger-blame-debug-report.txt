1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.fuck.fuckinggooo"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
12-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:6:5-77
12-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:6:22-74
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
13-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:7:5-89
13-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:7:22-86
14    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
14-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:8:5-77
14-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:8:22-74
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
15-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:9:5-87
15-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:9:22-84
16    <uses-permission android:name="android.permission.CAMERA" />
16-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:10:5-65
16-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:10:22-62
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:11:5-80
17-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:11:22-77
18
19    <uses-feature
19-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:13:5-15:36
20        android:name="android.hardware.camera"
20-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:14:9-47
21        android:required="false" />
21-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:15:9-33
22    <uses-feature
22-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:16:5-18:36
23        android:name="android.hardware.camera.autofocus"
23-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:17:9-57
24        android:required="false" />
24-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:18:9-33
25
26    <permission
26-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\abd70e57a2ac52e1db17c7b5c85d4011\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
27        android:name="com.fuck.fuckinggooo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
27-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\abd70e57a2ac52e1db17c7b5c85d4011\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
28        android:protectionLevel="signature" />
28-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\abd70e57a2ac52e1db17c7b5c85d4011\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
29
30    <uses-permission android:name="com.fuck.fuckinggooo.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
30-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\abd70e57a2ac52e1db17c7b5c85d4011\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
30-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\abd70e57a2ac52e1db17c7b5c85d4011\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
31
32    <uses-feature
32-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\730544f0989b6ea6b7a76ddb9126f348\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
33        android:name="android.hardware.camera.front"
33-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\730544f0989b6ea6b7a76ddb9126f348\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
34        android:required="false" />
34-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\730544f0989b6ea6b7a76ddb9126f348\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
35    <uses-feature
35-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\730544f0989b6ea6b7a76ddb9126f348\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
36        android:name="android.hardware.camera.flash"
36-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\730544f0989b6ea6b7a76ddb9126f348\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
37        android:required="false" />
37-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\730544f0989b6ea6b7a76ddb9126f348\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
38    <uses-feature
38-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\730544f0989b6ea6b7a76ddb9126f348\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
39        android:name="android.hardware.screen.landscape"
39-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\730544f0989b6ea6b7a76ddb9126f348\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
40        android:required="false" />
40-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\730544f0989b6ea6b7a76ddb9126f348\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
41    <uses-feature
41-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\730544f0989b6ea6b7a76ddb9126f348\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
42        android:name="android.hardware.wifi"
42-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\730544f0989b6ea6b7a76ddb9126f348\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
43        android:required="false" />
43-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\730544f0989b6ea6b7a76ddb9126f348\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
44
45    <application
45-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:20:5-58:19
46        android:allowBackup="true"
46-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:21:9-35
47        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
47-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\abd70e57a2ac52e1db17c7b5c85d4011\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
48        android:dataExtractionRules="@xml/data_extraction_rules"
48-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:22:9-65
49        android:debuggable="true"
50        android:extractNativeLibs="false"
51        android:fullBackupContent="@xml/backup_rules"
51-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:23:9-54
52        android:icon="@mipmap/ic_launcher"
52-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:24:9-43
53        android:label="@string/app_name"
53-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:25:9-41
54        android:roundIcon="@mipmap/ic_launcher_round"
54-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:26:9-54
55        android:supportsRtl="true"
55-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:27:9-35
56        android:testOnly="true"
57        android:theme="@style/Theme.FuckingGooo" >
57-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:28:9-49
58        <activity
58-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:29:9-39:20
59            android:name="com.fuck.fuckinggooo.MainActivity"
59-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:30:13-41
60            android:exported="true"
60-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:31:13-36
61            android:label="@string/app_name"
61-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:32:13-45
62            android:theme="@style/Theme.FuckingGooo" >
62-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:33:13-53
63            <intent-filter>
63-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:34:13-38:29
64                <action android:name="android.intent.action.MAIN" />
64-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:35:17-69
64-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:35:25-66
65
66                <category android:name="android.intent.category.LAUNCHER" />
66-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:37:17-77
66-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:37:27-74
67            </intent-filter>
68        </activity>
69        <activity
69-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:41:9-44:56
70            android:name="com.fuck.fuckinggooo.ui.activity.QrScanActivity"
70-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:42:13-55
71            android:exported="false"
71-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:43:13-37
72            android:theme="@style/Theme.FuckingGooo" />
72-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:44:13-53
73
74        <service
74-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:46:9-57:19
75            android:name="com.fuck.fuckinggooo.service.ProxyVpnService"
75-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:47:13-52
76            android:exported="false"
76-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:48:13-37
77            android:foregroundServiceType="specialUse"
77-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:50:13-55
78            android:permission="android.permission.BIND_VPN_SERVICE" >
78-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:49:13-69
79            <property
79-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:51:13-53:39
80                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
80-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:52:17-76
81                android:value="vpn" />
81-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:53:17-36
82
83            <intent-filter>
83-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:54:13-56:29
84                <action android:name="android.net.VpnService" />
84-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:55:17-65
84-->C:\Users\<USER>\Desktop\fuckingGooo\app\src\main\AndroidManifest.xml:55:25-62
85            </intent-filter>
86        </service>
87
88        <activity
88-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a216616003f1a492619b244b674b4bd\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
89            android:name="androidx.compose.ui.tooling.PreviewActivity"
89-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a216616003f1a492619b244b674b4bd\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
90            android:exported="true" />
90-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\2a216616003f1a492619b244b674b4bd\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
91
92        <provider
92-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b10b96620ee2bf5cad32c65e9eeb9ed\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
93            android:name="androidx.startup.InitializationProvider"
93-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b10b96620ee2bf5cad32c65e9eeb9ed\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
94            android:authorities="com.fuck.fuckinggooo.androidx-startup"
94-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b10b96620ee2bf5cad32c65e9eeb9ed\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
95            android:exported="false" >
95-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b10b96620ee2bf5cad32c65e9eeb9ed\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
96            <meta-data
96-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b10b96620ee2bf5cad32c65e9eeb9ed\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
97                android:name="androidx.emoji2.text.EmojiCompatInitializer"
97-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b10b96620ee2bf5cad32c65e9eeb9ed\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
98                android:value="androidx.startup" />
98-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b10b96620ee2bf5cad32c65e9eeb9ed\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
99            <meta-data
99-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c63a5a6b0f691082c625c045806ccb5\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:29:13-31:52
100                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
100-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c63a5a6b0f691082c625c045806ccb5\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:30:17-78
101                android:value="androidx.startup" />
101-->[androidx.lifecycle:lifecycle-process:2.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c63a5a6b0f691082c625c045806ccb5\transformed\lifecycle-process-2.9.2\AndroidManifest.xml:31:17-49
102            <meta-data
102-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
103                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
103-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
104                android:value="androidx.startup" />
104-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
105        </provider>
106
107        <activity
107-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\183442f9a3920bb197d53d265b7c7fe8\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
108            android:name="androidx.activity.ComponentActivity"
108-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\183442f9a3920bb197d53d265b7c7fe8\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
109            android:exported="true" />
109-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\183442f9a3920bb197d53d265b7c7fe8\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
110
111        <service
111-->[androidx.room:room-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\947c871cd675082c47b1a517c87f0c8f\transformed\room-runtime-2.6.0\AndroidManifest.xml:24:9-28:63
112            android:name="androidx.room.MultiInstanceInvalidationService"
112-->[androidx.room:room-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\947c871cd675082c47b1a517c87f0c8f\transformed\room-runtime-2.6.0\AndroidManifest.xml:25:13-74
113            android:directBootAware="true"
113-->[androidx.room:room-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\947c871cd675082c47b1a517c87f0c8f\transformed\room-runtime-2.6.0\AndroidManifest.xml:26:13-43
114            android:exported="false" />
114-->[androidx.room:room-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\947c871cd675082c47b1a517c87f0c8f\transformed\room-runtime-2.6.0\AndroidManifest.xml:27:13-37
115
116        <receiver
116-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
117            android:name="androidx.profileinstaller.ProfileInstallReceiver"
117-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
118            android:directBootAware="false"
118-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
119            android:enabled="true"
119-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
120            android:exported="true"
120-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
121            android:permission="android.permission.DUMP" >
121-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
122            <intent-filter>
122-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
123                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
123-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
123-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
124            </intent-filter>
125            <intent-filter>
125-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
126                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
126-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
126-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
127            </intent-filter>
128            <intent-filter>
128-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
129                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
129-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
129-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
130            </intent-filter>
131            <intent-filter>
131-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
132                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
132-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
132-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e939a86441b77310b01eb35a8d5633e0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
133            </intent-filter>
134        </receiver>
135
136        <activity
136-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\730544f0989b6ea6b7a76ddb9126f348\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
137            android:name="com.journeyapps.barcodescanner.CaptureActivity"
137-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\730544f0989b6ea6b7a76ddb9126f348\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
138            android:clearTaskOnLaunch="true"
138-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\730544f0989b6ea6b7a76ddb9126f348\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
139            android:screenOrientation="sensorLandscape"
139-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\730544f0989b6ea6b7a76ddb9126f348\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
140            android:stateNotNeeded="true"
140-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\730544f0989b6ea6b7a76ddb9126f348\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
141            android:theme="@style/zxing_CaptureTheme"
141-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\730544f0989b6ea6b7a76ddb9126f348\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
142            android:windowSoftInputMode="stateAlwaysHidden" />
142-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\730544f0989b6ea6b7a76ddb9126f348\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
143    </application>
144
145</manifest>
