package com.fuck.fuckinggooo.model;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.lang.Class;
import java.lang.Exception;
import java.lang.IllegalStateException;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class ProxyConfigDao_Impl implements ProxyConfigDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<ProxyConfig> __insertionAdapterOfProxyConfig;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<ProxyConfig> __deletionAdapterOfProxyConfig;

  private final EntityDeletionOrUpdateAdapter<ProxyConfig> __updateAdapterOfProxyConfig;

  private final SharedSQLiteStatement __preparedStmtOfDeleteByUrl;

  public ProxyConfigDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfProxyConfig = new EntityInsertionAdapter<ProxyConfig>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `proxy_configs` (`subscriptionUrl`,`name`,`nodes`,`selectedNodeId`,`lastUpdate`) VALUES (?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ProxyConfig entity) {
        statement.bindString(1, entity.getSubscriptionUrl());
        statement.bindString(2, entity.getName());
        final String _tmp = __converters.fromNodeList(entity.getNodes());
        if (_tmp == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, _tmp);
        }
        if (entity.getSelectedNodeId() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getSelectedNodeId());
        }
        statement.bindLong(5, entity.getLastUpdate());
      }
    };
    this.__deletionAdapterOfProxyConfig = new EntityDeletionOrUpdateAdapter<ProxyConfig>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `proxy_configs` WHERE `subscriptionUrl` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ProxyConfig entity) {
        statement.bindString(1, entity.getSubscriptionUrl());
      }
    };
    this.__updateAdapterOfProxyConfig = new EntityDeletionOrUpdateAdapter<ProxyConfig>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `proxy_configs` SET `subscriptionUrl` = ?,`name` = ?,`nodes` = ?,`selectedNodeId` = ?,`lastUpdate` = ? WHERE `subscriptionUrl` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ProxyConfig entity) {
        statement.bindString(1, entity.getSubscriptionUrl());
        statement.bindString(2, entity.getName());
        final String _tmp = __converters.fromNodeList(entity.getNodes());
        if (_tmp == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, _tmp);
        }
        if (entity.getSelectedNodeId() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getSelectedNodeId());
        }
        statement.bindLong(5, entity.getLastUpdate());
        statement.bindString(6, entity.getSubscriptionUrl());
      }
    };
    this.__preparedStmtOfDeleteByUrl = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM proxy_configs WHERE subscriptionUrl = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insert(final ProxyConfig config, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfProxyConfig.insert(config);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object delete(final ProxyConfig config, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfProxyConfig.handle(config);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object update(final ProxyConfig config, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfProxyConfig.handle(config);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteByUrl(final String url, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteByUrl.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, url);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteByUrl.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<ProxyConfig> getByUrl(final String url) {
    final String _sql = "SELECT * FROM proxy_configs WHERE subscriptionUrl = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, url);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"proxy_configs"}, new Callable<ProxyConfig>() {
      @Override
      @Nullable
      public ProxyConfig call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfSubscriptionUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "subscriptionUrl");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfNodes = CursorUtil.getColumnIndexOrThrow(_cursor, "nodes");
          final int _cursorIndexOfSelectedNodeId = CursorUtil.getColumnIndexOrThrow(_cursor, "selectedNodeId");
          final int _cursorIndexOfLastUpdate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUpdate");
          final ProxyConfig _result;
          if (_cursor.moveToFirst()) {
            final String _tmpSubscriptionUrl;
            _tmpSubscriptionUrl = _cursor.getString(_cursorIndexOfSubscriptionUrl);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final List<ProxyNode> _tmpNodes;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfNodes)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfNodes);
            }
            final List<ProxyNode> _tmp_1 = __converters.toNodeList(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.List<com.fuck.fuckinggooo.model.ProxyNode>', but it was NULL.");
            } else {
              _tmpNodes = _tmp_1;
            }
            final String _tmpSelectedNodeId;
            if (_cursor.isNull(_cursorIndexOfSelectedNodeId)) {
              _tmpSelectedNodeId = null;
            } else {
              _tmpSelectedNodeId = _cursor.getString(_cursorIndexOfSelectedNodeId);
            }
            final long _tmpLastUpdate;
            _tmpLastUpdate = _cursor.getLong(_cursorIndexOfLastUpdate);
            _result = new ProxyConfig(_tmpSubscriptionUrl,_tmpName,_tmpNodes,_tmpSelectedNodeId,_tmpLastUpdate);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<ProxyConfig>> getAll() {
    final String _sql = "SELECT * FROM proxy_configs";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"proxy_configs"}, new Callable<List<ProxyConfig>>() {
      @Override
      @NonNull
      public List<ProxyConfig> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfSubscriptionUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "subscriptionUrl");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfNodes = CursorUtil.getColumnIndexOrThrow(_cursor, "nodes");
          final int _cursorIndexOfSelectedNodeId = CursorUtil.getColumnIndexOrThrow(_cursor, "selectedNodeId");
          final int _cursorIndexOfLastUpdate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUpdate");
          final List<ProxyConfig> _result = new ArrayList<ProxyConfig>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ProxyConfig _item;
            final String _tmpSubscriptionUrl;
            _tmpSubscriptionUrl = _cursor.getString(_cursorIndexOfSubscriptionUrl);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final List<ProxyNode> _tmpNodes;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfNodes)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfNodes);
            }
            final List<ProxyNode> _tmp_1 = __converters.toNodeList(_tmp);
            if (_tmp_1 == null) {
              throw new IllegalStateException("Expected NON-NULL 'java.util.List<com.fuck.fuckinggooo.model.ProxyNode>', but it was NULL.");
            } else {
              _tmpNodes = _tmp_1;
            }
            final String _tmpSelectedNodeId;
            if (_cursor.isNull(_cursorIndexOfSelectedNodeId)) {
              _tmpSelectedNodeId = null;
            } else {
              _tmpSelectedNodeId = _cursor.getString(_cursorIndexOfSelectedNodeId);
            }
            final long _tmpLastUpdate;
            _tmpLastUpdate = _cursor.getLong(_cursorIndexOfLastUpdate);
            _item = new ProxyConfig(_tmpSubscriptionUrl,_tmpName,_tmpNodes,_tmpSelectedNodeId,_tmpLastUpdate);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
