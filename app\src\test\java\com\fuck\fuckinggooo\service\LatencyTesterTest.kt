package com.fuck.fuckinggooo.service

import com.fuck.fuckinggooo.model.ProxyNode
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.Assert.*

/**
 * 延迟测试器的单元测试
 */
class LatencyTesterTest {

    private val latencyTester = LatencyTester()
    private val hysteria2Tester = Hysteria2LatencyTester()

    @Test
    fun testHysteria2LatencyTesting() = runBlocking {
        // 创建一个测试用的Hysteria2节点
        val hy2Node = ProxyNode(
            id = "test-hy2",
            name = "Test Hysteria2",
            server = "www.google.com", // 使用可达的服务器进行测试
            port = 443,
            protocol = "hysteria2",
            password = "test",
            latency = 0
        )

        // 测试Hysteria2专用延迟测试
        val latency = hysteria2Tester.testHysteria2Latency(hy2Node)
        
        // 验证结果
        assertTrue("延迟应该大于0或等于-1（失败）", latency > 0 || latency == -1L)
        
        if (latency > 0) {
            assertTrue("延迟应该在合理范围内", latency < 10000) // 小于10秒
            println("Hysteria2延迟测试结果: ${latency}ms")
        } else {
            println("Hysteria2延迟测试失败（这在测试环境中是正常的）")
        }
    }

    @Test
    fun testSmartLatencyTesting() = runBlocking {
        // 测试不同协议的智能延迟测试
        val testNodes = listOf(
            ProxyNode("test-hy2", "Test HY2", "www.google.com", 443, "hysteria2", "test", 0),
            ProxyNode("test-vmess", "Test VMess", "www.google.com", 443, "vmess", "test", 0),
            ProxyNode("test-ss", "Test SS", "www.google.com", 443, "shadowsocks", "test", 0)
        )

        testNodes.forEach { node ->
            val latency = latencyTester.testLatency(node)
            println("${node.protocol} 协议延迟测试结果: ${latency}ms")
            
            // 验证结果格式
            assertTrue("延迟应该大于0或等于-1", latency > 0 || latency == -1L)
        }
    }

    @Test
    fun testMultipleLatencyTesting() = runBlocking {
        val hy2Node = ProxyNode(
            id = "test-hy2-multi",
            name = "Test Hysteria2 Multiple",
            server = "www.google.com",
            port = 443,
            protocol = "hysteria2",
            password = "test",
            latency = 0
        )

        // 测试多次延迟测试
        val latency = latencyTester.testLatencyWithRetries(hy2Node, 2)
        
        println("多次延迟测试结果: ${latency}ms")
        assertTrue("延迟应该大于0或等于-1", latency > 0 || latency == -1L)
    }

    @Test
    fun testBatchLatencyTesting() = runBlocking {
        val testNodes = listOf(
            ProxyNode("test1", "Test 1", "www.google.com", 443, "hysteria2", "test", 0),
            ProxyNode("test2", "Test 2", "www.cloudflare.com", 443, "vmess", "test", 0)
        )

        // 测试批量延迟测试
        val results = latencyTester.batchTestLatency(testNodes)
        
        assertEquals("应该返回所有节点的结果", testNodes.size, results.size)
        
        results.forEach { (nodeId, result) ->
            println("节点 $nodeId 延迟: ${result.latency}ms, 成功: ${result.success}")
            assertNotNull("结果不应该为空", result)
            assertTrue("延迟应该大于0或等于-1", result.latency > 0 || result.latency == -1L)
        }
    }

    @Test
    fun testProtocolRecognition() {
        // 测试协议识别逻辑
        val protocols = mapOf(
            "hysteria2" to "应该使用Hysteria2测试器",
            "hy2" to "应该使用Hysteria2测试器", 
            "hysteria" to "应该使用Hysteria2测试器",
            "vmess" to "应该使用HTTP测试",
            "vless" to "应该使用HTTP测试",
            "trojan" to "应该使用HTTP测试",
            "shadowsocks" to "应该使用TCP测试",
            "ss" to "应该使用TCP测试",
            "unknown" to "应该使用默认TCP测试"
        )

        protocols.forEach { (protocol, expected) ->
            println("协议 $protocol: $expected")
            // 这里可以添加更具体的测试逻辑
        }
    }
}
