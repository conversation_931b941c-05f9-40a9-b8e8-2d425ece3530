package com.fuck.fuckinggooo.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.net.VpnService
import android.os.Build
import android.os.ParcelFileDescriptor
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.fuck.fuckinggooo.core.SingBoxCore
import com.fuck.fuckinggooo.core.TunToSocks
import com.fuck.fuckinggooo.model.ProxyNode
import com.fuck.fuckinggooo.viewmodel.ProxyMode
import com.fuck.fuckinggooo.core.SingBoxConfigGenerator
import com.google.gson.Gson
import kotlinx.coroutines.*
import java.io.*

class ProxyVpnService : VpnService() {

    companion object {
        private const val TAG = "ProxyVpnService"
        private const val NOTIFICATION_ID = 1
        private const val CHANNEL_ID = "vpn_service_channel"
        
        const val ACTION_START = "start"
        const val ACTION_STOP = "stop"
        const val EXTRA_PROXY_NODE_JSON = "proxy_node_json"
        const val EXTRA_PROXY_MODE = "proxy_mode"
        
        // 状态广播
        const val VPN_STATUS_ACTION = "com.fuck.fuckinggooo.service.VPN_STATUS_CHANGED"
        const val EXTRA_STATUS = "vpn_status"
        const val EXTRA_ERROR_MESSAGE = "error_message"
        
        // VPN状态常量
        const val STATUS_CONNECTING = "connecting"
        const val STATUS_CONNECTED = "connected"
        const val STATUS_DISCONNECTING = "disconnecting"
        const val STATUS_DISCONNECTED = "disconnected"
        const val STATUS_ERROR = "error"
        
        private var isServiceRunning = false
        private var currentInstance: ProxyVpnService? = null
    }

    private var vpnInterface: ParcelFileDescriptor? = null
    private var vpnJob: Job? = null
    
    // 广播VPN状态变化
    private fun broadcastVpnStatus(status: String, errorMessage: String? = null) {
        val intent = Intent(VPN_STATUS_ACTION).apply {
            putExtra(EXTRA_STATUS, status)
            errorMessage?.let { putExtra(EXTRA_ERROR_MESSAGE, it) }
        }
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
        Log.d(TAG, "Broadcast VPN status: $status${errorMessage?.let { ", error: $it" } ?: ""}")
    }
    private var currentNode: ProxyNode? = null
    private var isRunning = false
    private var tunToSocks: TunToSocks? = null
    private var singBoxCore: SingBoxCore? = null

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val action = intent?.action

        when (action) {
            ACTION_START -> {
                // 检查是否已经在运行
                if (isRunning) {
                    Log.w(TAG, "VPN is already running")
                    broadcastVpnStatus(STATUS_CONNECTED)
                    return START_STICKY
                }

                val nodeJson = intent.getStringExtra(EXTRA_PROXY_NODE_JSON)
                val modeString = intent.getStringExtra(EXTRA_PROXY_MODE)

                if (nodeJson == null) {
                    Log.e(TAG, "No proxy node JSON provided")
                    stopSelf()
                    return START_NOT_STICKY
                }

                try {
                    // 立即启动前台服务以避免ANR
                    val initialNotification = createInitialNotification()
                    startForeground(NOTIFICATION_ID, initialNotification)
                    Log.d(TAG, "Started foreground service with initial notification")

                    val node = Gson().fromJson(nodeJson, ProxyNode::class.java)
                    val mode = ProxyMode.valueOf(modeString ?: ProxyMode.RULE.name)

                    isServiceRunning = true
                    currentInstance = this
                    startVpn(node, mode)

                } catch (e: Exception) {
                    Log.e(TAG, "Failed to parse proxy node or mode", e)
                    stopSelf()
                    return START_NOT_STICKY
                }
            }
            ACTION_STOP -> {
                Log.d(TAG, "Received stop action")
                stopVpn()
                return START_NOT_STICKY
            }
            else -> {
                Log.w(TAG, "Unknown action: $action")
                return START_NOT_STICKY
            }
        }

        return START_STICKY
    }

    private fun createInitialNotification(): Notification {
        createNotificationChannel()
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("FuckingGooo VPN")
            .setContentText("正在连接...")
            .setSmallIcon(android.R.drawable.ic_dialog_info)
            .setOngoing(true)
            .build()
    }

    private fun createNotification(): Notification {
        createNotificationChannel()
        
        val stopIntent = Intent(this, ProxyVpnService::class.java).apply {
            action = ACTION_STOP
        }
        val stopPendingIntent = PendingIntent.getService(
            this, 0, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("FuckingGooo VPN")
            .setContentText("Connected to ${currentNode?.name ?: "Unknown"}")
            .setSmallIcon(android.R.drawable.ic_dialog_info)
            .setOngoing(true)
            .addAction(android.R.drawable.ic_menu_close_clear_cancel, "Disconnect", stopPendingIntent)
            .build()
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "VPN Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "VPN connection status"
                setShowBadge(false)
            }
            
            val manager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            manager.createNotificationChannel(channel)
        }
    }

    private fun startVpn(node: ProxyNode, mode: ProxyMode = ProxyMode.RULE) {
        if (isRunning) {
            Log.w(TAG, "VPN is already running")
            broadcastVpnStatus(STATUS_CONNECTED)
            return
        }

        Log.d(TAG, "Starting VPN for node: ${node.name}")
        isRunning = true
        broadcastVpnStatus(STATUS_CONNECTING)
        
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "Starting VPN with SingBox core for node: ${node.name}, mode: $mode")
                currentNode = node
                
                // 生成sing-box配置
                val configJson = SingBoxConfigGenerator.generateConfig(node, mode)
                Log.d(TAG, "Generated SingBox config: $configJson")
                
                // 初始化并启动SingBox核心
                singBoxCore = SingBoxCore(this@ProxyVpnService)
                val started = singBoxCore!!.start(configJson, node)
                
                if (!started) {
                    Log.w(TAG, "SingBox core start returned false, but continuing with VPN setup")
                }
                
                // 等待SingBox核心启动
                delay(1000) // 减少等待时间
                
                // 建立VPN接口
                setupVpnInterface()
                
                // 初始化TunToSocks转换器，连接到SingBox的SOCKS端口
                try {
                    val socksPort = singBoxCore!!.getSocksPort()
                    Log.d(TAG, "Creating TunToSocks with SOCKS port: $socksPort")
                    tunToSocks = TunToSocks("127.0.0.1", socksPort)
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to create TunToSocks with core port, using fallback port 1081", e)
                    // Fallback: 使用DirectSocksProxy的端口
                    tunToSocks = TunToSocks("127.0.0.1", 1081)
                }
                
                // 更新通知为已连接状态
                val notification = createNotification()
                val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                notificationManager.notify(NOTIFICATION_ID, notification)
                
                broadcastVpnStatus(STATUS_CONNECTED)
                Log.d(TAG, "VPN started successfully with SingBox core")

            } catch (e: Exception) {
                Log.e(TAG, "Failed to start VPN", e)
                isRunning = false
                broadcastVpnStatus(STATUS_ERROR, e.message)
                stopSelf()
            }
        }
    }

    private fun stopVpn() {
        if (!isRunning) {
            Log.w(TAG, "VPN is not running")
            return
        }

        broadcastVpnStatus(STATUS_DISCONNECTING)
        
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "Stopping VPN")
                
                // 停止TunToSocks转换器
                tunToSocks?.stop()
                tunToSocks = null
                
                // 停止SingBox核心
                singBoxCore?.stop()
                singBoxCore?.cleanup()
                singBoxCore = null
                
                // 停止数据转发
                vpnJob?.cancel()
                vpnJob = null
                
                // 关闭VPN接口
                vpnInterface?.close()
                vpnInterface = null
                
                isRunning = false
                currentNode = null
                
                broadcastVpnStatus(STATUS_DISCONNECTED)
                Log.d(TAG, "VPN stopped successfully")
                
            } catch (e: Exception) {
                Log.e(TAG, "Error stopping VPN", e)
                broadcastVpnStatus(STATUS_ERROR, e.message)
            }
        }
    }

    private fun setupVpnInterface() {
        val builder = Builder()
            .setSession("FuckingGooo VPN")
            .addAddress("**********", 30)  // 使用NekoBox的IP范围
            .addRoute("0.0.0.0", 0)         // 路由所有流量
            .addDnsServer("**********")     // 使用本地DNS
            .addDnsServer("*******")        // 备用DNS
            .addDnsServer("*******")
            .setMtu(1500)
            .setBlocking(false)

        // 添加应用白名单（排除自己）
        try {
            builder.addDisallowedApplication(packageName)
            Log.d(TAG, "Added disallowed application: $packageName")
        } catch (e: Exception) {
            Log.w(TAG, "Failed to add disallowed application", e)
        }

        // 建立VPN接口
        vpnInterface = builder.establish()

        if (vpnInterface == null) {
            throw RuntimeException("Failed to establish VPN interface")
        }

        Log.d(TAG, "VPN interface established with IP **********/30")

        // 启动数据转发
        startPacketForwarding()
    }

    private fun startPacketForwarding() {
        vpnJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                val vpnInput = FileInputStream(vpnInterface!!.fileDescriptor)
                val vpnOutput = FileOutputStream(vpnInterface!!.fileDescriptor)
                
                Log.d(TAG, "Starting packet forwarding with TunToSocks")
                
                // 启动TunToSocks转换器
                tunToSocks?.start(vpnInput, vpnOutput)
                
                Log.d(TAG, "TunToSocks packet forwarding started")
                
            } catch (e: Exception) {
                Log.e(TAG, "Error in packet forwarding", e)
            }
        }
    }



    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "Service destroyed")

        isServiceRunning = false
        currentInstance = null

        if (isRunning) {
            stopVpn()
        }
    }

    override fun onRevoke() {
        super.onRevoke()
        Log.d(TAG, "VPN permission revoked")
        stopVpn()
    }


}
