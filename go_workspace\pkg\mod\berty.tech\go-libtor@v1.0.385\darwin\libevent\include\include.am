# include/include.am for libevent
# Copyright 2000-2007 Niels Provos
# Copyright 2007-2012 <PERSON>els Provos and <PERSON>
#
# See LICENSE for copying information.

include_event2dir = $(includedir)/event2

EVENT2_EXPORT = \
	include/event2/buffer.h \
	include/event2/buffer_compat.h \
	include/event2/bufferevent.h \
	include/event2/bufferevent_compat.h \
	include/event2/bufferevent_struct.h \
	include/event2/dns.h \
	include/event2/dns_compat.h \
	include/event2/dns_struct.h \
	include/event2/event.h \
	include/event2/event_compat.h \
	include/event2/event_struct.h \
	include/event2/watch.h \
	include/event2/http.h \
	include/event2/http_compat.h \
	include/event2/http_struct.h \
	include/event2/keyvalq_struct.h \
	include/event2/listener.h \
	include/event2/rpc.h \
	include/event2/rpc_compat.h \
	include/event2/rpc_struct.h \
	include/event2/tag.h \
	include/event2/tag_compat.h \
	include/event2/thread.h \
	include/event2/util.h \
	include/event2/visibility.h

if OPENSSL
EVENT2_EXPORT += include/event2/bufferevent_ssl.h
endif

## Without the nobase_ prefixing, Automake would strip "include/event2/" from
## the source header filename to derive the installed header filename.
## With nobase_ the installed path is $(includedir)/include/event2/ev*.h.

if INSTALL_LIBEVENT
include_event2_HEADERS =	$(EVENT2_EXPORT)
nodist_include_event2_HEADERS = include/event2/event-config.h
else
noinst_HEADERS +=		$(EVENT2_EXPORT)
nodist_noinst_HEADERS =		include/event2/event-config.h
endif
