// Copyright 2019 Google LLC.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.12.2
// source: google/cloud/websecurityscanner/v1alpha/finding_addon.proto

package websecurityscanner

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Information reported for an outdated library.
type OutdatedLibrary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the outdated library.
	LibraryName string `protobuf:"bytes,1,opt,name=library_name,json=libraryName,proto3" json:"library_name,omitempty"`
	// The version number.
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	// URLs to learn more information about the vulnerabilities in the library.
	LearnMoreUrls []string `protobuf:"bytes,3,rep,name=learn_more_urls,json=learnMoreUrls,proto3" json:"learn_more_urls,omitempty"`
}

func (x *OutdatedLibrary) Reset() {
	*x = OutdatedLibrary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OutdatedLibrary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutdatedLibrary) ProtoMessage() {}

func (x *OutdatedLibrary) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutdatedLibrary.ProtoReflect.Descriptor instead.
func (*OutdatedLibrary) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_rawDescGZIP(), []int{0}
}

func (x *OutdatedLibrary) GetLibraryName() string {
	if x != nil {
		return x.LibraryName
	}
	return ""
}

func (x *OutdatedLibrary) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *OutdatedLibrary) GetLearnMoreUrls() []string {
	if x != nil {
		return x.LearnMoreUrls
	}
	return nil
}

// Information regarding any resource causing the vulnerability such
// as JavaScript sources, image, audio files, etc.
type ViolatingResource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The MIME type of this resource.
	ContentType string `protobuf:"bytes,1,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	// URL of this violating resource.
	ResourceUrl string `protobuf:"bytes,2,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
}

func (x *ViolatingResource) Reset() {
	*x = ViolatingResource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ViolatingResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ViolatingResource) ProtoMessage() {}

func (x *ViolatingResource) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ViolatingResource.ProtoReflect.Descriptor instead.
func (*ViolatingResource) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_rawDescGZIP(), []int{1}
}

func (x *ViolatingResource) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *ViolatingResource) GetResourceUrl() string {
	if x != nil {
		return x.ResourceUrl
	}
	return ""
}

// Information about vulnerable request parameters.
type VulnerableParameters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The vulnerable parameter names.
	ParameterNames []string `protobuf:"bytes,1,rep,name=parameter_names,json=parameterNames,proto3" json:"parameter_names,omitempty"`
}

func (x *VulnerableParameters) Reset() {
	*x = VulnerableParameters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulnerableParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulnerableParameters) ProtoMessage() {}

func (x *VulnerableParameters) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulnerableParameters.ProtoReflect.Descriptor instead.
func (*VulnerableParameters) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_rawDescGZIP(), []int{2}
}

func (x *VulnerableParameters) GetParameterNames() []string {
	if x != nil {
		return x.ParameterNames
	}
	return nil
}

// Information about vulnerable or missing HTTP Headers.
type VulnerableHeaders struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of vulnerable headers.
	Headers []*VulnerableHeaders_Header `protobuf:"bytes,1,rep,name=headers,proto3" json:"headers,omitempty"`
	// List of missing headers.
	MissingHeaders []*VulnerableHeaders_Header `protobuf:"bytes,2,rep,name=missing_headers,json=missingHeaders,proto3" json:"missing_headers,omitempty"`
}

func (x *VulnerableHeaders) Reset() {
	*x = VulnerableHeaders{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulnerableHeaders) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulnerableHeaders) ProtoMessage() {}

func (x *VulnerableHeaders) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulnerableHeaders.ProtoReflect.Descriptor instead.
func (*VulnerableHeaders) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_rawDescGZIP(), []int{3}
}

func (x *VulnerableHeaders) GetHeaders() []*VulnerableHeaders_Header {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *VulnerableHeaders) GetMissingHeaders() []*VulnerableHeaders_Header {
	if x != nil {
		return x.MissingHeaders
	}
	return nil
}

// Information reported for an XSS.
type Xss struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Stack traces leading to the point where the XSS occurred.
	StackTraces []string `protobuf:"bytes,1,rep,name=stack_traces,json=stackTraces,proto3" json:"stack_traces,omitempty"`
	// An error message generated by a javascript breakage.
	ErrorMessage string `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
}

func (x *Xss) Reset() {
	*x = Xss{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Xss) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Xss) ProtoMessage() {}

func (x *Xss) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Xss.ProtoReflect.Descriptor instead.
func (*Xss) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_rawDescGZIP(), []int{4}
}

func (x *Xss) GetStackTraces() []string {
	if x != nil {
		return x.StackTraces
	}
	return nil
}

func (x *Xss) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

// Describes a HTTP Header.
type VulnerableHeaders_Header struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Header name.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Header value.
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *VulnerableHeaders_Header) Reset() {
	*x = VulnerableHeaders_Header{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulnerableHeaders_Header) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulnerableHeaders_Header) ProtoMessage() {}

func (x *VulnerableHeaders_Header) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulnerableHeaders_Header.ProtoReflect.Descriptor instead.
func (*VulnerableHeaders_Header) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_rawDescGZIP(), []int{3, 0}
}

func (x *VulnerableHeaders_Header) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VulnerableHeaders_Header) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

var File_google_cloud_websecurityscanner_v1alpha_finding_addon_proto protoreflect.FileDescriptor

var file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_rawDesc = []byte{
	0x0a, 0x3b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77,
	0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2f, 0x66, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x27, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x22, 0x76, 0x0a, 0x0f, 0x4f, 0x75, 0x74, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x65, 0x61, 0x72, 0x6e, 0x5f,
	0x6d, 0x6f, 0x72, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0d, 0x6c, 0x65, 0x61, 0x72, 0x6e, 0x4d, 0x6f, 0x72, 0x65, 0x55, 0x72, 0x6c, 0x73, 0x22, 0x59,
	0x0a, 0x11, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x72, 0x6c, 0x22, 0x3f, 0x0a, 0x14, 0x56, 0x75, 0x6c,
	0x6e, 0x65, 0x72, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0x90, 0x02, 0x0a, 0x11, 0x56,
	0x75, 0x6c, 0x6e, 0x65, 0x72, 0x61, 0x62, 0x6c, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x12, 0x5b, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x41, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x56, 0x75, 0x6c, 0x6e,
	0x65, 0x72, 0x61, 0x62, 0x6c, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x6a, 0x0a,
	0x0f, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x2e, 0x56, 0x75, 0x6c, 0x6e, 0x65, 0x72, 0x61, 0x62, 0x6c, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x73, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x1a, 0x32, 0x0a, 0x06, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x4d, 0x0a,
	0x03, 0x58, 0x73, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x63,
	0x6b, 0x54, 0x72, 0x61, 0x63, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x9d, 0x01, 0x0a,
	0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x42, 0x11, 0x46, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50,
	0x01, 0x5a, 0x59, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67,
	0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77,
	0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x3b, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_rawDescOnce sync.Once
	file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_rawDescData = file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_rawDesc
)

func file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_rawDescGZIP() []byte {
	file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_rawDescOnce.Do(func() {
		file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_rawDescData)
	})
	return file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_rawDescData
}

var file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_goTypes = []interface{}{
	(*OutdatedLibrary)(nil),          // 0: google.cloud.websecurityscanner.v1alpha.OutdatedLibrary
	(*ViolatingResource)(nil),        // 1: google.cloud.websecurityscanner.v1alpha.ViolatingResource
	(*VulnerableParameters)(nil),     // 2: google.cloud.websecurityscanner.v1alpha.VulnerableParameters
	(*VulnerableHeaders)(nil),        // 3: google.cloud.websecurityscanner.v1alpha.VulnerableHeaders
	(*Xss)(nil),                      // 4: google.cloud.websecurityscanner.v1alpha.Xss
	(*VulnerableHeaders_Header)(nil), // 5: google.cloud.websecurityscanner.v1alpha.VulnerableHeaders.Header
}
var file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_depIdxs = []int32{
	5, // 0: google.cloud.websecurityscanner.v1alpha.VulnerableHeaders.headers:type_name -> google.cloud.websecurityscanner.v1alpha.VulnerableHeaders.Header
	5, // 1: google.cloud.websecurityscanner.v1alpha.VulnerableHeaders.missing_headers:type_name -> google.cloud.websecurityscanner.v1alpha.VulnerableHeaders.Header
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_init() }
func file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_init() {
	if File_google_cloud_websecurityscanner_v1alpha_finding_addon_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OutdatedLibrary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ViolatingResource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulnerableParameters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulnerableHeaders); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Xss); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulnerableHeaders_Header); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_goTypes,
		DependencyIndexes: file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_depIdxs,
		MessageInfos:      file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_msgTypes,
	}.Build()
	File_google_cloud_websecurityscanner_v1alpha_finding_addon_proto = out.File
	file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_rawDesc = nil
	file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_goTypes = nil
	file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_depIdxs = nil
}
