// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by aliasgen. DO NOT EDIT.

// Package vmwareengine aliases all exported identifiers in package
// "cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb".
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb.
// Please read https://github.com/googleapis/google-cloud-go/blob/main/migration.md
// for more details.
package vmwareengine

import (
	src "cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb"
	grpc "google.golang.org/grpc"
)

// Deprecated: Please use consts in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
const (
	Cluster_ACTIVE                                  = src.Cluster_ACTIVE
	Cluster_CREATING                                = src.Cluster_CREATING
	Cluster_DELETING                                = src.Cluster_DELETING
	Cluster_REPAIRING                               = src.Cluster_REPAIRING
	Cluster_STATE_UNSPECIFIED                       = src.Cluster_STATE_UNSPECIFIED
	Cluster_UPDATING                                = src.Cluster_UPDATING
	HcxActivationKey_AVAILABLE                      = src.HcxActivationKey_AVAILABLE
	HcxActivationKey_CONSUMED                       = src.HcxActivationKey_CONSUMED
	HcxActivationKey_CREATING                       = src.HcxActivationKey_CREATING
	HcxActivationKey_STATE_UNSPECIFIED              = src.HcxActivationKey_STATE_UNSPECIFIED
	Hcx_ACTIVE                                      = src.Hcx_ACTIVE
	Hcx_CREATING                                    = src.Hcx_CREATING
	Hcx_STATE_UNSPECIFIED                           = src.Hcx_STATE_UNSPECIFIED
	NetworkPolicy_NetworkService_ACTIVE             = src.NetworkPolicy_NetworkService_ACTIVE
	NetworkPolicy_NetworkService_RECONCILING        = src.NetworkPolicy_NetworkService_RECONCILING
	NetworkPolicy_NetworkService_STATE_UNSPECIFIED  = src.NetworkPolicy_NetworkService_STATE_UNSPECIFIED
	NetworkPolicy_NetworkService_UNPROVISIONED      = src.NetworkPolicy_NetworkService_UNPROVISIONED
	Nsx_ACTIVE                                      = src.Nsx_ACTIVE
	Nsx_CREATING                                    = src.Nsx_CREATING
	Nsx_STATE_UNSPECIFIED                           = src.Nsx_STATE_UNSPECIFIED
	PrivateCloud_ACTIVE                             = src.PrivateCloud_ACTIVE
	PrivateCloud_CREATING                           = src.PrivateCloud_CREATING
	PrivateCloud_DELETED                            = src.PrivateCloud_DELETED
	PrivateCloud_FAILED                             = src.PrivateCloud_FAILED
	PrivateCloud_PURGING                            = src.PrivateCloud_PURGING
	PrivateCloud_STATE_UNSPECIFIED                  = src.PrivateCloud_STATE_UNSPECIFIED
	PrivateCloud_UPDATING                           = src.PrivateCloud_UPDATING
	Subnet_ACTIVE                                   = src.Subnet_ACTIVE
	Subnet_CREATING                                 = src.Subnet_CREATING
	Subnet_DELETING                                 = src.Subnet_DELETING
	Subnet_STATE_UNSPECIFIED                        = src.Subnet_STATE_UNSPECIFIED
	Subnet_UPDATING                                 = src.Subnet_UPDATING
	Vcenter_ACTIVE                                  = src.Vcenter_ACTIVE
	Vcenter_CREATING                                = src.Vcenter_CREATING
	Vcenter_STATE_UNSPECIFIED                       = src.Vcenter_STATE_UNSPECIFIED
	VmwareEngineNetwork_ACTIVE                      = src.VmwareEngineNetwork_ACTIVE
	VmwareEngineNetwork_CREATING                    = src.VmwareEngineNetwork_CREATING
	VmwareEngineNetwork_DELETING                    = src.VmwareEngineNetwork_DELETING
	VmwareEngineNetwork_LEGACY                      = src.VmwareEngineNetwork_LEGACY
	VmwareEngineNetwork_STATE_UNSPECIFIED           = src.VmwareEngineNetwork_STATE_UNSPECIFIED
	VmwareEngineNetwork_TYPE_UNSPECIFIED            = src.VmwareEngineNetwork_TYPE_UNSPECIFIED
	VmwareEngineNetwork_UPDATING                    = src.VmwareEngineNetwork_UPDATING
	VmwareEngineNetwork_VpcNetwork_GOOGLE_CLOUD     = src.VmwareEngineNetwork_VpcNetwork_GOOGLE_CLOUD
	VmwareEngineNetwork_VpcNetwork_INTERNET         = src.VmwareEngineNetwork_VpcNetwork_INTERNET
	VmwareEngineNetwork_VpcNetwork_INTRANET         = src.VmwareEngineNetwork_VpcNetwork_INTRANET
	VmwareEngineNetwork_VpcNetwork_TYPE_UNSPECIFIED = src.VmwareEngineNetwork_VpcNetwork_TYPE_UNSPECIFIED
)

// Deprecated: Please use vars in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
var (
	Cluster_State_name                                   = src.Cluster_State_name
	Cluster_State_value                                  = src.Cluster_State_value
	File_google_cloud_vmwareengine_v1_vmwareengine_proto = src.File_google_cloud_vmwareengine_v1_vmwareengine_proto
	HcxActivationKey_State_name                          = src.HcxActivationKey_State_name
	HcxActivationKey_State_value                         = src.HcxActivationKey_State_value
	Hcx_State_name                                       = src.Hcx_State_name
	Hcx_State_value                                      = src.Hcx_State_value
	NetworkPolicy_NetworkService_State_name              = src.NetworkPolicy_NetworkService_State_name
	NetworkPolicy_NetworkService_State_value             = src.NetworkPolicy_NetworkService_State_value
	Nsx_State_name                                       = src.Nsx_State_name
	Nsx_State_value                                      = src.Nsx_State_value
	PrivateCloud_State_name                              = src.PrivateCloud_State_name
	PrivateCloud_State_value                             = src.PrivateCloud_State_value
	Subnet_State_name                                    = src.Subnet_State_name
	Subnet_State_value                                   = src.Subnet_State_value
	Vcenter_State_name                                   = src.Vcenter_State_name
	Vcenter_State_value                                  = src.Vcenter_State_value
	VmwareEngineNetwork_State_name                       = src.VmwareEngineNetwork_State_name
	VmwareEngineNetwork_State_value                      = src.VmwareEngineNetwork_State_value
	VmwareEngineNetwork_Type_name                        = src.VmwareEngineNetwork_Type_name
	VmwareEngineNetwork_Type_value                       = src.VmwareEngineNetwork_Type_value
	VmwareEngineNetwork_VpcNetwork_Type_name             = src.VmwareEngineNetwork_VpcNetwork_Type_name
	VmwareEngineNetwork_VpcNetwork_Type_value            = src.VmwareEngineNetwork_VpcNetwork_Type_value
)

// A cluster in a private cloud.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type Cluster = src.Cluster

// Enum State defines possible states of private cloud clusters.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type Cluster_State = src.Cluster_State

// Request message for
// [VmwareEngine.CreateCluster][google.cloud.vmwareengine.v1.VmwareEngine.CreateCluster]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type CreateClusterRequest = src.CreateClusterRequest

// Request message for
// [VmwareEngine.CreateHcxActivationKey][google.cloud.vmwareengine.v1.VmwareEngine.CreateHcxActivationKey]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type CreateHcxActivationKeyRequest = src.CreateHcxActivationKeyRequest

// Request message for
// [VmwareEngine.CreateNetworkPolicy][google.cloud.vmwareengine.v1.VmwareEngine.CreateNetworkPolicy]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type CreateNetworkPolicyRequest = src.CreateNetworkPolicyRequest

// Request message for
// [VmwareEngine.CreatePrivateCloud][google.cloud.vmwareengine.v1.VmwareEngine.CreatePrivateCloud]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type CreatePrivateCloudRequest = src.CreatePrivateCloudRequest

// Request message for
// [VmwareEngine.CreateVmwareEngineNetwork][google.cloud.vmwareengine.v1.VmwareEngine.CreateVmwareEngineNetwork]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type CreateVmwareEngineNetworkRequest = src.CreateVmwareEngineNetworkRequest

// Credentials for a private cloud.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type Credentials = src.Credentials

// Request message for
// [VmwareEngine.DeleteCluster][google.cloud.vmwareengine.v1.VmwareEngine.DeleteCluster]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type DeleteClusterRequest = src.DeleteClusterRequest

// Request message for
// [VmwareEngine.DeleteNetworkPolicy][google.cloud.vmwareengine.v1.VmwareEngine.DeleteNetworkPolicy]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type DeleteNetworkPolicyRequest = src.DeleteNetworkPolicyRequest

// Request message for
// [VmwareEngine.DeletePrivateCloud][google.cloud.vmwareengine.v1.VmwareEngine.DeletePrivateCloud]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type DeletePrivateCloudRequest = src.DeletePrivateCloudRequest

// Request message for
// [VmwareEngine.DeleteVmwareEngineNetwork][google.cloud.vmwareengine.v1.VmwareEngine.DeleteVmwareEngineNetwork]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type DeleteVmwareEngineNetworkRequest = src.DeleteVmwareEngineNetworkRequest

// Request message for
// [VmwareEngine.GetCluster][google.cloud.vmwareengine.v1.VmwareEngine.GetCluster]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type GetClusterRequest = src.GetClusterRequest

// Request message for [VmwareEngine.GetHcxActivationKeys][]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type GetHcxActivationKeyRequest = src.GetHcxActivationKeyRequest

// Request message for
// [VmwareEngine.GetNetworkPolicy][google.cloud.vmwareengine.v1.VmwareEngine.GetNetworkPolicy]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type GetNetworkPolicyRequest = src.GetNetworkPolicyRequest

// Request message for
// [VmwareEngine.GetNodeType][google.cloud.vmwareengine.v1.VmwareEngine.GetNodeType]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type GetNodeTypeRequest = src.GetNodeTypeRequest

// Request message for
// [VmwareEngine.GetPrivateCloud][google.cloud.vmwareengine.v1.VmwareEngine.GetPrivateCloud]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type GetPrivateCloudRequest = src.GetPrivateCloudRequest

// Request message for
// [VmwareEngine.GetVmwareEngineNetwork][google.cloud.vmwareengine.v1.VmwareEngine.GetVmwareEngineNetwork]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type GetVmwareEngineNetworkRequest = src.GetVmwareEngineNetworkRequest

// Details about a HCX Cloud Manager appliance.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type Hcx = src.Hcx

// HCX activation key. A default key is created during private cloud
// provisioning, but this behavior is subject to change and you should always
// verify active keys. Use
// [VmwareEngine.ListHcxActivationKeys][google.cloud.vmwareengine.v1.VmwareEngine.ListHcxActivationKeys]
// to retrieve existing keys and
// [VmwareEngine.CreateHcxActivationKey][google.cloud.vmwareengine.v1.VmwareEngine.CreateHcxActivationKey]
// to create new ones.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type HcxActivationKey = src.HcxActivationKey

// State of HCX activation key
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type HcxActivationKey_State = src.HcxActivationKey_State

// State of the appliance
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type Hcx_State = src.Hcx_State

// Request message for
// [VmwareEngine.ListClusters][google.cloud.vmwareengine.v1.VmwareEngine.ListClusters]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type ListClustersRequest = src.ListClustersRequest

// Response message for
// [VmwareEngine.ListClusters][google.cloud.vmwareengine.v1.VmwareEngine.ListClusters]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type ListClustersResponse = src.ListClustersResponse

// Request message for
// [VmwareEngine.ListHcxActivationKeys][google.cloud.vmwareengine.v1.VmwareEngine.ListHcxActivationKeys]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type ListHcxActivationKeysRequest = src.ListHcxActivationKeysRequest

// Response message for
// [VmwareEngine.ListHcxActivationKeys][google.cloud.vmwareengine.v1.VmwareEngine.ListHcxActivationKeys]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type ListHcxActivationKeysResponse = src.ListHcxActivationKeysResponse

// Request message for
// [VmwareEngine.ListNetworkPolicies][google.cloud.vmwareengine.v1.VmwareEngine.ListNetworkPolicies]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type ListNetworkPoliciesRequest = src.ListNetworkPoliciesRequest

// Response message for
// [VmwareEngine.ListNetworkPolicies][google.cloud.vmwareengine.v1.VmwareEngine.ListNetworkPolicies]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type ListNetworkPoliciesResponse = src.ListNetworkPoliciesResponse

// Request message for
// [VmwareEngine.ListNodeTypes][google.cloud.vmwareengine.v1.VmwareEngine.ListNodeTypes]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type ListNodeTypesRequest = src.ListNodeTypesRequest

// Response message for
// [VmwareEngine.ListNodeTypes][google.cloud.vmwareengine.v1.VmwareEngine.ListNodeTypes]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type ListNodeTypesResponse = src.ListNodeTypesResponse

// Request message for
// [VmwareEngine.ListPrivateClouds][google.cloud.vmwareengine.v1.VmwareEngine.ListPrivateClouds]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type ListPrivateCloudsRequest = src.ListPrivateCloudsRequest

// Response message for
// [VmwareEngine.ListPrivateClouds][google.cloud.vmwareengine.v1.VmwareEngine.ListPrivateClouds]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type ListPrivateCloudsResponse = src.ListPrivateCloudsResponse

// Request message for
// [VmwareEngine.ListSubnets][google.cloud.vmwareengine.v1.VmwareEngine.ListSubnets]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type ListSubnetsRequest = src.ListSubnetsRequest

// Response message for
// [VmwareEngine.ListSubnets][google.cloud.vmwareengine.v1.VmwareEngine.ListSubnets]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type ListSubnetsResponse = src.ListSubnetsResponse

// Request message for
// [VmwareEngine.ListVmwareEngineNetworks][google.cloud.vmwareengine.v1.VmwareEngine.ListVmwareEngineNetworks]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type ListVmwareEngineNetworksRequest = src.ListVmwareEngineNetworksRequest

// Response message for
// [VmwareEngine.ListVmwareEngineNetworks][google.cloud.vmwareengine.v1.VmwareEngine.ListVmwareEngineNetworks]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type ListVmwareEngineNetworksResponse = src.ListVmwareEngineNetworksResponse

// Network configuration in the consumer project with which the peering has to
// be done.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type NetworkConfig = src.NetworkConfig

// Represents a network policy resource. Network policies are regional
// resources. You can use a network policy to enable or disable internet access
// and external IP access. Network policies are associated with a VMware Engine
// network, which might span across regions. For a given region, a network
// policy applies to all private clouds in the VMware Engine network associated
// with the policy.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type NetworkPolicy = src.NetworkPolicy

// Represents a network service that is managed by a `NetworkPolicy` resource.
// A network service provides a way to control an aspect of external access to
// VMware workloads. For example, whether the VMware workloads in the private
// clouds governed by a network policy can access or be accessed from the
// internet.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type NetworkPolicy_NetworkService = src.NetworkPolicy_NetworkService

// Enum State defines possible states of a network policy controlled service.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type NetworkPolicy_NetworkService_State = src.NetworkPolicy_NetworkService_State

// Describes node type.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type NodeType = src.NodeType

// Information about the type and number of nodes associated with the cluster.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type NodeTypeConfig = src.NodeTypeConfig

// Details about a NSX Manager appliance.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type Nsx = src.Nsx

// State of the appliance
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type Nsx_State = src.Nsx_State

// Represents the metadata of the long-running operation.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type OperationMetadata = src.OperationMetadata

// Represents a private cloud resource. Private clouds are zonal resources.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type PrivateCloud = src.PrivateCloud

// Management cluster configuration.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type PrivateCloud_ManagementCluster = src.PrivateCloud_ManagementCluster

// Enum State defines possible states of private clouds.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type PrivateCloud_State = src.PrivateCloud_State

// Request message for
// [VmwareEngine.ResetNsxCredentials][google.cloud.vmwareengine.v1.VmwareEngine.ResetNsxCredentials]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type ResetNsxCredentialsRequest = src.ResetNsxCredentialsRequest

// Request message for
// [VmwareEngine.ResetVcenterCredentials][google.cloud.vmwareengine.v1.VmwareEngine.ResetVcenterCredentials]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type ResetVcenterCredentialsRequest = src.ResetVcenterCredentialsRequest

// Request message for
// [VmwareEngine.ShowNsxCredentials][google.cloud.vmwareengine.v1.VmwareEngine.ShowNsxCredentials]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type ShowNsxCredentialsRequest = src.ShowNsxCredentialsRequest

// Request message for
// [VmwareEngine.ShowVcenterCredentials][google.cloud.vmwareengine.v1.VmwareEngine.ShowVcenterCredentials]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type ShowVcenterCredentialsRequest = src.ShowVcenterCredentialsRequest

// Subnet in a private cloud. Either `management` subnets (such as vMotion)
// that are read-only, or `userDefined`, which can also be updated.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type Subnet = src.Subnet

// Defines possible states of subnets.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type Subnet_State = src.Subnet_State

// Request message for
// [VmwareEngine.UndeletePrivateCloud][google.cloud.vmwareengine.v1.VmwareEngine.UndeletePrivateCloud]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type UndeletePrivateCloudRequest = src.UndeletePrivateCloudRequest

// UnimplementedVmwareEngineServer can be embedded to have forward compatible
// implementations.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type UnimplementedVmwareEngineServer = src.UnimplementedVmwareEngineServer

// Request message for
// [VmwareEngine.UpdateCluster][google.cloud.vmwareengine.v1.VmwareEngine.UpdateCluster]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type UpdateClusterRequest = src.UpdateClusterRequest

// Request message for
// [VmwareEngine.UpdateNetworkPolicy][google.cloud.vmwareengine.v1.VmwareEngine.UpdateNetworkPolicy]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type UpdateNetworkPolicyRequest = src.UpdateNetworkPolicyRequest

// Request message for
// [VmwareEngine.UpdatePrivateCloud][google.cloud.vmwareengine.v1.VmwareEngine.UpdatePrivateCloud]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type UpdatePrivateCloudRequest = src.UpdatePrivateCloudRequest

// Request message for
// [VmwareEngine.UpdateVmwareEngineNetwork][google.cloud.vmwareengine.v1.VmwareEngine.UpdateVmwareEngineNetwork]
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type UpdateVmwareEngineNetworkRequest = src.UpdateVmwareEngineNetworkRequest

// Details about a vCenter Server management appliance.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type Vcenter = src.Vcenter

// State of the appliance
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type Vcenter_State = src.Vcenter_State

// VmwareEngineClient is the client API for VmwareEngine service. For
// semantics around ctx use and closing/ending streaming RPCs, please refer to
// https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type VmwareEngineClient = src.VmwareEngineClient

// VMware Engine network resource that provides connectivity for VMware Engine
// private clouds.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type VmwareEngineNetwork = src.VmwareEngineNetwork

// Enum State defines possible states of VMware Engine network.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type VmwareEngineNetwork_State = src.VmwareEngineNetwork_State

// Enum Type defines possible types of VMware Engine network.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type VmwareEngineNetwork_Type = src.VmwareEngineNetwork_Type

// Represents a VMware Engine VPC network that is managed by a VMware Engine
// network resource.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type VmwareEngineNetwork_VpcNetwork = src.VmwareEngineNetwork_VpcNetwork

// Enum Type defines possible types of a VMware Engine network controlled
// service.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type VmwareEngineNetwork_VpcNetwork_Type = src.VmwareEngineNetwork_VpcNetwork_Type

// VmwareEngineServer is the server API for VmwareEngine service.
//
// Deprecated: Please use types in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
type VmwareEngineServer = src.VmwareEngineServer

// Deprecated: Please use funcs in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
func NewVmwareEngineClient(cc grpc.ClientConnInterface) VmwareEngineClient {
	return src.NewVmwareEngineClient(cc)
}

// Deprecated: Please use funcs in: cloud.google.com/go/vmwareengine/apiv1/vmwareenginepb
func RegisterVmwareEngineServer(s *grpc.Server, srv VmwareEngineServer) {
	src.RegisterVmwareEngineServer(s, srv)
}
