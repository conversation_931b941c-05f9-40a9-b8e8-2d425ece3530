package com.fuck.fuckinggooo.model;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AppDatabase_Impl extends AppDatabase {
  private volatile ProxyNodeDao _proxyNodeDao;

  private volatile ProxyConfigDao _proxyConfigDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(3) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `proxy_nodes` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `server` TEXT NOT NULL, `port` INTEGER NOT NULL, `protocol` TEXT NOT NULL, `password` TEXT, `uuid` TEXT, `alterId` INTEGER, `security` TEXT, `network` TEXT, `username` TEXT, `path` TEXT, `host` TEXT, `serviceName` TEXT, `flow` TEXT, `headerType` TEXT, `tls` INTEGER, `sni` TEXT, `allowInsecure` INTEGER, `latency` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `proxy_configs` (`subscriptionUrl` TEXT NOT NULL, `name` TEXT NOT NULL, `nodes` TEXT NOT NULL, `selectedNodeId` TEXT, `lastUpdate` INTEGER NOT NULL, PRIMARY KEY(`subscriptionUrl`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '4383c8b1d52527dfcd7ada0599719213')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `proxy_nodes`");
        db.execSQL("DROP TABLE IF EXISTS `proxy_configs`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsProxyNodes = new HashMap<String, TableInfo.Column>(20);
        _columnsProxyNodes.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyNodes.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyNodes.put("server", new TableInfo.Column("server", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyNodes.put("port", new TableInfo.Column("port", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyNodes.put("protocol", new TableInfo.Column("protocol", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyNodes.put("password", new TableInfo.Column("password", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyNodes.put("uuid", new TableInfo.Column("uuid", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyNodes.put("alterId", new TableInfo.Column("alterId", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyNodes.put("security", new TableInfo.Column("security", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyNodes.put("network", new TableInfo.Column("network", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyNodes.put("username", new TableInfo.Column("username", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyNodes.put("path", new TableInfo.Column("path", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyNodes.put("host", new TableInfo.Column("host", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyNodes.put("serviceName", new TableInfo.Column("serviceName", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyNodes.put("flow", new TableInfo.Column("flow", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyNodes.put("headerType", new TableInfo.Column("headerType", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyNodes.put("tls", new TableInfo.Column("tls", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyNodes.put("sni", new TableInfo.Column("sni", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyNodes.put("allowInsecure", new TableInfo.Column("allowInsecure", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyNodes.put("latency", new TableInfo.Column("latency", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysProxyNodes = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesProxyNodes = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoProxyNodes = new TableInfo("proxy_nodes", _columnsProxyNodes, _foreignKeysProxyNodes, _indicesProxyNodes);
        final TableInfo _existingProxyNodes = TableInfo.read(db, "proxy_nodes");
        if (!_infoProxyNodes.equals(_existingProxyNodes)) {
          return new RoomOpenHelper.ValidationResult(false, "proxy_nodes(com.fuck.fuckinggooo.model.ProxyNode).\n"
                  + " Expected:\n" + _infoProxyNodes + "\n"
                  + " Found:\n" + _existingProxyNodes);
        }
        final HashMap<String, TableInfo.Column> _columnsProxyConfigs = new HashMap<String, TableInfo.Column>(5);
        _columnsProxyConfigs.put("subscriptionUrl", new TableInfo.Column("subscriptionUrl", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyConfigs.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyConfigs.put("nodes", new TableInfo.Column("nodes", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyConfigs.put("selectedNodeId", new TableInfo.Column("selectedNodeId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsProxyConfigs.put("lastUpdate", new TableInfo.Column("lastUpdate", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysProxyConfigs = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesProxyConfigs = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoProxyConfigs = new TableInfo("proxy_configs", _columnsProxyConfigs, _foreignKeysProxyConfigs, _indicesProxyConfigs);
        final TableInfo _existingProxyConfigs = TableInfo.read(db, "proxy_configs");
        if (!_infoProxyConfigs.equals(_existingProxyConfigs)) {
          return new RoomOpenHelper.ValidationResult(false, "proxy_configs(com.fuck.fuckinggooo.model.ProxyConfig).\n"
                  + " Expected:\n" + _infoProxyConfigs + "\n"
                  + " Found:\n" + _existingProxyConfigs);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "4383c8b1d52527dfcd7ada0599719213", "7d902dd2a1a056e8dee537fa3dc01479");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "proxy_nodes","proxy_configs");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    try {
      super.beginTransaction();
      _db.execSQL("DELETE FROM `proxy_nodes`");
      _db.execSQL("DELETE FROM `proxy_configs`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(ProxyNodeDao.class, ProxyNodeDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(ProxyConfigDao.class, ProxyConfigDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public ProxyNodeDao proxyNodeDao() {
    if (_proxyNodeDao != null) {
      return _proxyNodeDao;
    } else {
      synchronized(this) {
        if(_proxyNodeDao == null) {
          _proxyNodeDao = new ProxyNodeDao_Impl(this);
        }
        return _proxyNodeDao;
      }
    }
  }

  @Override
  public ProxyConfigDao proxyConfigDao() {
    if (_proxyConfigDao != null) {
      return _proxyConfigDao;
    } else {
      synchronized(this) {
        if(_proxyConfigDao == null) {
          _proxyConfigDao = new ProxyConfigDao_Impl(this);
        }
        return _proxyConfigDao;
      }
    }
  }
}
