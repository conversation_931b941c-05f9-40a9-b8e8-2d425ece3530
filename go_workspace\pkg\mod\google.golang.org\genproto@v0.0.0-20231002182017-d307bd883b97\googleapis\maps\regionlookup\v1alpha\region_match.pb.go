// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.12.2
// source: google/maps/regionlookup/v1alpha/region_match.proto

package regionlookup

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Region Match.
//
// Next available tag: 5
type RegionMatch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Place ID of the region that is matched. If region is found, this field is
	// not set.
	MatchedPlaceId string `protobuf:"bytes,1,opt,name=matched_place_id,json=matchedPlaceId,proto3" json:"matched_place_id,omitempty"`
	// Region candidate IDs. Up to three candidates may be returned.
	CandidatePlaceIds []string `protobuf:"bytes,2,rep,name=candidate_place_ids,json=candidatePlaceIds,proto3" json:"candidate_place_ids,omitempty"`
	// Matching debug information for when no match is found.
	DebugInfo string `protobuf:"bytes,3,opt,name=debug_info,json=debugInfo,proto3" json:"debug_info,omitempty"`
}

func (x *RegionMatch) Reset() {
	*x = RegionMatch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_regionlookup_v1alpha_region_match_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegionMatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegionMatch) ProtoMessage() {}

func (x *RegionMatch) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_regionlookup_v1alpha_region_match_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegionMatch.ProtoReflect.Descriptor instead.
func (*RegionMatch) Descriptor() ([]byte, []int) {
	return file_google_maps_regionlookup_v1alpha_region_match_proto_rawDescGZIP(), []int{0}
}

func (x *RegionMatch) GetMatchedPlaceId() string {
	if x != nil {
		return x.MatchedPlaceId
	}
	return ""
}

func (x *RegionMatch) GetCandidatePlaceIds() []string {
	if x != nil {
		return x.CandidatePlaceIds
	}
	return nil
}

func (x *RegionMatch) GetDebugInfo() string {
	if x != nil {
		return x.DebugInfo
	}
	return ""
}

var File_google_maps_regionlookup_v1alpha_region_match_proto protoreflect.FileDescriptor

var file_google_maps_regionlookup_v1alpha_region_match_proto_rawDesc = []byte{
	0x0a, 0x33, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x2f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61,
	0x70, 0x73, 0x2e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x2e,
	0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x22, 0x86, 0x01, 0x0a, 0x0b, 0x52, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x12, 0x28, 0x0a, 0x10, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x64, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x61, 0x6e, 0x64, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x70,
	0x6c, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11,
	0x63, 0x61, 0x6e, 0x64, 0x69, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x49, 0x64,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x62, 0x75, 0x67, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x62, 0x75, 0x67, 0x49, 0x6e, 0x66, 0x6f,
	0x42, 0xda, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75,
	0x70, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x42, 0x10, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x4c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67,
	0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x70, 0x69, 0x73, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x3b, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0xf8, 0x01, 0x01, 0xa2, 0x02,
	0x06, 0x4d, 0x52, 0x4c, 0x56, 0x31, 0x41, 0xaa, 0x02, 0x20, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x4d, 0x61, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x6f, 0x6b,
	0x75, 0x70, 0x2e, 0x56, 0x31, 0x41, 0x6c, 0x70, 0x68, 0x61, 0xca, 0x02, 0x20, 0x47, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x5c, 0x4d, 0x61, 0x70, 0x73, 0x5c, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4c,
	0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x5c, 0x56, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_maps_regionlookup_v1alpha_region_match_proto_rawDescOnce sync.Once
	file_google_maps_regionlookup_v1alpha_region_match_proto_rawDescData = file_google_maps_regionlookup_v1alpha_region_match_proto_rawDesc
)

func file_google_maps_regionlookup_v1alpha_region_match_proto_rawDescGZIP() []byte {
	file_google_maps_regionlookup_v1alpha_region_match_proto_rawDescOnce.Do(func() {
		file_google_maps_regionlookup_v1alpha_region_match_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_maps_regionlookup_v1alpha_region_match_proto_rawDescData)
	})
	return file_google_maps_regionlookup_v1alpha_region_match_proto_rawDescData
}

var file_google_maps_regionlookup_v1alpha_region_match_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_google_maps_regionlookup_v1alpha_region_match_proto_goTypes = []interface{}{
	(*RegionMatch)(nil), // 0: google.maps.regionlookup.v1alpha.RegionMatch
}
var file_google_maps_regionlookup_v1alpha_region_match_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_google_maps_regionlookup_v1alpha_region_match_proto_init() }
func file_google_maps_regionlookup_v1alpha_region_match_proto_init() {
	if File_google_maps_regionlookup_v1alpha_region_match_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_maps_regionlookup_v1alpha_region_match_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegionMatch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_maps_regionlookup_v1alpha_region_match_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_maps_regionlookup_v1alpha_region_match_proto_goTypes,
		DependencyIndexes: file_google_maps_regionlookup_v1alpha_region_match_proto_depIdxs,
		MessageInfos:      file_google_maps_regionlookup_v1alpha_region_match_proto_msgTypes,
	}.Build()
	File_google_maps_regionlookup_v1alpha_region_match_proto = out.File
	file_google_maps_regionlookup_v1alpha_region_match_proto_rawDesc = nil
	file_google_maps_regionlookup_v1alpha_region_match_proto_goTypes = nil
	file_google_maps_regionlookup_v1alpha_region_match_proto_depIdxs = nil
}
