// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by aliasgen. DO NOT EDIT.

// Package executions aliases all exported identifiers in package
// "cloud.google.com/go/workflows/executions/apiv1beta/executionspb".
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/executions/apiv1beta/executionspb.
// Please read https://github.com/googleapis/google-cloud-go/blob/main/migration.md
// for more details.
package executions

import (
	src "cloud.google.com/go/workflows/executions/apiv1beta/executionspb"
	grpc "google.golang.org/grpc"
)

// Deprecated: Please use consts in: cloud.google.com/go/workflows/executions/apiv1beta/executionspb
const (
	ExecutionView_BASIC                      = src.ExecutionView_BASIC
	ExecutionView_EXECUTION_VIEW_UNSPECIFIED = src.ExecutionView_EXECUTION_VIEW_UNSPECIFIED
	ExecutionView_FULL                       = src.ExecutionView_FULL
	Execution_ACTIVE                         = src.Execution_ACTIVE
	Execution_CANCELLED                      = src.Execution_CANCELLED
	Execution_FAILED                         = src.Execution_FAILED
	Execution_STATE_UNSPECIFIED              = src.Execution_STATE_UNSPECIFIED
	Execution_SUCCEEDED                      = src.Execution_SUCCEEDED
)

// Deprecated: Please use vars in: cloud.google.com/go/workflows/executions/apiv1beta/executionspb
var (
	ExecutionView_name                                             = src.ExecutionView_name
	ExecutionView_value                                            = src.ExecutionView_value
	Execution_State_name                                           = src.Execution_State_name
	Execution_State_value                                          = src.Execution_State_value
	File_google_cloud_workflows_executions_v1beta_executions_proto = src.File_google_cloud_workflows_executions_v1beta_executions_proto
)

// Request for the
// [CancelExecution][google.cloud.workflows.executions.v1beta.Executions.CancelExecution]
// method.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/executions/apiv1beta/executionspb
type CancelExecutionRequest = src.CancelExecutionRequest

// Request for the
// [CreateExecution][google.cloud.workflows.executions.v1beta.Executions.CreateExecution]
// method.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/executions/apiv1beta/executionspb
type CreateExecutionRequest = src.CreateExecutionRequest

// A running instance of a [Workflow][google.cloud.workflows.v1beta.Workflow].
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/executions/apiv1beta/executionspb
type Execution = src.Execution

// Defines possible views for execution resource.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/executions/apiv1beta/executionspb
type ExecutionView = src.ExecutionView

// Error describes why the execution was abnormally terminated.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/executions/apiv1beta/executionspb
type Execution_Error = src.Execution_Error

// Describes the current state of the execution. More states may be added in
// the future.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/executions/apiv1beta/executionspb
type Execution_State = src.Execution_State

// ExecutionsClient is the client API for Executions service. For semantics
// around ctx use and closing/ending streaming RPCs, please refer to
// https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/executions/apiv1beta/executionspb
type ExecutionsClient = src.ExecutionsClient

// ExecutionsServer is the server API for Executions service.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/executions/apiv1beta/executionspb
type ExecutionsServer = src.ExecutionsServer

// Request for the
// [GetExecution][google.cloud.workflows.executions.v1beta.Executions.GetExecution]
// method.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/executions/apiv1beta/executionspb
type GetExecutionRequest = src.GetExecutionRequest

// Request for the
// [ListExecutions][google.cloud.workflows.executions.v1beta.Executions.ListExecutions]
// method.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/executions/apiv1beta/executionspb
type ListExecutionsRequest = src.ListExecutionsRequest

// Response for the
// [ListExecutions][google.cloud.workflows.executions.v1beta.Executions.ListExecutions]
// method.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/executions/apiv1beta/executionspb
type ListExecutionsResponse = src.ListExecutionsResponse

// UnimplementedExecutionsServer can be embedded to have forward compatible
// implementations.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/executions/apiv1beta/executionspb
type UnimplementedExecutionsServer = src.UnimplementedExecutionsServer

// Deprecated: Please use funcs in: cloud.google.com/go/workflows/executions/apiv1beta/executionspb
func NewExecutionsClient(cc grpc.ClientConnInterface) ExecutionsClient {
	return src.NewExecutionsClient(cc)
}

// Deprecated: Please use funcs in: cloud.google.com/go/workflows/executions/apiv1beta/executionspb
func RegisterExecutionsServer(s *grpc.Server, srv ExecutionsServer) {
	src.RegisterExecutionsServer(s, srv)
}
