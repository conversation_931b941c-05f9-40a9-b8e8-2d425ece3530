// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.21.9
// source: google/maps/fleetengine/v1/vehicle_api.proto

package fleetengine

import (
	context "context"
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Specifies the order of the vehicle matches in the response.
type SearchVehiclesRequest_VehicleMatchOrder int32

const (
	// Default, used for unspecified or unrecognized vehicle matches order.
	SearchVehiclesRequest_UNKNOWN_VEHICLE_MATCH_ORDER SearchVehiclesRequest_VehicleMatchOrder = 0
	// Ascending order by vehicle driving time to the pickup point.
	SearchVehiclesRequest_PICKUP_POINT_ETA SearchVehiclesRequest_VehicleMatchOrder = 1
	// Ascending order by vehicle driving distance to the pickup point.
	SearchVehiclesRequest_PICKUP_POINT_DISTANCE SearchVehiclesRequest_VehicleMatchOrder = 2
	// Ascending order by vehicle driving time to the dropoff point. This order
	// can only be used if the dropoff point is specified in the request.
	SearchVehiclesRequest_DROPOFF_POINT_ETA SearchVehiclesRequest_VehicleMatchOrder = 3
	// Ascending order by straight-line distance from the vehicle's last
	// reported location to the pickup point.
	SearchVehiclesRequest_PICKUP_POINT_STRAIGHT_DISTANCE SearchVehiclesRequest_VehicleMatchOrder = 4
	// Ascending order by the configured match cost. Match cost is defined as a
	// weighted calculation between straight-line distance and ETA. Weights are
	// set with default values and can be modified per customer. Please contact
	// Google support if these weights need to be modified for your project.
	SearchVehiclesRequest_COST SearchVehiclesRequest_VehicleMatchOrder = 5
)

// Enum value maps for SearchVehiclesRequest_VehicleMatchOrder.
var (
	SearchVehiclesRequest_VehicleMatchOrder_name = map[int32]string{
		0: "UNKNOWN_VEHICLE_MATCH_ORDER",
		1: "PICKUP_POINT_ETA",
		2: "PICKUP_POINT_DISTANCE",
		3: "DROPOFF_POINT_ETA",
		4: "PICKUP_POINT_STRAIGHT_DISTANCE",
		5: "COST",
	}
	SearchVehiclesRequest_VehicleMatchOrder_value = map[string]int32{
		"UNKNOWN_VEHICLE_MATCH_ORDER":    0,
		"PICKUP_POINT_ETA":               1,
		"PICKUP_POINT_DISTANCE":          2,
		"DROPOFF_POINT_ETA":              3,
		"PICKUP_POINT_STRAIGHT_DISTANCE": 4,
		"COST":                           5,
	}
)

func (x SearchVehiclesRequest_VehicleMatchOrder) Enum() *SearchVehiclesRequest_VehicleMatchOrder {
	p := new(SearchVehiclesRequest_VehicleMatchOrder)
	*p = x
	return p
}

func (x SearchVehiclesRequest_VehicleMatchOrder) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SearchVehiclesRequest_VehicleMatchOrder) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_v1_vehicle_api_proto_enumTypes[0].Descriptor()
}

func (SearchVehiclesRequest_VehicleMatchOrder) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_v1_vehicle_api_proto_enumTypes[0]
}

func (x SearchVehiclesRequest_VehicleMatchOrder) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SearchVehiclesRequest_VehicleMatchOrder.Descriptor instead.
func (SearchVehiclesRequest_VehicleMatchOrder) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescGZIP(), []int{6, 0}
}

// Specifies the types of restrictions on a vehicle's current trips.
type SearchVehiclesRequest_CurrentTripsPresent int32

const (
	// Only vehicles without trips can appear in search results.
	// A validation exception is thrown if `include_back_to_back` is true. See
	// the `include_back_to_back` flag for more details.
	SearchVehiclesRequest_CURRENT_TRIPS_PRESENT_UNSPECIFIED SearchVehiclesRequest_CurrentTripsPresent = 0
	// Vehicles without trips can appear in search results.
	// A validation exception is thrown if `include_back_to_back` is true.
	SearchVehiclesRequest_NONE SearchVehiclesRequest_CurrentTripsPresent = 1
	// Vehicles with at most 5 current trips and 10 waypoints are included
	// in the search results.
	// A validation exception is thrown if `include_back_to_back` is true.
	SearchVehiclesRequest_ANY SearchVehiclesRequest_CurrentTripsPresent = 2
)

// Enum value maps for SearchVehiclesRequest_CurrentTripsPresent.
var (
	SearchVehiclesRequest_CurrentTripsPresent_name = map[int32]string{
		0: "CURRENT_TRIPS_PRESENT_UNSPECIFIED",
		1: "NONE",
		2: "ANY",
	}
	SearchVehiclesRequest_CurrentTripsPresent_value = map[string]int32{
		"CURRENT_TRIPS_PRESENT_UNSPECIFIED": 0,
		"NONE":                              1,
		"ANY":                               2,
	}
)

func (x SearchVehiclesRequest_CurrentTripsPresent) Enum() *SearchVehiclesRequest_CurrentTripsPresent {
	p := new(SearchVehiclesRequest_CurrentTripsPresent)
	*p = x
	return p
}

func (x SearchVehiclesRequest_CurrentTripsPresent) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SearchVehiclesRequest_CurrentTripsPresent) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_v1_vehicle_api_proto_enumTypes[1].Descriptor()
}

func (SearchVehiclesRequest_CurrentTripsPresent) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_v1_vehicle_api_proto_enumTypes[1]
}

func (x SearchVehiclesRequest_CurrentTripsPresent) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SearchVehiclesRequest_CurrentTripsPresent.Descriptor instead.
func (SearchVehiclesRequest_CurrentTripsPresent) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescGZIP(), []int{6, 1}
}

// Type of vehicle match.
type VehicleMatch_VehicleMatchType int32

const (
	// Unknown vehicle match type
	VehicleMatch_UNKNOWN VehicleMatch_VehicleMatchType = 0
	// The vehicle currently has no trip assigned to it and can proceed to the
	// pickup point.
	VehicleMatch_EXCLUSIVE VehicleMatch_VehicleMatchType = 1
	// The vehicle is currently assigned to a trip, but can proceed to the
	// pickup point after completing the in-progress trip.  ETA and distance
	// calculations take the existing trip into account.
	VehicleMatch_BACK_TO_BACK VehicleMatch_VehicleMatchType = 2
	// The vehicle has sufficient capacity for a shared ride.
	VehicleMatch_CARPOOL VehicleMatch_VehicleMatchType = 3
	// The vehicle will finish its current, active trip before proceeding to the
	// pickup point.  ETA and distance calculations take the existing trip into
	// account.
	VehicleMatch_CARPOOL_BACK_TO_BACK VehicleMatch_VehicleMatchType = 4
)

// Enum value maps for VehicleMatch_VehicleMatchType.
var (
	VehicleMatch_VehicleMatchType_name = map[int32]string{
		0: "UNKNOWN",
		1: "EXCLUSIVE",
		2: "BACK_TO_BACK",
		3: "CARPOOL",
		4: "CARPOOL_BACK_TO_BACK",
	}
	VehicleMatch_VehicleMatchType_value = map[string]int32{
		"UNKNOWN":              0,
		"EXCLUSIVE":            1,
		"BACK_TO_BACK":         2,
		"CARPOOL":              3,
		"CARPOOL_BACK_TO_BACK": 4,
	}
)

func (x VehicleMatch_VehicleMatchType) Enum() *VehicleMatch_VehicleMatchType {
	p := new(VehicleMatch_VehicleMatchType)
	*p = x
	return p
}

func (x VehicleMatch_VehicleMatchType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VehicleMatch_VehicleMatchType) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_v1_vehicle_api_proto_enumTypes[2].Descriptor()
}

func (VehicleMatch_VehicleMatchType) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_v1_vehicle_api_proto_enumTypes[2]
}

func (x VehicleMatch_VehicleMatchType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VehicleMatch_VehicleMatchType.Descriptor instead.
func (VehicleMatch_VehicleMatchType) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescGZIP(), []int{11, 0}
}

// `CreateVehicle` request message.
type CreateVehicleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The standard Fleet Engine request header.
	Header *RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Required. Must be in the format `providers/{provider}`.
	// The provider must be the Project ID (for example, `sample-cloud-project`)
	// of the Google Cloud Project of which the service account making
	// this call is a member.
	Parent string `protobuf:"bytes,3,opt,name=parent,proto3" json:"parent,omitempty"`
	// Required. Unique Vehicle ID.
	// Subject to the following restrictions:
	//
	// * Must be a valid Unicode string.
	// * Limited to a maximum length of 64 characters.
	// * Normalized according to [Unicode Normalization Form C]
	// (http://www.unicode.org/reports/tr15/).
	// * May not contain any of the following ASCII characters: '/', ':', '?',
	// ',', or '#'.
	VehicleId string `protobuf:"bytes,4,opt,name=vehicle_id,json=vehicleId,proto3" json:"vehicle_id,omitempty"`
	// Required. The Vehicle entity to create. When creating a Vehicle, the
	// following fields are required:
	//
	// * `vehicleState`
	// * `supportedTripTypes`
	// * `maximumCapacity`
	// * `vehicleType`
	//
	// When creating a Vehicle, the following fields are ignored:
	//
	// * `name`
	// * `currentTrips`
	// * `availableCapacity`
	// * `current_route_segment`
	// * `current_route_segment_end_point`
	// * `current_route_segment_version`
	// * `current_route_segment_traffic`
	// * `route`
	// * `waypoints`
	// * `waypoints_version`
	// * `remaining_distance_meters`
	// * `remaining_time_seconds`
	// * `eta_to_next_waypoint`
	// * `navigation_status`
	//
	// All other fields are optional and used if provided.
	Vehicle *Vehicle `protobuf:"bytes,5,opt,name=vehicle,proto3" json:"vehicle,omitempty"`
}

func (x *CreateVehicleRequest) Reset() {
	*x = CreateVehicleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateVehicleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateVehicleRequest) ProtoMessage() {}

func (x *CreateVehicleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateVehicleRequest.ProtoReflect.Descriptor instead.
func (*CreateVehicleRequest) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescGZIP(), []int{0}
}

func (x *CreateVehicleRequest) GetHeader() *RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CreateVehicleRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *CreateVehicleRequest) GetVehicleId() string {
	if x != nil {
		return x.VehicleId
	}
	return ""
}

func (x *CreateVehicleRequest) GetVehicle() *Vehicle {
	if x != nil {
		return x.Vehicle
	}
	return nil
}

// `GetVehicle` request message.
type GetVehicleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The standard Fleet Engine request header.
	Header *RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Required. Must be in the format
	// `providers/{provider}/vehicles/{vehicle}`.
	// The provider must be the Project ID (for example, `sample-cloud-project`)
	// of the Google Cloud Project of which the service account making
	// this call is a member.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// Indicates the minimum timestamp (exclusive) for which
	// `Vehicle.current_route_segment` is retrieved.
	// If the route is unchanged since this timestamp, the `current_route_segment`
	// field is not set in the response. If a minimum is unspecified, the
	// `current_route_segment` is always retrieved.
	CurrentRouteSegmentVersion *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=current_route_segment_version,json=currentRouteSegmentVersion,proto3" json:"current_route_segment_version,omitempty"`
	// Indicates the minimum timestamp (exclusive) for which `Vehicle.waypoints`
	// data is retrieved. If the waypoints are unchanged since this timestamp, the
	// `vehicle.waypoints` data is not set in the response. If this field is
	// unspecified, `vehicle.waypoints` is always retrieved.
	WaypointsVersion *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=waypoints_version,json=waypointsVersion,proto3" json:"waypoints_version,omitempty"`
}

func (x *GetVehicleRequest) Reset() {
	*x = GetVehicleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVehicleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVehicleRequest) ProtoMessage() {}

func (x *GetVehicleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVehicleRequest.ProtoReflect.Descriptor instead.
func (*GetVehicleRequest) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetVehicleRequest) GetHeader() *RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetVehicleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetVehicleRequest) GetCurrentRouteSegmentVersion() *timestamppb.Timestamp {
	if x != nil {
		return x.CurrentRouteSegmentVersion
	}
	return nil
}

func (x *GetVehicleRequest) GetWaypointsVersion() *timestamppb.Timestamp {
	if x != nil {
		return x.WaypointsVersion
	}
	return nil
}

// `UpdateVehicle request message.
type UpdateVehicleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The standard Fleet Engine request header.
	Header *RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Required. Must be in the format
	// `providers/{provider}/vehicles/{vehicle}`.
	// The {provider} must be the Project ID (for example, `sample-cloud-project`)
	// of the Google Cloud Project of which the service account making
	// this call is a member.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// Required. The `Vehicle` entity values to apply.  When updating a `Vehicle`,
	// the following fields may not be updated as they are managed by the
	// server.
	//
	// * `current_trips`
	// * `available_capacity`
	// * `current_route_segment_version`
	// * `waypoints_version`
	//
	// Furthermore, the vehicle `name` cannot be updated.
	//
	// If the `attributes` field is updated, **all** the vehicle's attributes are
	// replaced with the attributes provided in the request. If you want to update
	// only some attributes, see the `UpdateVehicleAttributes` method. Likewise,
	// the `waypoints` field can be updated, but must contain all the waypoints.
	// currently on the vehicle, and no other waypoints.
	Vehicle *Vehicle `protobuf:"bytes,4,opt,name=vehicle,proto3" json:"vehicle,omitempty"`
	// Required. A field mask indicating which fields of the `Vehicle` to update.
	// At least one field name must be provided.
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,5,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *UpdateVehicleRequest) Reset() {
	*x = UpdateVehicleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateVehicleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVehicleRequest) ProtoMessage() {}

func (x *UpdateVehicleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVehicleRequest.ProtoReflect.Descriptor instead.
func (*UpdateVehicleRequest) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateVehicleRequest) GetHeader() *RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *UpdateVehicleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateVehicleRequest) GetVehicle() *Vehicle {
	if x != nil {
		return x.Vehicle
	}
	return nil
}

func (x *UpdateVehicleRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// `UpdateVehicleLocation` request message.
//
// Deprecated: Do not use.
type UpdateVehicleLocationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The standard Fleet Engine request header.
	Header *RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Required. Must be in the format
	// `providers/{provider}/vehicles/{vehicle}`.
	// The {provider} must be the Project ID (for example, `sample-cloud-project`)
	// of the Google Cloud Project of which the service account making
	// this call is a member.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// Required. The vehicle's most recent location.  The `location` and
	// `update_time` subfields are required.
	CurrentLocation *VehicleLocation `protobuf:"bytes,4,opt,name=current_location,json=currentLocation,proto3" json:"current_location,omitempty"`
	// Set the vehicle's state to either `ONLINE` or `OFFLINE`.
	// If set to `UNKNOWN_VEHICLE_STATE`, the vehicle's state will not be altered.
	CurrentState VehicleState `protobuf:"varint,5,opt,name=current_state,json=currentState,proto3,enum=maps.fleetengine.v1.VehicleState" json:"current_state,omitempty"`
}

func (x *UpdateVehicleLocationRequest) Reset() {
	*x = UpdateVehicleLocationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateVehicleLocationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVehicleLocationRequest) ProtoMessage() {}

func (x *UpdateVehicleLocationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVehicleLocationRequest.ProtoReflect.Descriptor instead.
func (*UpdateVehicleLocationRequest) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateVehicleLocationRequest) GetHeader() *RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *UpdateVehicleLocationRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateVehicleLocationRequest) GetCurrentLocation() *VehicleLocation {
	if x != nil {
		return x.CurrentLocation
	}
	return nil
}

func (x *UpdateVehicleLocationRequest) GetCurrentState() VehicleState {
	if x != nil {
		return x.CurrentState
	}
	return VehicleState_UNKNOWN_VEHICLE_STATE
}

// `UpdateVehicleAttributes` request message.
type UpdateVehicleAttributesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The standard Fleet Engine request header.
	Header *RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Required. Must be in the format `providers/{provider}/vehicles/{vehicle}`.
	// The provider must be the Project ID (for example, `sample-cloud-project`)
	// of the Google Cloud Project of which the service account making
	// this call is a member.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// Required. The vehicle attributes to update. Unmentioned attributes are not
	// altered or removed.
	Attributes []*VehicleAttribute `protobuf:"bytes,4,rep,name=attributes,proto3" json:"attributes,omitempty"`
}

func (x *UpdateVehicleAttributesRequest) Reset() {
	*x = UpdateVehicleAttributesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateVehicleAttributesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVehicleAttributesRequest) ProtoMessage() {}

func (x *UpdateVehicleAttributesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVehicleAttributesRequest.ProtoReflect.Descriptor instead.
func (*UpdateVehicleAttributesRequest) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateVehicleAttributesRequest) GetHeader() *RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *UpdateVehicleAttributesRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateVehicleAttributesRequest) GetAttributes() []*VehicleAttribute {
	if x != nil {
		return x.Attributes
	}
	return nil
}

// `UpdateVehicleAttributes` response message.
type UpdateVehicleAttributesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The updated full list of vehicle attributes, including new,
	// altered, and untouched attributes.
	Attributes []*VehicleAttribute `protobuf:"bytes,1,rep,name=attributes,proto3" json:"attributes,omitempty"`
}

func (x *UpdateVehicleAttributesResponse) Reset() {
	*x = UpdateVehicleAttributesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateVehicleAttributesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVehicleAttributesResponse) ProtoMessage() {}

func (x *UpdateVehicleAttributesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVehicleAttributesResponse.ProtoReflect.Descriptor instead.
func (*UpdateVehicleAttributesResponse) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateVehicleAttributesResponse) GetAttributes() []*VehicleAttribute {
	if x != nil {
		return x.Attributes
	}
	return nil
}

// `SearchVehicles` request message.
type SearchVehiclesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The standard Fleet Engine request header.
	Header *RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Required. Must be in the format `providers/{provider}`.
	// The provider must be the Project ID (for example, `sample-cloud-project`)
	// of the Google Cloud Project of which the service account making
	// this call is a member.
	Parent string `protobuf:"bytes,3,opt,name=parent,proto3" json:"parent,omitempty"`
	// Required. The pickup point to search near.
	PickupPoint *TerminalLocation `protobuf:"bytes,4,opt,name=pickup_point,json=pickupPoint,proto3" json:"pickup_point,omitempty"`
	// The customer's intended dropoff location. The field is required if
	// `trip_types` contains `TripType.SHARED`.
	DropoffPoint *TerminalLocation `protobuf:"bytes,5,opt,name=dropoff_point,json=dropoffPoint,proto3" json:"dropoff_point,omitempty"`
	// Required. Defines the vehicle search radius around the pickup point. Only
	// vehicles within the search radius will be returned. Value must be between
	// 400 and 10000 meters (inclusive).
	PickupRadiusMeters int32 `protobuf:"varint,6,opt,name=pickup_radius_meters,json=pickupRadiusMeters,proto3" json:"pickup_radius_meters,omitempty"`
	// Required. Specifies the maximum number of vehicles to return. The value
	// must be between 1 and 50 (inclusive).
	Count int32 `protobuf:"varint,7,opt,name=count,proto3" json:"count,omitempty"`
	// Required. Specifies the number of passengers being considered for a trip.
	// The value must be greater than or equal to one. The driver is not
	// considered in the capacity value.
	MinimumCapacity int32 `protobuf:"varint,8,opt,name=minimum_capacity,json=minimumCapacity,proto3" json:"minimum_capacity,omitempty"`
	// Required. Represents the type of proposed trip. Eligible vehicles are those
	// that can support at least one of the specified trip type.
	//
	// `EXCLUSIVE` and `SHARED` may not be included together.
	// `SHARED` is not supported when `current_trips_present` is
	// `CURRENT_TRIPS_PRESENT_UNSPECIFIED`.
	TripTypes []TripType `protobuf:"varint,9,rep,packed,name=trip_types,json=tripTypes,proto3,enum=maps.fleetengine.v1.TripType" json:"trip_types,omitempty"`
	// Restricts the search to only those vehicles that have updated their
	// locations within the specified duration. If this field is not
	// set, the server uses five minutes as the default value.
	MaximumStaleness *durationpb.Duration `protobuf:"bytes,10,opt,name=maximum_staleness,json=maximumStaleness,proto3" json:"maximum_staleness,omitempty"`
	// Required. Restricts the search to vehicles with one of the specified types.
	// At least one vehicle type must be specified.
	VehicleTypes []*Vehicle_VehicleType `protobuf:"bytes,14,rep,name=vehicle_types,json=vehicleTypes,proto3" json:"vehicle_types,omitempty"`
	// Callers can form complex logical operations using any combination of the
	// `required_attributes`, `required_one_of_attributes`, and
	// `required_one_of_attribute_sets` fields.
	//
	// `required_attributes` is a list; `required_one_of_attributes` uses a
	// message which allows a list of lists. In combination, the two fields allow
	// the composition of this expression:
	//
	// ```
	// (required_attributes[0] AND required_attributes[1] AND ...)
	// AND
	// (required_one_of_attributes[0][0] OR required_one_of_attributes[0][1] OR
	// ...)
	// AND
	// (required_one_of_attributes[1][0] OR required_one_of_attributes[1][1] OR
	// ...)
	// ```
	//
	// Restricts the search to only those vehicles with the specified attributes.
	// This field is a conjunction/AND operation. A max of 50 required_attributes
	// is allowed. This matches the maximum number of attributes allowed on a
	// vehicle.
	RequiredAttributes []*VehicleAttribute `protobuf:"bytes,12,rep,name=required_attributes,json=requiredAttributes,proto3" json:"required_attributes,omitempty"`
	// Restricts the search to only those vehicles with at least one of
	// the specified attributes in each `VehicleAttributeList`. Within each
	// list, a vehicle must match at least one of the attributes. This field is an
	// inclusive disjunction/OR operation in each `VehicleAttributeList` and a
	// conjunction/AND operation across the collection of `VehicleAttributeList`.
	RequiredOneOfAttributes []*VehicleAttributeList `protobuf:"bytes,15,rep,name=required_one_of_attributes,json=requiredOneOfAttributes,proto3" json:"required_one_of_attributes,omitempty"`
	// `required_one_of_attribute_sets` provides additional functionality.
	//
	// Similar to `required_one_of_attributes`, `required_one_of_attribute_sets`
	// uses a message which allows a list of lists, allowing expressions such as
	// this one:
	//
	// ```
	// (required_attributes[0] AND required_attributes[1] AND ...)
	// AND
	// (required_one_of_attribute_sets[0][0] AND
	// required_one_of_attribute_sets[0][1] AND
	// ...)
	// OR
	// (required_one_of_attribute_sets[1][0] AND
	// required_one_of_attribute_sets[1][1] AND
	// ...)
	// ```
	//
	// Restricts the search to only those vehicles with all the attributes in a
	// `VehicleAttributeList`. Within each list, a
	// vehicle must match all of the attributes. This field is a conjunction/AND
	// operation in each `VehicleAttributeList` and inclusive disjunction/OR
	// operation across the collection of `VehicleAttributeList`.
	RequiredOneOfAttributeSets []*VehicleAttributeList `protobuf:"bytes,20,rep,name=required_one_of_attribute_sets,json=requiredOneOfAttributeSets,proto3" json:"required_one_of_attribute_sets,omitempty"`
	// Required. Specifies the desired ordering criterion for results.
	OrderBy SearchVehiclesRequest_VehicleMatchOrder `protobuf:"varint,13,opt,name=order_by,json=orderBy,proto3,enum=maps.fleetengine.v1.SearchVehiclesRequest_VehicleMatchOrder" json:"order_by,omitempty"`
	// Indicates if a vehicle with a single active trip is eligible for another
	// match. If `false`, vehicles with assigned trips are excluded from the
	// search results. If `true`, search results include vehicles with
	// `TripStatus` of `ENROUTE_TO_DROPOFF`.
	//
	// This field is only considered if a single `trip_type` of `EXCLUSIVE` is
	// specified.
	//
	// The default value is `false`.
	IncludeBackToBack bool `protobuf:"varint,18,opt,name=include_back_to_back,json=includeBackToBack,proto3" json:"include_back_to_back,omitempty"`
	// Indicates the trip associated with this `SearchVehicleRequest`.
	TripId string `protobuf:"bytes,19,opt,name=trip_id,json=tripId,proto3" json:"trip_id,omitempty"`
	// Restricts vehicles from appearing in the search results based on
	// their current trips.
	//
	// When current_trips_present is `NONE` or `ANY`, `trip_types` can be either
	// `EXCLUSIVE` or `SHARED`, but not both.
	CurrentTripsPresent SearchVehiclesRequest_CurrentTripsPresent `protobuf:"varint,21,opt,name=current_trips_present,json=currentTripsPresent,proto3,enum=maps.fleetengine.v1.SearchVehiclesRequest_CurrentTripsPresent" json:"current_trips_present,omitempty"`
}

func (x *SearchVehiclesRequest) Reset() {
	*x = SearchVehiclesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchVehiclesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchVehiclesRequest) ProtoMessage() {}

func (x *SearchVehiclesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchVehiclesRequest.ProtoReflect.Descriptor instead.
func (*SearchVehiclesRequest) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescGZIP(), []int{6}
}

func (x *SearchVehiclesRequest) GetHeader() *RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SearchVehiclesRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *SearchVehiclesRequest) GetPickupPoint() *TerminalLocation {
	if x != nil {
		return x.PickupPoint
	}
	return nil
}

func (x *SearchVehiclesRequest) GetDropoffPoint() *TerminalLocation {
	if x != nil {
		return x.DropoffPoint
	}
	return nil
}

func (x *SearchVehiclesRequest) GetPickupRadiusMeters() int32 {
	if x != nil {
		return x.PickupRadiusMeters
	}
	return 0
}

func (x *SearchVehiclesRequest) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *SearchVehiclesRequest) GetMinimumCapacity() int32 {
	if x != nil {
		return x.MinimumCapacity
	}
	return 0
}

func (x *SearchVehiclesRequest) GetTripTypes() []TripType {
	if x != nil {
		return x.TripTypes
	}
	return nil
}

func (x *SearchVehiclesRequest) GetMaximumStaleness() *durationpb.Duration {
	if x != nil {
		return x.MaximumStaleness
	}
	return nil
}

func (x *SearchVehiclesRequest) GetVehicleTypes() []*Vehicle_VehicleType {
	if x != nil {
		return x.VehicleTypes
	}
	return nil
}

func (x *SearchVehiclesRequest) GetRequiredAttributes() []*VehicleAttribute {
	if x != nil {
		return x.RequiredAttributes
	}
	return nil
}

func (x *SearchVehiclesRequest) GetRequiredOneOfAttributes() []*VehicleAttributeList {
	if x != nil {
		return x.RequiredOneOfAttributes
	}
	return nil
}

func (x *SearchVehiclesRequest) GetRequiredOneOfAttributeSets() []*VehicleAttributeList {
	if x != nil {
		return x.RequiredOneOfAttributeSets
	}
	return nil
}

func (x *SearchVehiclesRequest) GetOrderBy() SearchVehiclesRequest_VehicleMatchOrder {
	if x != nil {
		return x.OrderBy
	}
	return SearchVehiclesRequest_UNKNOWN_VEHICLE_MATCH_ORDER
}

func (x *SearchVehiclesRequest) GetIncludeBackToBack() bool {
	if x != nil {
		return x.IncludeBackToBack
	}
	return false
}

func (x *SearchVehiclesRequest) GetTripId() string {
	if x != nil {
		return x.TripId
	}
	return ""
}

func (x *SearchVehiclesRequest) GetCurrentTripsPresent() SearchVehiclesRequest_CurrentTripsPresent {
	if x != nil {
		return x.CurrentTripsPresent
	}
	return SearchVehiclesRequest_CURRENT_TRIPS_PRESENT_UNSPECIFIED
}

// `SearchVehicles` response message.
type SearchVehiclesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// List of vehicles that match the `SearchVehiclesRequest` criteria, ordered
	// according to `SearchVehiclesRequest.order_by` field.
	Matches []*VehicleMatch `protobuf:"bytes,1,rep,name=matches,proto3" json:"matches,omitempty"`
}

func (x *SearchVehiclesResponse) Reset() {
	*x = SearchVehiclesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchVehiclesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchVehiclesResponse) ProtoMessage() {}

func (x *SearchVehiclesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchVehiclesResponse.ProtoReflect.Descriptor instead.
func (*SearchVehiclesResponse) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescGZIP(), []int{7}
}

func (x *SearchVehiclesResponse) GetMatches() []*VehicleMatch {
	if x != nil {
		return x.Matches
	}
	return nil
}

// `ListVehicles` request message.
type ListVehiclesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The standard Fleet Engine request header.
	Header *RequestHeader `protobuf:"bytes,12,opt,name=header,proto3" json:"header,omitempty"`
	// Required. Must be in the format `providers/{provider}`.
	// The provider must be the Project ID (for example, `sample-cloud-project`)
	// of the Google Cloud Project of which the service account making
	// this call is a member.
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// The maximum number of vehicles to return.
	// Default value: 100.
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// The value of the `next_page_token` provided by a previous call to
	// `ListVehicles` so that you can paginate through groups of vehicles. The
	// value is undefined if the filter criteria of the request is not the same as
	// the filter criteria for the previous call to `ListVehicles`.
	PageToken string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// Specifies the required minimum capacity of the vehicle. All vehicles
	// returned will have a `maximum_capacity` greater than or equal to this
	// value. If set, must be greater or equal to 0.
	MinimumCapacity *wrapperspb.Int32Value `protobuf:"bytes,6,opt,name=minimum_capacity,json=minimumCapacity,proto3" json:"minimum_capacity,omitempty"`
	// Restricts the response to vehicles that support at least one of the
	// specified trip types.
	TripTypes []TripType `protobuf:"varint,7,rep,packed,name=trip_types,json=tripTypes,proto3,enum=maps.fleetengine.v1.TripType" json:"trip_types,omitempty"`
	// Restricts the response to vehicles that have updated their locations within
	// the specified duration at the time of the call. If present, must be a valid
	// positive duration.
	MaximumStaleness *durationpb.Duration `protobuf:"bytes,8,opt,name=maximum_staleness,json=maximumStaleness,proto3" json:"maximum_staleness,omitempty"`
	// Required. Restricts the response to vehicles with one of the specified type
	// categories.
	VehicleTypeCategories []Vehicle_VehicleType_Category `protobuf:"varint,9,rep,packed,name=vehicle_type_categories,json=vehicleTypeCategories,proto3,enum=maps.fleetengine.v1.Vehicle_VehicleType_Category" json:"vehicle_type_categories,omitempty"`
	// Callers can form complex logical operations using any combination of the
	// `required_attributes`, `required_one_of_attributes`, and
	// `required_one_of_attribute_sets` fields.
	//
	// `required_attributes` is a list; `required_one_of_attributes` uses a
	// message which allows a list of lists. In combination, the two fields allow
	// the composition of this expression:
	//
	// ```
	// (required_attributes[0] AND required_attributes[1] AND ...)
	// AND
	// (required_one_of_attributes[0][0] OR required_one_of_attributes[0][1] OR
	// ...)
	// AND
	// (required_one_of_attributes[1][0] OR required_one_of_attributes[1][1] OR
	// ...)
	// ```
	//
	// Restricts the response to vehicles with the specified attributes. This
	// field is a conjunction/AND operation. Your app can specify up to 100
	// attributes; however, the combined key:value string length cannot exceed
	// 1024 characters.
	RequiredAttributes []string `protobuf:"bytes,10,rep,name=required_attributes,json=requiredAttributes,proto3" json:"required_attributes,omitempty"`
	// Restricts the response to vehicles with at least one of the specified
	// attributes in each `VehicleAttributeList`. Within each list, a vehicle must
	// match at least one of the attributes. This field is an inclusive
	// disjunction/OR operation in each `VehicleAttributeList` and a
	// conjunction/AND operation across the collection of `VehicleAttributeList`.
	// Format: key1:value1|key2:value2|key3:value3...
	RequiredOneOfAttributes []string `protobuf:"bytes,13,rep,name=required_one_of_attributes,json=requiredOneOfAttributes,proto3" json:"required_one_of_attributes,omitempty"`
	// `required_one_of_attribute_sets` provides additional functionality.
	//
	// Similar to `required_one_of_attributes`, `required_one_of_attribute_sets`
	// uses a message which allows a list of lists, allowing expressions such as
	// this one:
	//
	// ```
	// (required_attributes[0] AND required_attributes[1] AND ...)
	// AND
	// (required_one_of_attributes[0][0] AND required_one_of_attributes[0][1] AND
	// ...)
	// OR
	// (required_one_of_attributes[1][0] AND required_one_of_attributes[1][1] AND
	// ...)
	// ```
	//
	// Restricts the response to vehicles that match all the attributes in a
	// `VehicleAttributeList`. Within each list, a vehicle must match all of the
	// attributes. This field is a conjunction/AND operation in each
	// `VehicleAttributeList` and inclusive disjunction/OR operation across the
	// collection of `VehicleAttributeList`. Each repeated string should be of the
	// format "key1:value1|key2:value2|key3:value3".
	RequiredOneOfAttributeSets []string `protobuf:"bytes,15,rep,name=required_one_of_attribute_sets,json=requiredOneOfAttributeSets,proto3" json:"required_one_of_attribute_sets,omitempty"`
	// Restricts the response to vehicles that have this vehicle state.
	VehicleState VehicleState `protobuf:"varint,11,opt,name=vehicle_state,json=vehicleState,proto3,enum=maps.fleetengine.v1.VehicleState" json:"vehicle_state,omitempty"`
	// Only return the vehicles with current trip(s).
	OnTripOnly bool `protobuf:"varint,14,opt,name=on_trip_only,json=onTripOnly,proto3" json:"on_trip_only,omitempty"`
}

func (x *ListVehiclesRequest) Reset() {
	*x = ListVehiclesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListVehiclesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVehiclesRequest) ProtoMessage() {}

func (x *ListVehiclesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVehiclesRequest.ProtoReflect.Descriptor instead.
func (*ListVehiclesRequest) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescGZIP(), []int{8}
}

func (x *ListVehiclesRequest) GetHeader() *RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ListVehiclesRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *ListVehiclesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListVehiclesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListVehiclesRequest) GetMinimumCapacity() *wrapperspb.Int32Value {
	if x != nil {
		return x.MinimumCapacity
	}
	return nil
}

func (x *ListVehiclesRequest) GetTripTypes() []TripType {
	if x != nil {
		return x.TripTypes
	}
	return nil
}

func (x *ListVehiclesRequest) GetMaximumStaleness() *durationpb.Duration {
	if x != nil {
		return x.MaximumStaleness
	}
	return nil
}

func (x *ListVehiclesRequest) GetVehicleTypeCategories() []Vehicle_VehicleType_Category {
	if x != nil {
		return x.VehicleTypeCategories
	}
	return nil
}

func (x *ListVehiclesRequest) GetRequiredAttributes() []string {
	if x != nil {
		return x.RequiredAttributes
	}
	return nil
}

func (x *ListVehiclesRequest) GetRequiredOneOfAttributes() []string {
	if x != nil {
		return x.RequiredOneOfAttributes
	}
	return nil
}

func (x *ListVehiclesRequest) GetRequiredOneOfAttributeSets() []string {
	if x != nil {
		return x.RequiredOneOfAttributeSets
	}
	return nil
}

func (x *ListVehiclesRequest) GetVehicleState() VehicleState {
	if x != nil {
		return x.VehicleState
	}
	return VehicleState_UNKNOWN_VEHICLE_STATE
}

func (x *ListVehiclesRequest) GetOnTripOnly() bool {
	if x != nil {
		return x.OnTripOnly
	}
	return false
}

// `ListVehicles` response message.
type ListVehiclesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Vehicles matching the criteria in the request.
	// The maximum number of vehicles returned is determined by the `page_size`
	// field in the request.
	Vehicles []*Vehicle `protobuf:"bytes,1,rep,name=vehicles,proto3" json:"vehicles,omitempty"`
	// Token to retrieve the next page of vehicles, or empty if there are no
	// more vehicles that meet the request criteria.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// Required. Total number of vehicles matching the request criteria across all
	// pages.
	TotalSize int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
}

func (x *ListVehiclesResponse) Reset() {
	*x = ListVehiclesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListVehiclesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVehiclesResponse) ProtoMessage() {}

func (x *ListVehiclesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVehiclesResponse.ProtoReflect.Descriptor instead.
func (*ListVehiclesResponse) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescGZIP(), []int{9}
}

func (x *ListVehiclesResponse) GetVehicles() []*Vehicle {
	if x != nil {
		return x.Vehicles
	}
	return nil
}

func (x *ListVehiclesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListVehiclesResponse) GetTotalSize() int64 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

// Describes intermediate points along a route.
type Waypoint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The location of this waypoint.
	LatLng *latlng.LatLng `protobuf:"bytes,1,opt,name=lat_lng,json=latLng,proto3" json:"lat_lng,omitempty"`
	// The estimated time that the vehicle will arrive at this waypoint.
	Eta *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=eta,proto3" json:"eta,omitempty"`
}

func (x *Waypoint) Reset() {
	*x = Waypoint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Waypoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Waypoint) ProtoMessage() {}

func (x *Waypoint) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Waypoint.ProtoReflect.Descriptor instead.
func (*Waypoint) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescGZIP(), []int{10}
}

func (x *Waypoint) GetLatLng() *latlng.LatLng {
	if x != nil {
		return x.LatLng
	}
	return nil
}

func (x *Waypoint) GetEta() *timestamppb.Timestamp {
	if x != nil {
		return x.Eta
	}
	return nil
}

// Contains the vehicle and related estimates for a vehicle that match the
// points of active trips for the vehicle `SearchVehiclesRequest`.
type VehicleMatch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. A vehicle that matches the request.
	Vehicle *Vehicle `protobuf:"bytes,1,opt,name=vehicle,proto3" json:"vehicle,omitempty"`
	// The vehicle's driving ETA to the pickup point specified in the
	// request. An empty value indicates a failure in calculating ETA for the
	// vehicle.  If `SearchVehiclesRequest.include_back_to_back` was `true` and
	// this vehicle has an active trip, `vehicle_pickup_eta` includes the time
	// required to complete the current active trip.
	VehiclePickupEta *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=vehicle_pickup_eta,json=vehiclePickupEta,proto3" json:"vehicle_pickup_eta,omitempty"`
	// The distance from the Vehicle's current location to the pickup point
	// specified in the request, including any intermediate pickup or dropoff
	// points for existing trips. This distance comprises the calculated driving
	// (route) distance, plus the straight line distance between the navigation
	// end point and the requested pickup point. (The distance between the
	// navigation end point and the requested pickup point is typically small.) An
	// empty value indicates an error in calculating the distance.
	VehiclePickupDistanceMeters *wrapperspb.Int32Value `protobuf:"bytes,3,opt,name=vehicle_pickup_distance_meters,json=vehiclePickupDistanceMeters,proto3" json:"vehicle_pickup_distance_meters,omitempty"`
	// Required. The straight-line distance between the vehicle and the pickup
	// point specified in the request.
	VehiclePickupStraightLineDistanceMeters *wrapperspb.Int32Value `protobuf:"bytes,11,opt,name=vehicle_pickup_straight_line_distance_meters,json=vehiclePickupStraightLineDistanceMeters,proto3" json:"vehicle_pickup_straight_line_distance_meters,omitempty"`
	// The complete vehicle's driving ETA to the drop off point specified in the
	// request. The ETA includes stopping at any waypoints before the
	// `dropoff_point` specified in the request. The value will only be populated
	// when a drop off point is specified in the request. An empty value indicates
	// an error calculating the ETA.
	VehicleDropoffEta *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=vehicle_dropoff_eta,json=vehicleDropoffEta,proto3" json:"vehicle_dropoff_eta,omitempty"`
	// The vehicle's driving distance (in meters) from the pickup point
	// to the drop off point specified in the request. The distance is only
	// between the two points and does not include the vehicle location or any
	// other points that must be visited before the vehicle visits either the
	// pickup point or dropoff point. The value will only be populated when a
	// `dropoff_point` is specified in the request. An empty value indicates
	// a failure in calculating the distance from the pickup to
	// drop off point specified in the request.
	VehiclePickupToDropoffDistanceMeters *wrapperspb.Int32Value `protobuf:"bytes,5,opt,name=vehicle_pickup_to_dropoff_distance_meters,json=vehiclePickupToDropoffDistanceMeters,proto3" json:"vehicle_pickup_to_dropoff_distance_meters,omitempty"`
	// Required. The trip type of the request that was used to calculate the ETA
	// to the pickup point.
	TripType TripType `protobuf:"varint,6,opt,name=trip_type,json=tripType,proto3,enum=maps.fleetengine.v1.TripType" json:"trip_type,omitempty"`
	// The ordered list of waypoints used to calculate the ETA. The list
	// includes vehicle location, the pickup points of active
	// trips for the vehicle, and the pickup points provided in the
	// request. An empty list indicates a failure in calculating ETA for the
	// vehicle.
	VehicleTripsWaypoints []*Waypoint `protobuf:"bytes,7,rep,name=vehicle_trips_waypoints,json=vehicleTripsWaypoints,proto3" json:"vehicle_trips_waypoints,omitempty"`
	// Type of the vehicle match.
	VehicleMatchType VehicleMatch_VehicleMatchType `protobuf:"varint,8,opt,name=vehicle_match_type,json=vehicleMatchType,proto3,enum=maps.fleetengine.v1.VehicleMatch_VehicleMatchType" json:"vehicle_match_type,omitempty"`
	// The order requested for sorting vehicle matches.
	RequestedOrderedBy SearchVehiclesRequest_VehicleMatchOrder `protobuf:"varint,9,opt,name=requested_ordered_by,json=requestedOrderedBy,proto3,enum=maps.fleetengine.v1.SearchVehiclesRequest_VehicleMatchOrder" json:"requested_ordered_by,omitempty"`
	// The actual order that was used for this vehicle. Normally this
	// will match the 'order_by' field from the request; however, in certain
	// circumstances such as an internal server error, a different method
	// may be used (such as `PICKUP_POINT_STRAIGHT_DISTANCE`).
	OrderedBy SearchVehiclesRequest_VehicleMatchOrder `protobuf:"varint,10,opt,name=ordered_by,json=orderedBy,proto3,enum=maps.fleetengine.v1.SearchVehiclesRequest_VehicleMatchOrder" json:"ordered_by,omitempty"`
}

func (x *VehicleMatch) Reset() {
	*x = VehicleMatch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VehicleMatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VehicleMatch) ProtoMessage() {}

func (x *VehicleMatch) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VehicleMatch.ProtoReflect.Descriptor instead.
func (*VehicleMatch) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescGZIP(), []int{11}
}

func (x *VehicleMatch) GetVehicle() *Vehicle {
	if x != nil {
		return x.Vehicle
	}
	return nil
}

func (x *VehicleMatch) GetVehiclePickupEta() *timestamppb.Timestamp {
	if x != nil {
		return x.VehiclePickupEta
	}
	return nil
}

func (x *VehicleMatch) GetVehiclePickupDistanceMeters() *wrapperspb.Int32Value {
	if x != nil {
		return x.VehiclePickupDistanceMeters
	}
	return nil
}

func (x *VehicleMatch) GetVehiclePickupStraightLineDistanceMeters() *wrapperspb.Int32Value {
	if x != nil {
		return x.VehiclePickupStraightLineDistanceMeters
	}
	return nil
}

func (x *VehicleMatch) GetVehicleDropoffEta() *timestamppb.Timestamp {
	if x != nil {
		return x.VehicleDropoffEta
	}
	return nil
}

func (x *VehicleMatch) GetVehiclePickupToDropoffDistanceMeters() *wrapperspb.Int32Value {
	if x != nil {
		return x.VehiclePickupToDropoffDistanceMeters
	}
	return nil
}

func (x *VehicleMatch) GetTripType() TripType {
	if x != nil {
		return x.TripType
	}
	return TripType_UNKNOWN_TRIP_TYPE
}

func (x *VehicleMatch) GetVehicleTripsWaypoints() []*Waypoint {
	if x != nil {
		return x.VehicleTripsWaypoints
	}
	return nil
}

func (x *VehicleMatch) GetVehicleMatchType() VehicleMatch_VehicleMatchType {
	if x != nil {
		return x.VehicleMatchType
	}
	return VehicleMatch_UNKNOWN
}

func (x *VehicleMatch) GetRequestedOrderedBy() SearchVehiclesRequest_VehicleMatchOrder {
	if x != nil {
		return x.RequestedOrderedBy
	}
	return SearchVehiclesRequest_UNKNOWN_VEHICLE_MATCH_ORDER
}

func (x *VehicleMatch) GetOrderedBy() SearchVehiclesRequest_VehicleMatchOrder {
	if x != nil {
		return x.OrderedBy
	}
	return SearchVehiclesRequest_UNKNOWN_VEHICLE_MATCH_ORDER
}

// A list-of-lists datatype for vehicle attributes.
type VehicleAttributeList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of attributes in this collection.
	Attributes []*VehicleAttribute `protobuf:"bytes,1,rep,name=attributes,proto3" json:"attributes,omitempty"`
}

func (x *VehicleAttributeList) Reset() {
	*x = VehicleAttributeList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VehicleAttributeList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VehicleAttributeList) ProtoMessage() {}

func (x *VehicleAttributeList) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VehicleAttributeList.ProtoReflect.Descriptor instead.
func (*VehicleAttributeList) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescGZIP(), []int{12}
}

func (x *VehicleAttributeList) GetAttributes() []*VehicleAttribute {
	if x != nil {
		return x.Attributes
	}
	return nil
}

var File_google_maps_fleetengine_v1_vehicle_api_proto protoreflect.FileDescriptor

var file_google_maps_fleetengine_v1_vehicle_api_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13,
	0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68,
	0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x66, 0x6c, 0x65, 0x65,
	0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x6d, 0x61, 0x70, 0x73, 0x2f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x2f, 0x76, 0x31, 0x2f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x6c, 0x61, 0x74, 0x6c, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xd0, 0x01, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e,
	0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x12, 0x22, 0x0a, 0x0a, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x09, 0x76, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x07, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x07, 0x76, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x22, 0xb7, 0x02, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e,
	0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x3e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x2a, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x24, 0x0a, 0x22, 0x66, 0x6c, 0x65, 0x65,
	0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x5d, 0x0a, 0x1d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x72, 0x6f, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x1a, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x52, 0x6f, 0x75, 0x74, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x11, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x10, 0x77, 0x61, 0x79, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xea, 0x01, 0x0a,
	0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x07, 0x76, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61,
	0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x07,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x6b, 0x22, 0x95, 0x02, 0x0a, 0x1c, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x61, 0x70,
	0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x54, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x61, 0x70, 0x73,
	0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x03, 0xe0, 0x41, 0x02, 0x52, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d,
	0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x3a, 0x02, 0x18,
	0x01, 0x22, 0xc1, 0x01, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65,
	0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03,
	0xe0, 0x41, 0x02, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x4a, 0x0a, 0x0a, 0x61, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x22, 0x6d, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d,
	0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x73, 0x22, 0xb5, 0x0b, 0x0a, 0x15, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a,
	0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x06, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x4d, 0x0a, 0x0c, 0x70, 0x69, 0x63, 0x6b, 0x75,
	0x70, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0b, 0x70, 0x69, 0x63, 0x6b, 0x75,
	0x70, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x4a, 0x0a, 0x0d, 0x64, 0x72, 0x6f, 0x70, 0x6f, 0x66,
	0x66, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x64, 0x72, 0x6f, 0x70, 0x6f, 0x66, 0x66, 0x50, 0x6f, 0x69,
	0x6e, 0x74, 0x12, 0x35, 0x0a, 0x14, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x72, 0x61, 0x64,
	0x69, 0x75, 0x73, 0x5f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x12, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x52, 0x61, 0x64,
	0x69, 0x75, 0x73, 0x4d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x19, 0x0a, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x10, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x5f,
	0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x42, 0x03,
	0xe0, 0x41, 0x02, 0x52, 0x0f, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x43, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x12, 0x41, 0x0a, 0x0a, 0x74, 0x72, 0x69, 0x70, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e,
	0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x72, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x09, 0x74, 0x72,
	0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x46, 0x0a, 0x11, 0x6d, 0x61, 0x78, 0x69, 0x6d,
	0x75, 0x6d, 0x5f, 0x73, 0x74, 0x61, 0x6c, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x6d,
	0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x53, 0x74, 0x61, 0x6c, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x12,
	0x52, 0x0a, 0x0d, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0c, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x12, 0x56, 0x0a, 0x13, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x12, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x66, 0x0a, 0x1a, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x61,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x17, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x4f, 0x6e, 0x65, 0x4f, 0x66, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x73, 0x12, 0x6d, 0x0a, 0x1e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f,
	0x6f, 0x6e, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x5f, 0x73, 0x65, 0x74, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x61,
	0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x1a, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x4f, 0x6e, 0x65, 0x4f, 0x66, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x65,
	0x74, 0x73, 0x12, 0x5c, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x3c, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65,
	0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79,
	0x12, 0x2f, 0x0a, 0x14, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x62, 0x61, 0x63, 0x6b,
	0x5f, 0x74, 0x6f, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11,
	0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x54, 0x6f, 0x42, 0x61, 0x63,
	0x6b, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x72, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x72, 0x69, 0x70, 0x49, 0x64, 0x12, 0x72, 0x0a, 0x15, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x69, 0x70, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x73,
	0x65, 0x6e, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3e, 0x2e, 0x6d, 0x61, 0x70, 0x73,
	0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x69,
	0x70, 0x73, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x13, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x54, 0x72, 0x69, 0x70, 0x73, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x22, 0xaa,
	0x01, 0x0a, 0x11, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x1b, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f,
	0x56, 0x45, 0x48, 0x49, 0x43, 0x4c, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x49, 0x43, 0x4b, 0x55, 0x50, 0x5f,
	0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x45, 0x54, 0x41, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x50,
	0x49, 0x43, 0x4b, 0x55, 0x50, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x44, 0x49, 0x53, 0x54,
	0x41, 0x4e, 0x43, 0x45, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x52, 0x4f, 0x50, 0x4f, 0x46,
	0x46, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x45, 0x54, 0x41, 0x10, 0x03, 0x12, 0x22, 0x0a,
	0x1e, 0x50, 0x49, 0x43, 0x4b, 0x55, 0x50, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x53, 0x54,
	0x52, 0x41, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x44, 0x49, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x10,
	0x04, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x4f, 0x53, 0x54, 0x10, 0x05, 0x22, 0x4f, 0x0a, 0x13, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x69, 0x70, 0x73, 0x50, 0x72, 0x65, 0x73, 0x65,
	0x6e, 0x74, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x52,
	0x49, 0x50, 0x53, 0x5f, 0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4f, 0x4e,
	0x45, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4e, 0x59, 0x10, 0x02, 0x22, 0x55, 0x0a, 0x16,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66,
	0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x07, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x73, 0x22, 0x84, 0x06, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x61,
	0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x06, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x46, 0x0a, 0x10, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x5f, 0x63, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d,
	0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x3c, 0x0a, 0x0a, 0x74, 0x72, 0x69, 0x70,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6d,
	0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x74, 0x72, 0x69,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x46, 0x0a, 0x11, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75,
	0x6d, 0x5f, 0x73, 0x74, 0x61, 0x6c, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x6d, 0x61,
	0x78, 0x69, 0x6d, 0x75, 0x6d, 0x53, 0x74, 0x61, 0x6c, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x6e,
	0x0a, 0x17, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x31, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x2e, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x15, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x2f,
	0x0a, 0x13, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12,
	0x3b, 0x0a, 0x1a, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x65, 0x5f,
	0x6f, 0x66, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x0d, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x17, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x4f, 0x6e, 0x65,
	0x4f, 0x66, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x1e,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x6f, 0x6e, 0x65, 0x5f, 0x6f, 0x66, 0x5f,
	0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x73, 0x18, 0x0f,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x1a, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x4f, 0x6e,
	0x65, 0x4f, 0x66, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x53, 0x65, 0x74, 0x73,
	0x12, 0x46, 0x0a, 0x0d, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66,
	0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x76, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x6f, 0x6e, 0x5f, 0x74,
	0x72, 0x69, 0x70, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x6f, 0x6e, 0x54, 0x72, 0x69, 0x70, 0x4f, 0x6e, 0x6c, 0x79, 0x22, 0x9c, 0x01, 0x0a, 0x14, 0x4c,
	0x69, 0x73, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x08, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x52, 0x08, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x12, 0x26, 0x0a,
	0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x22, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x09,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x66, 0x0a, 0x08, 0x57, 0x61, 0x79,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x07, 0x6c, 0x61, 0x74, 0x5f, 0x6c, 0x6e, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x06, 0x6c, 0x61, 0x74,
	0x4c, 0x6e, 0x67, 0x12, 0x2c, 0x0a, 0x03, 0x65, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x03, 0x65, 0x74,
	0x61, 0x22, 0xea, 0x08, 0x0a, 0x0c, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x12, 0x3b, 0x0a, 0x07, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74,
	0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x07, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x12,
	0x48, 0x0a, 0x12, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x70, 0x69, 0x63, 0x6b, 0x75,
	0x70, 0x5f, 0x65, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x10, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x50, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x45, 0x74, 0x61, 0x12, 0x60, 0x0a, 0x1e, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x5f, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x64, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x1b,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x50, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x44, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x7f, 0x0a, 0x2c, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x73, 0x74,
	0x72, 0x61, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x64, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x03,
	0xe0, 0x41, 0x02, 0x52, 0x27, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x50, 0x69, 0x63, 0x6b,
	0x75, 0x70, 0x53, 0x74, 0x72, 0x61, 0x69, 0x67, 0x68, 0x74, 0x4c, 0x69, 0x6e, 0x65, 0x44, 0x69,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x4a, 0x0a, 0x13,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x64, 0x72, 0x6f, 0x70, 0x6f, 0x66, 0x66, 0x5f,
	0x65, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x11, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x44, 0x72,
	0x6f, 0x70, 0x6f, 0x66, 0x66, 0x45, 0x74, 0x61, 0x12, 0x74, 0x0a, 0x29, 0x76, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x5f, 0x70, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x74, 0x6f, 0x5f, 0x64, 0x72,
	0x6f, 0x70, 0x6f, 0x66, 0x66, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e,
	0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x24, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x50, 0x69, 0x63, 0x6b, 0x75, 0x70, 0x54, 0x6f, 0x44, 0x72, 0x6f, 0x70, 0x6f, 0x66, 0x66,
	0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x3f,
	0x0a, 0x09, 0x74, 0x72, 0x69, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1d, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x08, 0x74, 0x72, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x55, 0x0a, 0x17, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x74, 0x72, 0x69, 0x70, 0x73,
	0x5f, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52,
	0x15, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x72, 0x69, 0x70, 0x73, 0x57, 0x61, 0x79,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x60, 0x0a, 0x12, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4d, 0x61, 0x74,
	0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x6e, 0x0a, 0x14, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3c, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x12, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x65, 0x64, 0x42, 0x79, 0x12, 0x5b, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3c, 0x2e, 0x6d,
	0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x65, 0x64, 0x42, 0x79, 0x22, 0x67, 0x0a, 0x10, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x45, 0x58, 0x43, 0x4c, 0x55, 0x53,
	0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x54, 0x4f,
	0x5f, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x41, 0x52, 0x50, 0x4f,
	0x4f, 0x4c, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x41, 0x52, 0x50, 0x4f, 0x4f, 0x4c, 0x5f,
	0x42, 0x41, 0x43, 0x4b, 0x5f, 0x54, 0x4f, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x04, 0x22, 0x5d,
	0x0a, 0x14, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x45, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x61, 0x70,
	0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x32, 0xf5, 0x0c,
	0x0a, 0x0e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0xb7, 0x01, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x12, 0x29, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e,
	0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x22, 0x5d, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x2c, 0x22, 0x21, 0x2f, 0x76, 0x31, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x76, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x3a, 0x07, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x8a,
	0xd3, 0xe4, 0x93, 0x02, 0x25, 0x12, 0x23, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12,
	0x19, 0x7b, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x3d, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x12, 0xa6, 0x01, 0x0a, 0x0a, 0x47,
	0x65, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x26, 0x2e, 0x6d, 0x61, 0x70, 0x73,
	0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1c, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x22,
	0x52, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x12, 0x21, 0x2f, 0x76, 0x31, 0x2f, 0x7b, 0x6e, 0x61,
	0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x2f, 0x2a, 0x7d, 0x8a, 0xd3, 0xe4, 0x93, 0x02, 0x23,
	0x12, 0x21, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x7b, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73,
	0x2f, 0x2a, 0x7d, 0x12, 0xb5, 0x01, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x29, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1c, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x22, 0x5b,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x1a, 0x21, 0x2f, 0x76, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d,
	0x65, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x76, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x07, 0x76, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x8a, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x12, 0x21, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x19, 0x7b, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x3d, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x12, 0xd9, 0x01, 0x0a, 0x15,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e,
	0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x67,
	0x88, 0x02, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x35, 0x1a, 0x30, 0x2f, 0x76, 0x31, 0x2f, 0x7b,
	0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a,
	0x2f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x3a, 0x01, 0x2a, 0x8a, 0xd3,
	0xe4, 0x93, 0x02, 0x23, 0x12, 0x21, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x7b, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x12, 0xec, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75,
	0x74, 0x65, 0x73, 0x12, 0x33, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74,
	0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e,
	0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x66,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x37, 0x22, 0x32, 0x2f, 0x76, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d,
	0x65, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x76, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x3a, 0x01, 0x2a, 0x8a, 0xd3, 0xe4,
	0x93, 0x02, 0x23, 0x12, 0x21, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x7b, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x12, 0xb9, 0x01, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x12, 0x28, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66,
	0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x29, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x54, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x23, 0x12, 0x21, 0x2f, 0x76, 0x31, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x8a, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x12, 0x23, 0x0a,
	0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x7b, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x2f,
	0x2a, 0x7d, 0x12, 0xc9, 0x01, 0x0a, 0x0e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x73, 0x12, 0x2a, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2b, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x5e,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x22, 0x28, 0x2f, 0x76, 0x31, 0x2f, 0x7b, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d,
	0x2f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x3a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x3a, 0x01, 0x2a, 0x8a, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x12, 0x23, 0x0a, 0x06, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x12, 0x19, 0x7b, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x12, 0xd5,
	0x01, 0x0a, 0x14, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x46, 0x75, 0x7a, 0x7a, 0x65, 0x64, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x12, 0x2a, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66,
	0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74,
	0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x64, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x33, 0x22, 0x2e, 0x2f, 0x76, 0x31, 0x2f, 0x7b, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x2f,
	0x2a, 0x7d, 0x2f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x3a, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x46, 0x75, 0x7a, 0x7a, 0x65, 0x64, 0x3a, 0x01, 0x2a, 0x8a, 0xd3, 0xe4, 0x93, 0x02,
	0x25, 0x12, 0x23, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x7b, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x3d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x1a, 0x1d, 0xca, 0x41, 0x1a, 0x66, 0x6c, 0x65, 0x65, 0x74,
	0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x42, 0x77, 0x0a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x2e, 0x76, 0x31, 0x42, 0x0a, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x70, 0x69, 0x50,
	0x01, 0x5a, 0x45, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67,
	0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0xa2, 0x02, 0x03, 0x43, 0x46, 0x45, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescOnce sync.Once
	file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescData = file_google_maps_fleetengine_v1_vehicle_api_proto_rawDesc
)

func file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescGZIP() []byte {
	file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescOnce.Do(func() {
		file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescData)
	})
	return file_google_maps_fleetengine_v1_vehicle_api_proto_rawDescData
}

var file_google_maps_fleetengine_v1_vehicle_api_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_google_maps_fleetengine_v1_vehicle_api_proto_goTypes = []interface{}{
	(SearchVehiclesRequest_VehicleMatchOrder)(0),   // 0: maps.fleetengine.v1.SearchVehiclesRequest.VehicleMatchOrder
	(SearchVehiclesRequest_CurrentTripsPresent)(0), // 1: maps.fleetengine.v1.SearchVehiclesRequest.CurrentTripsPresent
	(VehicleMatch_VehicleMatchType)(0),             // 2: maps.fleetengine.v1.VehicleMatch.VehicleMatchType
	(*CreateVehicleRequest)(nil),                   // 3: maps.fleetengine.v1.CreateVehicleRequest
	(*GetVehicleRequest)(nil),                      // 4: maps.fleetengine.v1.GetVehicleRequest
	(*UpdateVehicleRequest)(nil),                   // 5: maps.fleetengine.v1.UpdateVehicleRequest
	(*UpdateVehicleLocationRequest)(nil),           // 6: maps.fleetengine.v1.UpdateVehicleLocationRequest
	(*UpdateVehicleAttributesRequest)(nil),         // 7: maps.fleetengine.v1.UpdateVehicleAttributesRequest
	(*UpdateVehicleAttributesResponse)(nil),        // 8: maps.fleetengine.v1.UpdateVehicleAttributesResponse
	(*SearchVehiclesRequest)(nil),                  // 9: maps.fleetengine.v1.SearchVehiclesRequest
	(*SearchVehiclesResponse)(nil),                 // 10: maps.fleetengine.v1.SearchVehiclesResponse
	(*ListVehiclesRequest)(nil),                    // 11: maps.fleetengine.v1.ListVehiclesRequest
	(*ListVehiclesResponse)(nil),                   // 12: maps.fleetengine.v1.ListVehiclesResponse
	(*Waypoint)(nil),                               // 13: maps.fleetengine.v1.Waypoint
	(*VehicleMatch)(nil),                           // 14: maps.fleetengine.v1.VehicleMatch
	(*VehicleAttributeList)(nil),                   // 15: maps.fleetengine.v1.VehicleAttributeList
	(*RequestHeader)(nil),                          // 16: maps.fleetengine.v1.RequestHeader
	(*Vehicle)(nil),                                // 17: maps.fleetengine.v1.Vehicle
	(*timestamppb.Timestamp)(nil),                  // 18: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),                  // 19: google.protobuf.FieldMask
	(*VehicleLocation)(nil),                        // 20: maps.fleetengine.v1.VehicleLocation
	(VehicleState)(0),                              // 21: maps.fleetengine.v1.VehicleState
	(*VehicleAttribute)(nil),                       // 22: maps.fleetengine.v1.VehicleAttribute
	(*TerminalLocation)(nil),                       // 23: maps.fleetengine.v1.TerminalLocation
	(TripType)(0),                                  // 24: maps.fleetengine.v1.TripType
	(*durationpb.Duration)(nil),                    // 25: google.protobuf.Duration
	(*Vehicle_VehicleType)(nil),                    // 26: maps.fleetengine.v1.Vehicle.VehicleType
	(*wrapperspb.Int32Value)(nil),                  // 27: google.protobuf.Int32Value
	(Vehicle_VehicleType_Category)(0),              // 28: maps.fleetengine.v1.Vehicle.VehicleType.Category
	(*latlng.LatLng)(nil),                          // 29: google.type.LatLng
}
var file_google_maps_fleetengine_v1_vehicle_api_proto_depIdxs = []int32{
	16, // 0: maps.fleetengine.v1.CreateVehicleRequest.header:type_name -> maps.fleetengine.v1.RequestHeader
	17, // 1: maps.fleetengine.v1.CreateVehicleRequest.vehicle:type_name -> maps.fleetengine.v1.Vehicle
	16, // 2: maps.fleetengine.v1.GetVehicleRequest.header:type_name -> maps.fleetengine.v1.RequestHeader
	18, // 3: maps.fleetengine.v1.GetVehicleRequest.current_route_segment_version:type_name -> google.protobuf.Timestamp
	18, // 4: maps.fleetengine.v1.GetVehicleRequest.waypoints_version:type_name -> google.protobuf.Timestamp
	16, // 5: maps.fleetengine.v1.UpdateVehicleRequest.header:type_name -> maps.fleetengine.v1.RequestHeader
	17, // 6: maps.fleetengine.v1.UpdateVehicleRequest.vehicle:type_name -> maps.fleetengine.v1.Vehicle
	19, // 7: maps.fleetengine.v1.UpdateVehicleRequest.update_mask:type_name -> google.protobuf.FieldMask
	16, // 8: maps.fleetengine.v1.UpdateVehicleLocationRequest.header:type_name -> maps.fleetengine.v1.RequestHeader
	20, // 9: maps.fleetengine.v1.UpdateVehicleLocationRequest.current_location:type_name -> maps.fleetengine.v1.VehicleLocation
	21, // 10: maps.fleetengine.v1.UpdateVehicleLocationRequest.current_state:type_name -> maps.fleetengine.v1.VehicleState
	16, // 11: maps.fleetengine.v1.UpdateVehicleAttributesRequest.header:type_name -> maps.fleetengine.v1.RequestHeader
	22, // 12: maps.fleetengine.v1.UpdateVehicleAttributesRequest.attributes:type_name -> maps.fleetengine.v1.VehicleAttribute
	22, // 13: maps.fleetengine.v1.UpdateVehicleAttributesResponse.attributes:type_name -> maps.fleetengine.v1.VehicleAttribute
	16, // 14: maps.fleetengine.v1.SearchVehiclesRequest.header:type_name -> maps.fleetengine.v1.RequestHeader
	23, // 15: maps.fleetengine.v1.SearchVehiclesRequest.pickup_point:type_name -> maps.fleetengine.v1.TerminalLocation
	23, // 16: maps.fleetengine.v1.SearchVehiclesRequest.dropoff_point:type_name -> maps.fleetengine.v1.TerminalLocation
	24, // 17: maps.fleetengine.v1.SearchVehiclesRequest.trip_types:type_name -> maps.fleetengine.v1.TripType
	25, // 18: maps.fleetengine.v1.SearchVehiclesRequest.maximum_staleness:type_name -> google.protobuf.Duration
	26, // 19: maps.fleetengine.v1.SearchVehiclesRequest.vehicle_types:type_name -> maps.fleetengine.v1.Vehicle.VehicleType
	22, // 20: maps.fleetengine.v1.SearchVehiclesRequest.required_attributes:type_name -> maps.fleetengine.v1.VehicleAttribute
	15, // 21: maps.fleetengine.v1.SearchVehiclesRequest.required_one_of_attributes:type_name -> maps.fleetengine.v1.VehicleAttributeList
	15, // 22: maps.fleetengine.v1.SearchVehiclesRequest.required_one_of_attribute_sets:type_name -> maps.fleetengine.v1.VehicleAttributeList
	0,  // 23: maps.fleetengine.v1.SearchVehiclesRequest.order_by:type_name -> maps.fleetengine.v1.SearchVehiclesRequest.VehicleMatchOrder
	1,  // 24: maps.fleetengine.v1.SearchVehiclesRequest.current_trips_present:type_name -> maps.fleetengine.v1.SearchVehiclesRequest.CurrentTripsPresent
	14, // 25: maps.fleetengine.v1.SearchVehiclesResponse.matches:type_name -> maps.fleetengine.v1.VehicleMatch
	16, // 26: maps.fleetengine.v1.ListVehiclesRequest.header:type_name -> maps.fleetengine.v1.RequestHeader
	27, // 27: maps.fleetengine.v1.ListVehiclesRequest.minimum_capacity:type_name -> google.protobuf.Int32Value
	24, // 28: maps.fleetengine.v1.ListVehiclesRequest.trip_types:type_name -> maps.fleetengine.v1.TripType
	25, // 29: maps.fleetengine.v1.ListVehiclesRequest.maximum_staleness:type_name -> google.protobuf.Duration
	28, // 30: maps.fleetengine.v1.ListVehiclesRequest.vehicle_type_categories:type_name -> maps.fleetengine.v1.Vehicle.VehicleType.Category
	21, // 31: maps.fleetengine.v1.ListVehiclesRequest.vehicle_state:type_name -> maps.fleetengine.v1.VehicleState
	17, // 32: maps.fleetengine.v1.ListVehiclesResponse.vehicles:type_name -> maps.fleetengine.v1.Vehicle
	29, // 33: maps.fleetengine.v1.Waypoint.lat_lng:type_name -> google.type.LatLng
	18, // 34: maps.fleetengine.v1.Waypoint.eta:type_name -> google.protobuf.Timestamp
	17, // 35: maps.fleetengine.v1.VehicleMatch.vehicle:type_name -> maps.fleetengine.v1.Vehicle
	18, // 36: maps.fleetengine.v1.VehicleMatch.vehicle_pickup_eta:type_name -> google.protobuf.Timestamp
	27, // 37: maps.fleetengine.v1.VehicleMatch.vehicle_pickup_distance_meters:type_name -> google.protobuf.Int32Value
	27, // 38: maps.fleetengine.v1.VehicleMatch.vehicle_pickup_straight_line_distance_meters:type_name -> google.protobuf.Int32Value
	18, // 39: maps.fleetengine.v1.VehicleMatch.vehicle_dropoff_eta:type_name -> google.protobuf.Timestamp
	27, // 40: maps.fleetengine.v1.VehicleMatch.vehicle_pickup_to_dropoff_distance_meters:type_name -> google.protobuf.Int32Value
	24, // 41: maps.fleetengine.v1.VehicleMatch.trip_type:type_name -> maps.fleetengine.v1.TripType
	13, // 42: maps.fleetengine.v1.VehicleMatch.vehicle_trips_waypoints:type_name -> maps.fleetengine.v1.Waypoint
	2,  // 43: maps.fleetengine.v1.VehicleMatch.vehicle_match_type:type_name -> maps.fleetengine.v1.VehicleMatch.VehicleMatchType
	0,  // 44: maps.fleetengine.v1.VehicleMatch.requested_ordered_by:type_name -> maps.fleetengine.v1.SearchVehiclesRequest.VehicleMatchOrder
	0,  // 45: maps.fleetengine.v1.VehicleMatch.ordered_by:type_name -> maps.fleetengine.v1.SearchVehiclesRequest.VehicleMatchOrder
	22, // 46: maps.fleetengine.v1.VehicleAttributeList.attributes:type_name -> maps.fleetengine.v1.VehicleAttribute
	3,  // 47: maps.fleetengine.v1.VehicleService.CreateVehicle:input_type -> maps.fleetengine.v1.CreateVehicleRequest
	4,  // 48: maps.fleetengine.v1.VehicleService.GetVehicle:input_type -> maps.fleetengine.v1.GetVehicleRequest
	5,  // 49: maps.fleetengine.v1.VehicleService.UpdateVehicle:input_type -> maps.fleetengine.v1.UpdateVehicleRequest
	6,  // 50: maps.fleetengine.v1.VehicleService.UpdateVehicleLocation:input_type -> maps.fleetengine.v1.UpdateVehicleLocationRequest
	7,  // 51: maps.fleetengine.v1.VehicleService.UpdateVehicleAttributes:input_type -> maps.fleetengine.v1.UpdateVehicleAttributesRequest
	11, // 52: maps.fleetengine.v1.VehicleService.ListVehicles:input_type -> maps.fleetengine.v1.ListVehiclesRequest
	9,  // 53: maps.fleetengine.v1.VehicleService.SearchVehicles:input_type -> maps.fleetengine.v1.SearchVehiclesRequest
	9,  // 54: maps.fleetengine.v1.VehicleService.SearchFuzzedVehicles:input_type -> maps.fleetengine.v1.SearchVehiclesRequest
	17, // 55: maps.fleetengine.v1.VehicleService.CreateVehicle:output_type -> maps.fleetengine.v1.Vehicle
	17, // 56: maps.fleetengine.v1.VehicleService.GetVehicle:output_type -> maps.fleetengine.v1.Vehicle
	17, // 57: maps.fleetengine.v1.VehicleService.UpdateVehicle:output_type -> maps.fleetengine.v1.Vehicle
	20, // 58: maps.fleetengine.v1.VehicleService.UpdateVehicleLocation:output_type -> maps.fleetengine.v1.VehicleLocation
	8,  // 59: maps.fleetengine.v1.VehicleService.UpdateVehicleAttributes:output_type -> maps.fleetengine.v1.UpdateVehicleAttributesResponse
	12, // 60: maps.fleetengine.v1.VehicleService.ListVehicles:output_type -> maps.fleetengine.v1.ListVehiclesResponse
	10, // 61: maps.fleetengine.v1.VehicleService.SearchVehicles:output_type -> maps.fleetengine.v1.SearchVehiclesResponse
	10, // 62: maps.fleetengine.v1.VehicleService.SearchFuzzedVehicles:output_type -> maps.fleetengine.v1.SearchVehiclesResponse
	55, // [55:63] is the sub-list for method output_type
	47, // [47:55] is the sub-list for method input_type
	47, // [47:47] is the sub-list for extension type_name
	47, // [47:47] is the sub-list for extension extendee
	0,  // [0:47] is the sub-list for field type_name
}

func init() { file_google_maps_fleetengine_v1_vehicle_api_proto_init() }
func file_google_maps_fleetengine_v1_vehicle_api_proto_init() {
	if File_google_maps_fleetengine_v1_vehicle_api_proto != nil {
		return
	}
	file_google_maps_fleetengine_v1_fleetengine_proto_init()
	file_google_maps_fleetengine_v1_header_proto_init()
	file_google_maps_fleetengine_v1_vehicles_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateVehicleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVehicleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateVehicleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateVehicleLocationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateVehicleAttributesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateVehicleAttributesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchVehiclesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchVehiclesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListVehiclesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListVehiclesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Waypoint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VehicleMatch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VehicleAttributeList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_maps_fleetengine_v1_vehicle_api_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_google_maps_fleetengine_v1_vehicle_api_proto_goTypes,
		DependencyIndexes: file_google_maps_fleetengine_v1_vehicle_api_proto_depIdxs,
		EnumInfos:         file_google_maps_fleetengine_v1_vehicle_api_proto_enumTypes,
		MessageInfos:      file_google_maps_fleetengine_v1_vehicle_api_proto_msgTypes,
	}.Build()
	File_google_maps_fleetengine_v1_vehicle_api_proto = out.File
	file_google_maps_fleetengine_v1_vehicle_api_proto_rawDesc = nil
	file_google_maps_fleetengine_v1_vehicle_api_proto_goTypes = nil
	file_google_maps_fleetengine_v1_vehicle_api_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// VehicleServiceClient is the client API for VehicleService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type VehicleServiceClient interface {
	// Instantiates a new vehicle associated with an on-demand rideshare or
	// deliveries provider. Each `Vehicle` must have a unique vehicle ID.
	//
	// The following `Vehicle` fields are required when creating a `Vehicle`:
	//
	// * `vehicleState`
	// * `supportedTripTypes`
	// * `maximumCapacity`
	// * `vehicleType`
	//
	// The following `Vehicle` fields are ignored when creating a `Vehicle`:
	//
	// * `name`
	// * `currentTrips`
	// * `availableCapacity`
	// * `current_route_segment`
	// * `current_route_segment_end_point`
	// * `current_route_segment_version`
	// * `current_route_segment_traffic`
	// * `route`
	// * `waypoints`
	// * `waypoints_version`
	// * `remaining_distance_meters`
	// * `remaining_time_seconds`
	// * `eta_to_next_waypoint`
	// * `navigation_status`
	//
	// All other fields are optional and used if provided.
	CreateVehicle(ctx context.Context, in *CreateVehicleRequest, opts ...grpc.CallOption) (*Vehicle, error)
	// Returns a vehicle from the Fleet Engine.
	GetVehicle(ctx context.Context, in *GetVehicleRequest, opts ...grpc.CallOption) (*Vehicle, error)
	// Writes updated vehicle data to the Fleet Engine.
	//
	// When updating a `Vehicle`, the following fields cannot be updated since
	// they are managed by the server:
	//
	// * `currentTrips`
	// * `availableCapacity`
	// * `current_route_segment_version`
	// * `waypoints_version`
	//
	// The vehicle `name` also cannot be updated.
	//
	// If the `attributes` field is updated, **all** the vehicle's attributes are
	// replaced with the attributes provided in the request. If you want to update
	// only some attributes, see the `UpdateVehicleAttributes` method. Likewise,
	// the `waypoints` field can be updated, but must contain all the waypoints
	// currently on the vehicle, and no other waypoints.
	UpdateVehicle(ctx context.Context, in *UpdateVehicleRequest, opts ...grpc.CallOption) (*Vehicle, error)
	// Deprecated: Do not use.
	// Deprecated: Use the `UpdateVehicle` method instead.
	// UpdateVehicleLocation updates the location of the vehicle.
	UpdateVehicleLocation(ctx context.Context, in *UpdateVehicleLocationRequest, opts ...grpc.CallOption) (*VehicleLocation, error)
	// Partially updates a vehicle's attributes.
	// Only the attributes mentioned in the request will be updated, other
	// attributes will NOT be altered. Note: this is different in `UpdateVehicle`,
	// where the whole `attributes` field will be replaced by the one in
	// `UpdateVehicleRequest`, attributes not in the request would be removed.
	UpdateVehicleAttributes(ctx context.Context, in *UpdateVehicleAttributesRequest, opts ...grpc.CallOption) (*UpdateVehicleAttributesResponse, error)
	// Returns a paginated list of vehicles associated with
	// a provider that match the request options.
	ListVehicles(ctx context.Context, in *ListVehiclesRequest, opts ...grpc.CallOption) (*ListVehiclesResponse, error)
	// Returns a list of vehicles that match the request options.
	SearchVehicles(ctx context.Context, in *SearchVehiclesRequest, opts ...grpc.CallOption) (*SearchVehiclesResponse, error)
	// Returns a list of vehicles that match the request
	// options, but the vehicle locations will be somewhat altered for privacy.
	// This method does not support the `SearchVehicleRequest.order_by` field.
	// Vehicle matches in the response will be in order of distance from the
	// pickup point.  Only the `vehicle` and `trip_type` fields will be populated.
	SearchFuzzedVehicles(ctx context.Context, in *SearchVehiclesRequest, opts ...grpc.CallOption) (*SearchVehiclesResponse, error)
}

type vehicleServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewVehicleServiceClient(cc grpc.ClientConnInterface) VehicleServiceClient {
	return &vehicleServiceClient{cc}
}

func (c *vehicleServiceClient) CreateVehicle(ctx context.Context, in *CreateVehicleRequest, opts ...grpc.CallOption) (*Vehicle, error) {
	out := new(Vehicle)
	err := c.cc.Invoke(ctx, "/maps.fleetengine.v1.VehicleService/CreateVehicle", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vehicleServiceClient) GetVehicle(ctx context.Context, in *GetVehicleRequest, opts ...grpc.CallOption) (*Vehicle, error) {
	out := new(Vehicle)
	err := c.cc.Invoke(ctx, "/maps.fleetengine.v1.VehicleService/GetVehicle", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vehicleServiceClient) UpdateVehicle(ctx context.Context, in *UpdateVehicleRequest, opts ...grpc.CallOption) (*Vehicle, error) {
	out := new(Vehicle)
	err := c.cc.Invoke(ctx, "/maps.fleetengine.v1.VehicleService/UpdateVehicle", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *vehicleServiceClient) UpdateVehicleLocation(ctx context.Context, in *UpdateVehicleLocationRequest, opts ...grpc.CallOption) (*VehicleLocation, error) {
	out := new(VehicleLocation)
	err := c.cc.Invoke(ctx, "/maps.fleetengine.v1.VehicleService/UpdateVehicleLocation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vehicleServiceClient) UpdateVehicleAttributes(ctx context.Context, in *UpdateVehicleAttributesRequest, opts ...grpc.CallOption) (*UpdateVehicleAttributesResponse, error) {
	out := new(UpdateVehicleAttributesResponse)
	err := c.cc.Invoke(ctx, "/maps.fleetengine.v1.VehicleService/UpdateVehicleAttributes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vehicleServiceClient) ListVehicles(ctx context.Context, in *ListVehiclesRequest, opts ...grpc.CallOption) (*ListVehiclesResponse, error) {
	out := new(ListVehiclesResponse)
	err := c.cc.Invoke(ctx, "/maps.fleetengine.v1.VehicleService/ListVehicles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vehicleServiceClient) SearchVehicles(ctx context.Context, in *SearchVehiclesRequest, opts ...grpc.CallOption) (*SearchVehiclesResponse, error) {
	out := new(SearchVehiclesResponse)
	err := c.cc.Invoke(ctx, "/maps.fleetengine.v1.VehicleService/SearchVehicles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vehicleServiceClient) SearchFuzzedVehicles(ctx context.Context, in *SearchVehiclesRequest, opts ...grpc.CallOption) (*SearchVehiclesResponse, error) {
	out := new(SearchVehiclesResponse)
	err := c.cc.Invoke(ctx, "/maps.fleetengine.v1.VehicleService/SearchFuzzedVehicles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VehicleServiceServer is the server API for VehicleService service.
type VehicleServiceServer interface {
	// Instantiates a new vehicle associated with an on-demand rideshare or
	// deliveries provider. Each `Vehicle` must have a unique vehicle ID.
	//
	// The following `Vehicle` fields are required when creating a `Vehicle`:
	//
	// * `vehicleState`
	// * `supportedTripTypes`
	// * `maximumCapacity`
	// * `vehicleType`
	//
	// The following `Vehicle` fields are ignored when creating a `Vehicle`:
	//
	// * `name`
	// * `currentTrips`
	// * `availableCapacity`
	// * `current_route_segment`
	// * `current_route_segment_end_point`
	// * `current_route_segment_version`
	// * `current_route_segment_traffic`
	// * `route`
	// * `waypoints`
	// * `waypoints_version`
	// * `remaining_distance_meters`
	// * `remaining_time_seconds`
	// * `eta_to_next_waypoint`
	// * `navigation_status`
	//
	// All other fields are optional and used if provided.
	CreateVehicle(context.Context, *CreateVehicleRequest) (*Vehicle, error)
	// Returns a vehicle from the Fleet Engine.
	GetVehicle(context.Context, *GetVehicleRequest) (*Vehicle, error)
	// Writes updated vehicle data to the Fleet Engine.
	//
	// When updating a `Vehicle`, the following fields cannot be updated since
	// they are managed by the server:
	//
	// * `currentTrips`
	// * `availableCapacity`
	// * `current_route_segment_version`
	// * `waypoints_version`
	//
	// The vehicle `name` also cannot be updated.
	//
	// If the `attributes` field is updated, **all** the vehicle's attributes are
	// replaced with the attributes provided in the request. If you want to update
	// only some attributes, see the `UpdateVehicleAttributes` method. Likewise,
	// the `waypoints` field can be updated, but must contain all the waypoints
	// currently on the vehicle, and no other waypoints.
	UpdateVehicle(context.Context, *UpdateVehicleRequest) (*Vehicle, error)
	// Deprecated: Do not use.
	// Deprecated: Use the `UpdateVehicle` method instead.
	// UpdateVehicleLocation updates the location of the vehicle.
	UpdateVehicleLocation(context.Context, *UpdateVehicleLocationRequest) (*VehicleLocation, error)
	// Partially updates a vehicle's attributes.
	// Only the attributes mentioned in the request will be updated, other
	// attributes will NOT be altered. Note: this is different in `UpdateVehicle`,
	// where the whole `attributes` field will be replaced by the one in
	// `UpdateVehicleRequest`, attributes not in the request would be removed.
	UpdateVehicleAttributes(context.Context, *UpdateVehicleAttributesRequest) (*UpdateVehicleAttributesResponse, error)
	// Returns a paginated list of vehicles associated with
	// a provider that match the request options.
	ListVehicles(context.Context, *ListVehiclesRequest) (*ListVehiclesResponse, error)
	// Returns a list of vehicles that match the request options.
	SearchVehicles(context.Context, *SearchVehiclesRequest) (*SearchVehiclesResponse, error)
	// Returns a list of vehicles that match the request
	// options, but the vehicle locations will be somewhat altered for privacy.
	// This method does not support the `SearchVehicleRequest.order_by` field.
	// Vehicle matches in the response will be in order of distance from the
	// pickup point.  Only the `vehicle` and `trip_type` fields will be populated.
	SearchFuzzedVehicles(context.Context, *SearchVehiclesRequest) (*SearchVehiclesResponse, error)
}

// UnimplementedVehicleServiceServer can be embedded to have forward compatible implementations.
type UnimplementedVehicleServiceServer struct {
}

func (*UnimplementedVehicleServiceServer) CreateVehicle(context.Context, *CreateVehicleRequest) (*Vehicle, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateVehicle not implemented")
}
func (*UnimplementedVehicleServiceServer) GetVehicle(context.Context, *GetVehicleRequest) (*Vehicle, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVehicle not implemented")
}
func (*UnimplementedVehicleServiceServer) UpdateVehicle(context.Context, *UpdateVehicleRequest) (*Vehicle, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateVehicle not implemented")
}
func (*UnimplementedVehicleServiceServer) UpdateVehicleLocation(context.Context, *UpdateVehicleLocationRequest) (*VehicleLocation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateVehicleLocation not implemented")
}
func (*UnimplementedVehicleServiceServer) UpdateVehicleAttributes(context.Context, *UpdateVehicleAttributesRequest) (*UpdateVehicleAttributesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateVehicleAttributes not implemented")
}
func (*UnimplementedVehicleServiceServer) ListVehicles(context.Context, *ListVehiclesRequest) (*ListVehiclesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListVehicles not implemented")
}
func (*UnimplementedVehicleServiceServer) SearchVehicles(context.Context, *SearchVehiclesRequest) (*SearchVehiclesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchVehicles not implemented")
}
func (*UnimplementedVehicleServiceServer) SearchFuzzedVehicles(context.Context, *SearchVehiclesRequest) (*SearchVehiclesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchFuzzedVehicles not implemented")
}

func RegisterVehicleServiceServer(s *grpc.Server, srv VehicleServiceServer) {
	s.RegisterService(&_VehicleService_serviceDesc, srv)
}

func _VehicleService_CreateVehicle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVehicleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehicleServiceServer).CreateVehicle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/maps.fleetengine.v1.VehicleService/CreateVehicle",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehicleServiceServer).CreateVehicle(ctx, req.(*CreateVehicleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VehicleService_GetVehicle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVehicleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehicleServiceServer).GetVehicle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/maps.fleetengine.v1.VehicleService/GetVehicle",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehicleServiceServer).GetVehicle(ctx, req.(*GetVehicleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VehicleService_UpdateVehicle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateVehicleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehicleServiceServer).UpdateVehicle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/maps.fleetengine.v1.VehicleService/UpdateVehicle",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehicleServiceServer).UpdateVehicle(ctx, req.(*UpdateVehicleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VehicleService_UpdateVehicleLocation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateVehicleLocationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehicleServiceServer).UpdateVehicleLocation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/maps.fleetengine.v1.VehicleService/UpdateVehicleLocation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehicleServiceServer).UpdateVehicleLocation(ctx, req.(*UpdateVehicleLocationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VehicleService_UpdateVehicleAttributes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateVehicleAttributesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehicleServiceServer).UpdateVehicleAttributes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/maps.fleetengine.v1.VehicleService/UpdateVehicleAttributes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehicleServiceServer).UpdateVehicleAttributes(ctx, req.(*UpdateVehicleAttributesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VehicleService_ListVehicles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListVehiclesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehicleServiceServer).ListVehicles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/maps.fleetengine.v1.VehicleService/ListVehicles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehicleServiceServer).ListVehicles(ctx, req.(*ListVehiclesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VehicleService_SearchVehicles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchVehiclesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehicleServiceServer).SearchVehicles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/maps.fleetengine.v1.VehicleService/SearchVehicles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehicleServiceServer).SearchVehicles(ctx, req.(*SearchVehiclesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VehicleService_SearchFuzzedVehicles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchVehiclesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VehicleServiceServer).SearchFuzzedVehicles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/maps.fleetengine.v1.VehicleService/SearchFuzzedVehicles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VehicleServiceServer).SearchFuzzedVehicles(ctx, req.(*SearchVehiclesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _VehicleService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "maps.fleetengine.v1.VehicleService",
	HandlerType: (*VehicleServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateVehicle",
			Handler:    _VehicleService_CreateVehicle_Handler,
		},
		{
			MethodName: "GetVehicle",
			Handler:    _VehicleService_GetVehicle_Handler,
		},
		{
			MethodName: "UpdateVehicle",
			Handler:    _VehicleService_UpdateVehicle_Handler,
		},
		{
			MethodName: "UpdateVehicleLocation",
			Handler:    _VehicleService_UpdateVehicleLocation_Handler,
		},
		{
			MethodName: "UpdateVehicleAttributes",
			Handler:    _VehicleService_UpdateVehicleAttributes_Handler,
		},
		{
			MethodName: "ListVehicles",
			Handler:    _VehicleService_ListVehicles_Handler,
		},
		{
			MethodName: "SearchVehicles",
			Handler:    _VehicleService_SearchVehicles_Handler,
		},
		{
			MethodName: "SearchFuzzedVehicles",
			Handler:    _VehicleService_SearchFuzzedVehicles_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "google/maps/fleetengine/v1/vehicle_api.proto",
}
