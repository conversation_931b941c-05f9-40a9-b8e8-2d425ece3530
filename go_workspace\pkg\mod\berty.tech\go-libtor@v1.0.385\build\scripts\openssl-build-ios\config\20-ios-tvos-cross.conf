## -*- mode: perl; -*-
## iOS configuration targets

my %targets = ();

%targets = (
    ## Base settings for iOS-tvOS cross-compile
    # Based on 10-main.conf: iphoneos-cross
    # Add generic compiler flags
    # Add embed-bitcode option if SDK version is 9 or higher
    "ios-tvos-cross-base" => {
        template         => 1,
        cflags           => combine('-isysroot $(CROSS_TOP)/SDKs/$(CROSS_SDK) -fno-common',
                				sub { ((!defined($ENV{'CONFIG_DISABLE_BITCODE'}) || $ENV{'CONFIG_DISABLE_BITCODE'} ne 'true') && defined($ENV{'SDKVERSION'}) && $ENV{'SDKVERSION'} =~ /^(9|[1-9][0-9]+)\./ && $disabled{shared})
                						? '-fembed-bitcode' : (); },
        					),
    },
    
    ## Base settings for iOS
    "ios-cross-base" => {
        inherit_from    => [ "ios-tvos-cross-base" ],
        template         => 1,
        cflags           => add(sub { defined($ENV{'IOS_MIN_SDK_VERSION'}) ? '-mios-version-min=$(IOS_MIN_SDK_VERSION)' : '-mios-version-min=7.0'; }),
    },

    ## Base settings for tvOS
    # Defines to skip functionality that uses unsupported functions
    "tvos-cross-base" => {
        inherit_from    => [ "ios-tvos-cross-base" ],
        template         => 1,
        cflags           => add(sub { defined($ENV{'TVOS_MIN_SDK_VERSION'}) ? '-mtvos-version-min=$(TVOS_MIN_SDK_VERSION)' : '-mtvos-version-min=9.0'; }),
        defines          => [ "HAVE_FORK=0" ],
    },

    ## Base for Catalyst
    "mac-catalyst-base" => {
        inherit_from    => [ "ios-tvos-cross-base" ],
        template         => 1,
        cflags          => add(sub { defined($ENV{'MACOSX_MIN_SDK_VERSION'}) ? '-mios-version-min=$(MACOSX_MIN_SDK_VERSION)' : '-mmacosx-version-min=10.15'; }),
    },

    ## Catalyst (armv7s)
    # Based on 10-main.conf: ios-cross
    "mac-catalyst-x86_64" => {
        inherit_from     => [ "darwin64-x86_64-cc", "mac-catalyst-base" ],
        cflags           => add("-target x86_64-apple-ios13.0-macabi"),
        sys_id           => "MacOSX",
    },

    ## Apple iOS simulator (x86_64)
    # Based on 10-main.conf: iphoneos-cross / darwin64-x86_64-cc
    "ios-sim-cross-x86_64" => {
        inherit_from     => [ "darwin64-x86_64-cc", "ios-cross-base" ],
        sys_id           => "iOS",
    },

    ## Apple iOS simulator (i386)
    # Based on 10-main.conf: iphoneos-cross / darwin-i386-cc
    "ios-sim-cross-i386" => {
        inherit_from     => [ "darwin-i386-cc", "ios-cross-base" ],
        sys_id           => "iOS",
    },

    ## Apple iOS (arm64)
    # Based on 10-main.conf: ios64-cross
    "ios64-cross-arm64" => {
        inherit_from     => [ "darwin-common", "ios-cross-base", asm("aarch64_asm") ],
        cflags           => add("-arch arm64"),
        bn_ops           => "SIXTY_FOUR_BIT_LONG RC4_CHAR",
        perlasm_scheme   => "ios64",
        sys_id           => "iOS",
    },

    ## Apple iOS (arm64e)
    # Based on 10-main.conf: ios64-cross
    "ios64-cross-arm64e" => {
        inherit_from     => [ "darwin-common", "ios-cross-base", asm("aarch64_asm") ],
        cflags           => add("-arch arm64e"),
        bn_ops           => "SIXTY_FOUR_BIT_LONG RC4_CHAR",
        perlasm_scheme   => "ios64",
        sys_id           => "iOS",
    },

    ## Apple iOS (armv7s)
    # Based on 10-main.conf: ios-cross
    "ios-cross-armv7s" => {
        inherit_from     => [ "darwin-common", "ios-cross-base", asm("armv4_asm") ],
        cflags           => add("-arch armv7s"),
        perlasm_scheme   => "ios32",
        sys_id           => "iOS",
    },

    ## Apple iOS (armv7)
    # Based on 10-main.conf: ios-cross
    "ios-cross-armv7" => {
        inherit_from     => [ "darwin-common", "ios-cross-base", asm("armv4_asm") ],
        cflags           => add("-arch armv7"),
        perlasm_scheme   => "ios32",
        sys_id           => "iOS",
    },

    ## Apple tvOS simulator (x86_64)
    # Based on 10-main.conf: iphoneos-cross / darwin64-x86_64-cc
    "tvos-sim-cross-x86_64" => {
        inherit_from     => [ "darwin64-x86_64-cc", "tvos-cross-base" ],
        sys_id           => "tvOS",
    },

    ## Apple tvOS (arm64)
    # Based on 10-main.conf: ios64-cross
    "tvos64-cross-arm64" => {
        inherit_from     => [ "darwin-common", "tvos-cross-base", asm("aarch64_asm") ],
        cflags           => add("-arch arm64"),
        bn_ops           => "SIXTY_FOUR_BIT_LONG RC4_CHAR",
        perlasm_scheme   => "ios64",
        sys_id           => "tvOS",
    },
);
