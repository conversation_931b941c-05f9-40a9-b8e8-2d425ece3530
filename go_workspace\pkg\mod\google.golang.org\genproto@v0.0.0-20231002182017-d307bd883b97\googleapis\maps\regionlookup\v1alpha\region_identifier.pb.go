// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.12.2
// source: google/maps/regionlookup/v1alpha/region_identifier.proto

package regionlookup

import (
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Possible place types to match to.
type RegionIdentifier_PlaceType int32

const (
	// Default value. This value is unused.
	RegionIdentifier_PLACE_TYPE_UNSPECIFIED RegionIdentifier_PlaceType = 0
	// Postal code.
	RegionIdentifier_POSTAL_CODE RegionIdentifier_PlaceType = 1
	// Administrative area level 1 (State in the US).
	RegionIdentifier_ADMINISTRATIVE_AREA_LEVEL_1 RegionIdentifier_PlaceType = 2
	// Administrative area level 2 (County in the US).
	RegionIdentifier_ADMINISTRATIVE_AREA_LEVEL_2 RegionIdentifier_PlaceType = 3
	// Locality (City).
	RegionIdentifier_LOCALITY RegionIdentifier_PlaceType = 4
	// Neighborhood.
	RegionIdentifier_NEIGHBORHOOD RegionIdentifier_PlaceType = 5
	// Country.
	RegionIdentifier_COUNTRY RegionIdentifier_PlaceType = 6
	// Sublocality.
	RegionIdentifier_SUBLOCALITY RegionIdentifier_PlaceType = 7
	// Administrative area level 3.
	RegionIdentifier_ADMINISTRATIVE_AREA_LEVEL_3 RegionIdentifier_PlaceType = 8
	// Administrative area level 4.
	RegionIdentifier_ADMINISTRATIVE_AREA_LEVEL_4 RegionIdentifier_PlaceType = 9
	// School district.
	RegionIdentifier_SCHOOL_DISTRICT RegionIdentifier_PlaceType = 10
)

// Enum value maps for RegionIdentifier_PlaceType.
var (
	RegionIdentifier_PlaceType_name = map[int32]string{
		0:  "PLACE_TYPE_UNSPECIFIED",
		1:  "POSTAL_CODE",
		2:  "ADMINISTRATIVE_AREA_LEVEL_1",
		3:  "ADMINISTRATIVE_AREA_LEVEL_2",
		4:  "LOCALITY",
		5:  "NEIGHBORHOOD",
		6:  "COUNTRY",
		7:  "SUBLOCALITY",
		8:  "ADMINISTRATIVE_AREA_LEVEL_3",
		9:  "ADMINISTRATIVE_AREA_LEVEL_4",
		10: "SCHOOL_DISTRICT",
	}
	RegionIdentifier_PlaceType_value = map[string]int32{
		"PLACE_TYPE_UNSPECIFIED":      0,
		"POSTAL_CODE":                 1,
		"ADMINISTRATIVE_AREA_LEVEL_1": 2,
		"ADMINISTRATIVE_AREA_LEVEL_2": 3,
		"LOCALITY":                    4,
		"NEIGHBORHOOD":                5,
		"COUNTRY":                     6,
		"SUBLOCALITY":                 7,
		"ADMINISTRATIVE_AREA_LEVEL_3": 8,
		"ADMINISTRATIVE_AREA_LEVEL_4": 9,
		"SCHOOL_DISTRICT":             10,
	}
)

func (x RegionIdentifier_PlaceType) Enum() *RegionIdentifier_PlaceType {
	p := new(RegionIdentifier_PlaceType)
	*p = x
	return p
}

func (x RegionIdentifier_PlaceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RegionIdentifier_PlaceType) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_regionlookup_v1alpha_region_identifier_proto_enumTypes[0].Descriptor()
}

func (RegionIdentifier_PlaceType) Type() protoreflect.EnumType {
	return &file_google_maps_regionlookup_v1alpha_region_identifier_proto_enumTypes[0]
}

func (x RegionIdentifier_PlaceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RegionIdentifier_PlaceType.Descriptor instead.
func (RegionIdentifier_PlaceType) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_regionlookup_v1alpha_region_identifier_proto_rawDescGZIP(), []int{0, 0}
}

// Region Identifier.
//
// Identifies a region to look up.
//
// One of place or unit_code must be specified. If none is specified,
// an INVALID_ARGUMENT error is returned. region_code must also be specified
// except when place_type is "country".
//
// place and unit_code specify a location to match a Place ID to. For
// example if place is "California" and region_code "US" the API
// returns the following matched_place_id results when the following
// place_types are specified:
//
// place_type:                   matched_place_id results:
// administrative_area_level_1   Place ID for The State of California
// (All other supported types)   No Match
//
// If unit_code is "6" (FIPs code for California) and region_code is "US
// the API returns the following matched_place_id results when the
// following place_types are specified:
//
// place type:                   matched_place_id results:
// administrative_area_level_1   Place ID for The State of California
// (All other supported types)   No Match
//
// or if unit_code is "US" the API returns the following results when
// the following place_types are specified:
//
// place type:                   matched_place_id results:
// country                       Place ID for the United States
// (All other supported types)   No Match
//
// If no match is found, matched_place_id is not set.
//
// Candidate Place IDs are returned when a lookup finds a region with a
// different place_type then the one requested. For example if place is
// "California" and place_type is "country" the Place ID for The State of
// California is returned as a candidate in the candidate_place_ids field.
//
// Next available tag: 10
type RegionIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The location must be specified by one of the following:
	//
	// Types that are assignable to Location:
	//	*RegionIdentifier_Place
	//	*RegionIdentifier_UnitCode
	Location isRegionIdentifier_Location `protobuf_oneof:"location"`
	// Required. Place type to match.
	PlaceType RegionIdentifier_PlaceType `protobuf:"varint,6,opt,name=place_type,json=placeType,proto3,enum=google.maps.regionlookup.v1alpha.RegionIdentifier_PlaceType" json:"place_type,omitempty"`
	// The BCP-47 language code, such as "en-US" or "sr-Latn", corresponding to
	// the language in which the place name and address is requested. If none is
	// requested, then it defaults to English.
	LanguageCode string `protobuf:"bytes,7,opt,name=language_code,json=languageCode,proto3" json:"language_code,omitempty"`
	// Two-letter ISO-3166 country/region code for the location you're trying to
	// match. region_code is optional if place_type is "country".
	RegionCode string `protobuf:"bytes,8,opt,name=region_code,json=regionCode,proto3" json:"region_code,omitempty"`
}

func (x *RegionIdentifier) Reset() {
	*x = RegionIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_regionlookup_v1alpha_region_identifier_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegionIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegionIdentifier) ProtoMessage() {}

func (x *RegionIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_regionlookup_v1alpha_region_identifier_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegionIdentifier.ProtoReflect.Descriptor instead.
func (*RegionIdentifier) Descriptor() ([]byte, []int) {
	return file_google_maps_regionlookup_v1alpha_region_identifier_proto_rawDescGZIP(), []int{0}
}

func (m *RegionIdentifier) GetLocation() isRegionIdentifier_Location {
	if m != nil {
		return m.Location
	}
	return nil
}

func (x *RegionIdentifier) GetPlace() string {
	if x, ok := x.GetLocation().(*RegionIdentifier_Place); ok {
		return x.Place
	}
	return ""
}

func (x *RegionIdentifier) GetUnitCode() string {
	if x, ok := x.GetLocation().(*RegionIdentifier_UnitCode); ok {
		return x.UnitCode
	}
	return ""
}

func (x *RegionIdentifier) GetPlaceType() RegionIdentifier_PlaceType {
	if x != nil {
		return x.PlaceType
	}
	return RegionIdentifier_PLACE_TYPE_UNSPECIFIED
}

func (x *RegionIdentifier) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

func (x *RegionIdentifier) GetRegionCode() string {
	if x != nil {
		return x.RegionCode
	}
	return ""
}

type isRegionIdentifier_Location interface {
	isRegionIdentifier_Location()
}

type RegionIdentifier_Place struct {
	// The name of the region to match to a Place ID.
	//
	// The place field is used in combination with place_type to look up
	// the region Place ID.
	//
	// For example:
	// If place_type is "locality", a valid place can be "Palo Alto, CA".
	//
	// If place_type is "postal_code", a valid place can be "94109".
	//
	// If place_type is "country", a valid place can be "United States".
	// etc.
	//
	// region_code is required when place is specified except when
	// place_type is "country".
	Place string `protobuf:"bytes,4,opt,name=place,proto3,oneof"`
}

type RegionIdentifier_UnitCode struct {
	// The FIPs state or county codes (US only) or ISO-3166-1 country code to be
	// matched.
	//
	// The unit_code field is used in combination with place_type to look up
	// the region Place ID.
	//
	// For example:
	// If place_type is "country", a valid unit_code can be "US" (ISO-3166-1
	// Alpha-2 code for United States) or "BR" (ISO-3166-1 Alpha-2 code for
	// Brazil).
	//
	// If place_type is "administrative_area_level_1" (state) and region_code is
	// "US", a valid unit_code can be "6" (FIPs code for California) or
	// "12"(FIPs code for Florida).
	//
	// If place_type is "administrative_area_level_2" (county) and region_code
	// is "US", a valid unit_code can be "6001" (FIPs code for Alameda County in
	// California) or "12086" (FIPs code for Miami-Dade County in Florida).
	//
	// region_code is required when specifying a FIPs code. region_code is
	// ignored for ISO-3166-1 country codes.
	UnitCode string `protobuf:"bytes,5,opt,name=unit_code,json=unitCode,proto3,oneof"`
}

func (*RegionIdentifier_Place) isRegionIdentifier_Location() {}

func (*RegionIdentifier_UnitCode) isRegionIdentifier_Location() {}

var File_google_maps_regionlookup_v1alpha_region_identifier_proto protoreflect.FileDescriptor

var file_google_maps_regionlookup_v1alpha_region_identifier_proto_rawDesc = []byte{
	0x0a, 0x38, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x2f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x20, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f,
	0x6f, 0x6b, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62,
	0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8f, 0x04,
	0x0a, 0x10, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x12, 0x16, 0x0a, 0x05, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x05, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x09, 0x75, 0x6e,
	0x69, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x08, 0x75, 0x6e, 0x69, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x60, 0x0a, 0x0a, 0x70, 0x6c, 0x61,
	0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x2e, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x09, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x22, 0x8f, 0x02, 0x0a, 0x09, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1a, 0x0a, 0x16, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x50,
	0x4f, 0x53, 0x54, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b,
	0x41, 0x44, 0x4d, 0x49, 0x4e, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x41,
	0x52, 0x45, 0x41, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x31, 0x10, 0x02, 0x12, 0x1f, 0x0a,
	0x1b, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x56, 0x45, 0x5f,
	0x41, 0x52, 0x45, 0x41, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x32, 0x10, 0x03, 0x12, 0x0c,
	0x0a, 0x08, 0x4c, 0x4f, 0x43, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x04, 0x12, 0x10, 0x0a, 0x0c,
	0x4e, 0x45, 0x49, 0x47, 0x48, 0x42, 0x4f, 0x52, 0x48, 0x4f, 0x4f, 0x44, 0x10, 0x05, 0x12, 0x0b,
	0x0a, 0x07, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59, 0x10, 0x06, 0x12, 0x0f, 0x0a, 0x0b, 0x53,
	0x55, 0x42, 0x4c, 0x4f, 0x43, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x07, 0x12, 0x1f, 0x0a, 0x1b,
	0x41, 0x44, 0x4d, 0x49, 0x4e, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x41,
	0x52, 0x45, 0x41, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x33, 0x10, 0x08, 0x12, 0x1f, 0x0a,
	0x1b, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x56, 0x45, 0x5f,
	0x41, 0x52, 0x45, 0x41, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x34, 0x10, 0x09, 0x12, 0x13,
	0x0a, 0x0f, 0x53, 0x43, 0x48, 0x4f, 0x4f, 0x4c, 0x5f, 0x44, 0x49, 0x53, 0x54, 0x52, 0x49, 0x43,
	0x54, 0x10, 0x0a, 0x42, 0x0a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0xdf, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d,
	0x61, 0x70, 0x73, 0x2e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70,
	0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x42, 0x15, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50,
	0x01, 0x5a, 0x4c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67,
	0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x3b, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0xf8,
	0x01, 0x01, 0xa2, 0x02, 0x06, 0x4d, 0x52, 0x4c, 0x56, 0x31, 0x41, 0xaa, 0x02, 0x20, 0x47, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x4d, 0x61, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x2e, 0x56, 0x31, 0x41, 0x6c, 0x70, 0x68, 0x61, 0xca, 0x02,
	0x20, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x4d, 0x61, 0x70, 0x73, 0x5c, 0x52, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x5c, 0x56, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_maps_regionlookup_v1alpha_region_identifier_proto_rawDescOnce sync.Once
	file_google_maps_regionlookup_v1alpha_region_identifier_proto_rawDescData = file_google_maps_regionlookup_v1alpha_region_identifier_proto_rawDesc
)

func file_google_maps_regionlookup_v1alpha_region_identifier_proto_rawDescGZIP() []byte {
	file_google_maps_regionlookup_v1alpha_region_identifier_proto_rawDescOnce.Do(func() {
		file_google_maps_regionlookup_v1alpha_region_identifier_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_maps_regionlookup_v1alpha_region_identifier_proto_rawDescData)
	})
	return file_google_maps_regionlookup_v1alpha_region_identifier_proto_rawDescData
}

var file_google_maps_regionlookup_v1alpha_region_identifier_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_google_maps_regionlookup_v1alpha_region_identifier_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_google_maps_regionlookup_v1alpha_region_identifier_proto_goTypes = []interface{}{
	(RegionIdentifier_PlaceType)(0), // 0: google.maps.regionlookup.v1alpha.RegionIdentifier.PlaceType
	(*RegionIdentifier)(nil),        // 1: google.maps.regionlookup.v1alpha.RegionIdentifier
}
var file_google_maps_regionlookup_v1alpha_region_identifier_proto_depIdxs = []int32{
	0, // 0: google.maps.regionlookup.v1alpha.RegionIdentifier.place_type:type_name -> google.maps.regionlookup.v1alpha.RegionIdentifier.PlaceType
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_google_maps_regionlookup_v1alpha_region_identifier_proto_init() }
func file_google_maps_regionlookup_v1alpha_region_identifier_proto_init() {
	if File_google_maps_regionlookup_v1alpha_region_identifier_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_maps_regionlookup_v1alpha_region_identifier_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegionIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_maps_regionlookup_v1alpha_region_identifier_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*RegionIdentifier_Place)(nil),
		(*RegionIdentifier_UnitCode)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_maps_regionlookup_v1alpha_region_identifier_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_maps_regionlookup_v1alpha_region_identifier_proto_goTypes,
		DependencyIndexes: file_google_maps_regionlookup_v1alpha_region_identifier_proto_depIdxs,
		EnumInfos:         file_google_maps_regionlookup_v1alpha_region_identifier_proto_enumTypes,
		MessageInfos:      file_google_maps_regionlookup_v1alpha_region_identifier_proto_msgTypes,
	}.Build()
	File_google_maps_regionlookup_v1alpha_region_identifier_proto = out.File
	file_google_maps_regionlookup_v1alpha_region_identifier_proto_rawDesc = nil
	file_google_maps_regionlookup_v1alpha_region_identifier_proto_goTypes = nil
	file_google_maps_regionlookup_v1alpha_region_identifier_proto_depIdxs = nil
}
