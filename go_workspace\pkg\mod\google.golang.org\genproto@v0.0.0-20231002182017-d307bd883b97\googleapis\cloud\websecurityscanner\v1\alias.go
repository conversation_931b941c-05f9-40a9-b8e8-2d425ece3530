// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by aliasgen. DO NOT EDIT.

// Package websecurityscanner aliases all exported identifiers in package
// "cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb".
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb.
// Please read https://github.com/googleapis/google-cloud-go/blob/main/migration.md
// for more details.
package websecurityscanner

import (
	src "cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb"
	grpc "google.golang.org/grpc"
)

// Deprecated: Please use consts in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
const (
	Finding_CRITICAL                                                    = src.Finding_CRITICAL
	Finding_HIGH                                                        = src.Finding_HIGH
	Finding_LOW                                                         = src.Finding_LOW
	Finding_MEDIUM                                                      = src.Finding_MEDIUM
	Finding_SEVERITY_UNSPECIFIED                                        = src.Finding_SEVERITY_UNSPECIFIED
	ScanConfigError_APPENGINE_API_BACKEND_ERROR                         = src.ScanConfigError_APPENGINE_API_BACKEND_ERROR
	ScanConfigError_APPENGINE_API_NOT_ACCESSIBLE                        = src.ScanConfigError_APPENGINE_API_NOT_ACCESSIBLE
	ScanConfigError_APPENGINE_DEFAULT_HOST_MISSING                      = src.ScanConfigError_APPENGINE_DEFAULT_HOST_MISSING
	ScanConfigError_CANNOT_USE_GOOGLE_COM_ACCOUNT                       = src.ScanConfigError_CANNOT_USE_GOOGLE_COM_ACCOUNT
	ScanConfigError_CANNOT_USE_OWNER_ACCOUNT                            = src.ScanConfigError_CANNOT_USE_OWNER_ACCOUNT
	ScanConfigError_CODE_UNSPECIFIED                                    = src.ScanConfigError_CODE_UNSPECIFIED
	ScanConfigError_COMPUTE_API_BACKEND_ERROR                           = src.ScanConfigError_COMPUTE_API_BACKEND_ERROR
	ScanConfigError_COMPUTE_API_NOT_ACCESSIBLE                          = src.ScanConfigError_COMPUTE_API_NOT_ACCESSIBLE
	ScanConfigError_CUSTOM_LOGIN_URL_DOES_NOT_BELONG_TO_CURRENT_PROJECT = src.ScanConfigError_CUSTOM_LOGIN_URL_DOES_NOT_BELONG_TO_CURRENT_PROJECT
	ScanConfigError_CUSTOM_LOGIN_URL_HAS_NON_ROUTABLE_IP_ADDRESS        = src.ScanConfigError_CUSTOM_LOGIN_URL_HAS_NON_ROUTABLE_IP_ADDRESS
	ScanConfigError_CUSTOM_LOGIN_URL_HAS_UNRESERVED_IP_ADDRESS          = src.ScanConfigError_CUSTOM_LOGIN_URL_HAS_UNRESERVED_IP_ADDRESS
	ScanConfigError_CUSTOM_LOGIN_URL_MALFORMED                          = src.ScanConfigError_CUSTOM_LOGIN_URL_MALFORMED
	ScanConfigError_CUSTOM_LOGIN_URL_MAPPED_TO_NON_ROUTABLE_ADDRESS     = src.ScanConfigError_CUSTOM_LOGIN_URL_MAPPED_TO_NON_ROUTABLE_ADDRESS
	ScanConfigError_CUSTOM_LOGIN_URL_MAPPED_TO_UNRESERVED_ADDRESS       = src.ScanConfigError_CUSTOM_LOGIN_URL_MAPPED_TO_UNRESERVED_ADDRESS
	ScanConfigError_DUPLICATE_SCAN_NAME                                 = src.ScanConfigError_DUPLICATE_SCAN_NAME
	ScanConfigError_FAILED_TO_AUTHENTICATE_TO_TARGET                    = src.ScanConfigError_FAILED_TO_AUTHENTICATE_TO_TARGET
	ScanConfigError_FINDING_TYPE_UNSPECIFIED                            = src.ScanConfigError_FINDING_TYPE_UNSPECIFIED
	ScanConfigError_FORBIDDEN_TO_SCAN_COMPUTE                           = src.ScanConfigError_FORBIDDEN_TO_SCAN_COMPUTE
	ScanConfigError_FORBIDDEN_UPDATE_TO_MANAGED_SCAN                    = src.ScanConfigError_FORBIDDEN_UPDATE_TO_MANAGED_SCAN
	ScanConfigError_INTERNAL_ERROR                                      = src.ScanConfigError_INTERNAL_ERROR
	ScanConfigError_INVALID_FIELD_VALUE                                 = src.ScanConfigError_INVALID_FIELD_VALUE
	ScanConfigError_MALFORMED_FILTER                                    = src.ScanConfigError_MALFORMED_FILTER
	ScanConfigError_MALFORMED_RESOURCE_NAME                             = src.ScanConfigError_MALFORMED_RESOURCE_NAME
	ScanConfigError_OK                                                  = src.ScanConfigError_OK
	ScanConfigError_PROJECT_INACTIVE                                    = src.ScanConfigError_PROJECT_INACTIVE
	ScanConfigError_REQUIRED_FIELD                                      = src.ScanConfigError_REQUIRED_FIELD
	ScanConfigError_RESOURCE_NAME_INCONSISTENT                          = src.ScanConfigError_RESOURCE_NAME_INCONSISTENT
	ScanConfigError_SCAN_ALREADY_RUNNING                                = src.ScanConfigError_SCAN_ALREADY_RUNNING
	ScanConfigError_SCAN_NOT_RUNNING                                    = src.ScanConfigError_SCAN_NOT_RUNNING
	ScanConfigError_SEED_URL_DOES_NOT_BELONG_TO_CURRENT_PROJECT         = src.ScanConfigError_SEED_URL_DOES_NOT_BELONG_TO_CURRENT_PROJECT
	ScanConfigError_SEED_URL_HAS_NON_ROUTABLE_IP_ADDRESS                = src.ScanConfigError_SEED_URL_HAS_NON_ROUTABLE_IP_ADDRESS
	ScanConfigError_SEED_URL_HAS_UNRESERVED_IP_ADDRESS                  = src.ScanConfigError_SEED_URL_HAS_UNRESERVED_IP_ADDRESS
	ScanConfigError_SEED_URL_MALFORMED                                  = src.ScanConfigError_SEED_URL_MALFORMED
	ScanConfigError_SEED_URL_MAPPED_TO_NON_ROUTABLE_ADDRESS             = src.ScanConfigError_SEED_URL_MAPPED_TO_NON_ROUTABLE_ADDRESS
	ScanConfigError_SEED_URL_MAPPED_TO_UNRESERVED_ADDRESS               = src.ScanConfigError_SEED_URL_MAPPED_TO_UNRESERVED_ADDRESS
	ScanConfigError_SERVICE_ACCOUNT_NOT_CONFIGURED                      = src.ScanConfigError_SERVICE_ACCOUNT_NOT_CONFIGURED
	ScanConfigError_TOO_MANY_SCANS                                      = src.ScanConfigError_TOO_MANY_SCANS
	ScanConfigError_UNABLE_TO_RESOLVE_PROJECT_INFO                      = src.ScanConfigError_UNABLE_TO_RESOLVE_PROJECT_INFO
	ScanConfigError_UNSUPPORTED_BLACKLIST_PATTERN_FORMAT                = src.ScanConfigError_UNSUPPORTED_BLACKLIST_PATTERN_FORMAT
	ScanConfigError_UNSUPPORTED_FILTER                                  = src.ScanConfigError_UNSUPPORTED_FILTER
	ScanConfigError_UNSUPPORTED_FINDING_TYPE                            = src.ScanConfigError_UNSUPPORTED_FINDING_TYPE
	ScanConfigError_UNSUPPORTED_URL_SCHEME                              = src.ScanConfigError_UNSUPPORTED_URL_SCHEME
	ScanConfig_CHROME_ANDROID                                           = src.ScanConfig_CHROME_ANDROID
	ScanConfig_CHROME_LINUX                                             = src.ScanConfig_CHROME_LINUX
	ScanConfig_DISABLED                                                 = src.ScanConfig_DISABLED
	ScanConfig_ENABLED                                                  = src.ScanConfig_ENABLED
	ScanConfig_EXPORT_TO_SECURITY_COMMAND_CENTER_UNSPECIFIED            = src.ScanConfig_EXPORT_TO_SECURITY_COMMAND_CENTER_UNSPECIFIED
	ScanConfig_LOW                                                      = src.ScanConfig_LOW
	ScanConfig_NORMAL                                                   = src.ScanConfig_NORMAL
	ScanConfig_RISK_LEVEL_UNSPECIFIED                                   = src.ScanConfig_RISK_LEVEL_UNSPECIFIED
	ScanConfig_SAFARI_IPHONE                                            = src.ScanConfig_SAFARI_IPHONE
	ScanConfig_USER_AGENT_UNSPECIFIED                                   = src.ScanConfig_USER_AGENT_UNSPECIFIED
	ScanRunErrorTrace_AUTHENTICATION_CONFIG_ISSUE                       = src.ScanRunErrorTrace_AUTHENTICATION_CONFIG_ISSUE
	ScanRunErrorTrace_CODE_UNSPECIFIED                                  = src.ScanRunErrorTrace_CODE_UNSPECIFIED
	ScanRunErrorTrace_INTERNAL_ERROR                                    = src.ScanRunErrorTrace_INTERNAL_ERROR
	ScanRunErrorTrace_SCAN_CONFIG_ISSUE                                 = src.ScanRunErrorTrace_SCAN_CONFIG_ISSUE
	ScanRunErrorTrace_TIMED_OUT_WHILE_SCANNING                          = src.ScanRunErrorTrace_TIMED_OUT_WHILE_SCANNING
	ScanRunErrorTrace_TOO_MANY_HTTP_ERRORS                              = src.ScanRunErrorTrace_TOO_MANY_HTTP_ERRORS
	ScanRunErrorTrace_TOO_MANY_REDIRECTS                                = src.ScanRunErrorTrace_TOO_MANY_REDIRECTS
	ScanRunWarningTrace_BLOCKED_BY_IAP                                  = src.ScanRunWarningTrace_BLOCKED_BY_IAP
	ScanRunWarningTrace_CODE_UNSPECIFIED                                = src.ScanRunWarningTrace_CODE_UNSPECIFIED
	ScanRunWarningTrace_INSUFFICIENT_CRAWL_RESULTS                      = src.ScanRunWarningTrace_INSUFFICIENT_CRAWL_RESULTS
	ScanRunWarningTrace_NO_STARTING_URL_FOUND_FOR_MANAGED_SCAN          = src.ScanRunWarningTrace_NO_STARTING_URL_FOUND_FOR_MANAGED_SCAN
	ScanRunWarningTrace_TOO_MANY_CRAWL_RESULTS                          = src.ScanRunWarningTrace_TOO_MANY_CRAWL_RESULTS
	ScanRunWarningTrace_TOO_MANY_FUZZ_TASKS                             = src.ScanRunWarningTrace_TOO_MANY_FUZZ_TASKS
	ScanRun_ERROR                                                       = src.ScanRun_ERROR
	ScanRun_EXECUTION_STATE_UNSPECIFIED                                 = src.ScanRun_EXECUTION_STATE_UNSPECIFIED
	ScanRun_FINISHED                                                    = src.ScanRun_FINISHED
	ScanRun_KILLED                                                      = src.ScanRun_KILLED
	ScanRun_QUEUED                                                      = src.ScanRun_QUEUED
	ScanRun_RESULT_STATE_UNSPECIFIED                                    = src.ScanRun_RESULT_STATE_UNSPECIFIED
	ScanRun_SCANNING                                                    = src.ScanRun_SCANNING
	ScanRun_SUCCESS                                                     = src.ScanRun_SUCCESS
	Xss_ATTACK_VECTOR_UNSPECIFIED                                       = src.Xss_ATTACK_VECTOR_UNSPECIFIED
	Xss_COOKIE                                                          = src.Xss_COOKIE
	Xss_FORM_INPUT                                                      = src.Xss_FORM_INPUT
	Xss_GET_PARAMETERS                                                  = src.Xss_GET_PARAMETERS
	Xss_HTML_COMMENT                                                    = src.Xss_HTML_COMMENT
	Xss_LOCAL_STORAGE                                                   = src.Xss_LOCAL_STORAGE
	Xss_POST_MESSAGE                                                    = src.Xss_POST_MESSAGE
	Xss_POST_PARAMETERS                                                 = src.Xss_POST_PARAMETERS
	Xss_PROTOCOL                                                        = src.Xss_PROTOCOL
	Xss_REFERRER                                                        = src.Xss_REFERRER
	Xss_SAME_ORIGIN                                                     = src.Xss_SAME_ORIGIN
	Xss_SESSION_STORAGE                                                 = src.Xss_SESSION_STORAGE
	Xss_STORED_XSS                                                      = src.Xss_STORED_XSS
	Xss_URL_FRAGMENT                                                    = src.Xss_URL_FRAGMENT
	Xss_USER_CONTROLLABLE_URL                                           = src.Xss_USER_CONTROLLABLE_URL
	Xss_WINDOW_NAME                                                     = src.Xss_WINDOW_NAME
	Xxe_COMPLETE_REQUEST_BODY                                           = src.Xxe_COMPLETE_REQUEST_BODY
	Xxe_LOCATION_UNSPECIFIED                                            = src.Xxe_LOCATION_UNSPECIFIED
)

// Deprecated: Please use vars in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
var (
	File_google_cloud_websecurityscanner_v1_crawled_url_proto            = src.File_google_cloud_websecurityscanner_v1_crawled_url_proto
	File_google_cloud_websecurityscanner_v1_finding_addon_proto          = src.File_google_cloud_websecurityscanner_v1_finding_addon_proto
	File_google_cloud_websecurityscanner_v1_finding_proto                = src.File_google_cloud_websecurityscanner_v1_finding_proto
	File_google_cloud_websecurityscanner_v1_finding_type_stats_proto     = src.File_google_cloud_websecurityscanner_v1_finding_type_stats_proto
	File_google_cloud_websecurityscanner_v1_scan_config_error_proto      = src.File_google_cloud_websecurityscanner_v1_scan_config_error_proto
	File_google_cloud_websecurityscanner_v1_scan_config_proto            = src.File_google_cloud_websecurityscanner_v1_scan_config_proto
	File_google_cloud_websecurityscanner_v1_scan_run_error_trace_proto   = src.File_google_cloud_websecurityscanner_v1_scan_run_error_trace_proto
	File_google_cloud_websecurityscanner_v1_scan_run_log_proto           = src.File_google_cloud_websecurityscanner_v1_scan_run_log_proto
	File_google_cloud_websecurityscanner_v1_scan_run_proto               = src.File_google_cloud_websecurityscanner_v1_scan_run_proto
	File_google_cloud_websecurityscanner_v1_scan_run_warning_trace_proto = src.File_google_cloud_websecurityscanner_v1_scan_run_warning_trace_proto
	File_google_cloud_websecurityscanner_v1_web_security_scanner_proto   = src.File_google_cloud_websecurityscanner_v1_web_security_scanner_proto
	Finding_Severity_name                                                = src.Finding_Severity_name
	Finding_Severity_value                                               = src.Finding_Severity_value
	ScanConfigError_Code_name                                            = src.ScanConfigError_Code_name
	ScanConfigError_Code_value                                           = src.ScanConfigError_Code_value
	ScanConfig_ExportToSecurityCommandCenter_name                        = src.ScanConfig_ExportToSecurityCommandCenter_name
	ScanConfig_ExportToSecurityCommandCenter_value                       = src.ScanConfig_ExportToSecurityCommandCenter_value
	ScanConfig_RiskLevel_name                                            = src.ScanConfig_RiskLevel_name
	ScanConfig_RiskLevel_value                                           = src.ScanConfig_RiskLevel_value
	ScanConfig_UserAgent_name                                            = src.ScanConfig_UserAgent_name
	ScanConfig_UserAgent_value                                           = src.ScanConfig_UserAgent_value
	ScanRunErrorTrace_Code_name                                          = src.ScanRunErrorTrace_Code_name
	ScanRunErrorTrace_Code_value                                         = src.ScanRunErrorTrace_Code_value
	ScanRunWarningTrace_Code_name                                        = src.ScanRunWarningTrace_Code_name
	ScanRunWarningTrace_Code_value                                       = src.ScanRunWarningTrace_Code_value
	ScanRun_ExecutionState_name                                          = src.ScanRun_ExecutionState_name
	ScanRun_ExecutionState_value                                         = src.ScanRun_ExecutionState_value
	ScanRun_ResultState_name                                             = src.ScanRun_ResultState_name
	ScanRun_ResultState_value                                            = src.ScanRun_ResultState_value
	Xss_AttackVector_name                                                = src.Xss_AttackVector_name
	Xss_AttackVector_value                                               = src.Xss_AttackVector_value
	Xxe_Location_name                                                    = src.Xxe_Location_name
	Xxe_Location_value                                                   = src.Xxe_Location_value
)

// A CrawledUrl resource represents a URL that was crawled during a ScanRun.
// Web Security Scanner Service crawls the web applications, following all
// links within the scope of sites, to find the URLs to test against.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type CrawledUrl = src.CrawledUrl

// Request for the `CreateScanConfig` method.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type CreateScanConfigRequest = src.CreateScanConfigRequest

// Request for the `DeleteScanConfig` method.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type DeleteScanConfigRequest = src.DeleteScanConfigRequest

// A Finding resource represents a vulnerability instance identified during a
// ScanRun.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type Finding = src.Finding

// A FindingTypeStats resource represents stats regarding a specific
// FindingType of Findings under a given ScanRun.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type FindingTypeStats = src.FindingTypeStats

// The severity level of a vulnerability.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type Finding_Severity = src.Finding_Severity

// ! Information about a vulnerability with an HTML.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type Form = src.Form

// Request for the `GetFinding` method.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type GetFindingRequest = src.GetFindingRequest

// Request for the `GetScanConfig` method.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type GetScanConfigRequest = src.GetScanConfigRequest

// Request for the `GetScanRun` method.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type GetScanRunRequest = src.GetScanRunRequest

// Request for the `ListCrawledUrls` method.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ListCrawledUrlsRequest = src.ListCrawledUrlsRequest

// Response for the `ListCrawledUrls` method.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ListCrawledUrlsResponse = src.ListCrawledUrlsResponse

// Request for the `ListFindingTypeStats` method.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ListFindingTypeStatsRequest = src.ListFindingTypeStatsRequest

// Response for the `ListFindingTypeStats` method.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ListFindingTypeStatsResponse = src.ListFindingTypeStatsResponse

// Request for the `ListFindings` method.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ListFindingsRequest = src.ListFindingsRequest

// Response for the `ListFindings` method.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ListFindingsResponse = src.ListFindingsResponse

// Request for the `ListScanConfigs` method.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ListScanConfigsRequest = src.ListScanConfigsRequest

// Response for the `ListScanConfigs` method.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ListScanConfigsResponse = src.ListScanConfigsResponse

// Request for the `ListScanRuns` method.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ListScanRunsRequest = src.ListScanRunsRequest

// Response for the `ListScanRuns` method.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ListScanRunsResponse = src.ListScanRunsResponse

// Information reported for an outdated library.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type OutdatedLibrary = src.OutdatedLibrary

// A ScanConfig resource contains the configurations to launch a scan.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanConfig = src.ScanConfig

// Defines a custom error message used by CreateScanConfig and
// UpdateScanConfig APIs when scan configuration validation fails. It is also
// reported as part of a ScanRunErrorTrace message if scan validation fails due
// to a scan configuration error.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanConfigError = src.ScanConfigError

// Output only. Defines an error reason code. Next id: 44
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanConfigError_Code = src.ScanConfigError_Code

// Scan authentication configuration.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanConfig_Authentication = src.ScanConfig_Authentication

// Describes authentication configuration that uses a custom account.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanConfig_Authentication_CustomAccount = src.ScanConfig_Authentication_CustomAccount
type ScanConfig_Authentication_CustomAccount_ = src.ScanConfig_Authentication_CustomAccount_

// Describes authentication configuration that uses a Google account.
// Deprecated: Do not use.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanConfig_Authentication_GoogleAccount = src.ScanConfig_Authentication_GoogleAccount
type ScanConfig_Authentication_GoogleAccount_ = src.ScanConfig_Authentication_GoogleAccount_

// Describes authentication configuration for Identity-Aware-Proxy (IAP).
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanConfig_Authentication_IapCredential = src.ScanConfig_Authentication_IapCredential
type ScanConfig_Authentication_IapCredential_ = src.ScanConfig_Authentication_IapCredential_

// Describes authentication configuration when Web-Security-Scanner service
// account is added in Identity-Aware-Proxy (IAP) access policies.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanConfig_Authentication_IapCredential_IapTestServiceAccountInfo = src.ScanConfig_Authentication_IapCredential_IapTestServiceAccountInfo
type ScanConfig_Authentication_IapCredential_IapTestServiceAccountInfo_ = src.ScanConfig_Authentication_IapCredential_IapTestServiceAccountInfo_

// Controls export of scan configurations and results to Security Command
// Center.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanConfig_ExportToSecurityCommandCenter = src.ScanConfig_ExportToSecurityCommandCenter

// Scan risk levels supported by Web Security Scanner. LOW impact scanning
// will minimize requests with the potential to modify data. To achieve the
// maximum scan coverage, NORMAL risk level is recommended.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanConfig_RiskLevel = src.ScanConfig_RiskLevel

// Scan schedule configuration.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanConfig_Schedule = src.ScanConfig_Schedule

// Type of user agents used for scanning.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanConfig_UserAgent = src.ScanConfig_UserAgent

// A ScanRun is a output-only resource representing an actual run of the scan.
// Next id: 12
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanRun = src.ScanRun

// Output only. Defines an error trace message for a ScanRun.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanRunErrorTrace = src.ScanRunErrorTrace

// Output only. Defines an error reason code. Next id: 8
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanRunErrorTrace_Code = src.ScanRunErrorTrace_Code

// A ScanRunLog is an output-only proto used for Stackdriver customer logging.
// It is used for logs covering the start and end of scan pipelines. Other than
// an added summary, this is a subset of the ScanRun. Representation in logs is
// either a proto Struct, or converted to JSON. Next id: 9
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanRunLog = src.ScanRunLog

// Output only. Defines a warning trace message for ScanRun. Warning traces
// provide customers with useful information that helps make the scanning
// process more effective.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanRunWarningTrace = src.ScanRunWarningTrace

// Output only. Defines a warning message code. Next id: 6
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanRunWarningTrace_Code = src.ScanRunWarningTrace_Code

// Types of ScanRun execution state.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanRun_ExecutionState = src.ScanRun_ExecutionState

// Types of ScanRun result state.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ScanRun_ResultState = src.ScanRun_ResultState

// Request for the `StartScanRun` method.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type StartScanRunRequest = src.StartScanRunRequest

// Request for the `StopScanRun` method.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type StopScanRunRequest = src.StopScanRunRequest

// UnimplementedWebSecurityScannerServer can be embedded to have forward
// compatible implementations.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type UnimplementedWebSecurityScannerServer = src.UnimplementedWebSecurityScannerServer

// Request for the `UpdateScanConfigRequest` method.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type UpdateScanConfigRequest = src.UpdateScanConfigRequest

// Information regarding any resource causing the vulnerability such as
// JavaScript sources, image, audio files, etc.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type ViolatingResource = src.ViolatingResource

// Information about vulnerable or missing HTTP Headers.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type VulnerableHeaders = src.VulnerableHeaders

// Describes a HTTP Header.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type VulnerableHeaders_Header = src.VulnerableHeaders_Header

// Information about vulnerable request parameters.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type VulnerableParameters = src.VulnerableParameters

// WebSecurityScannerClient is the client API for WebSecurityScanner service.
// For semantics around ctx use and closing/ending streaming RPCs, please refer
// to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type WebSecurityScannerClient = src.WebSecurityScannerClient

// WebSecurityScannerServer is the server API for WebSecurityScanner service.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type WebSecurityScannerServer = src.WebSecurityScannerServer

// Information reported for an XSS.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type Xss = src.Xss

// Types of XSS attack vector.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type Xss_AttackVector = src.Xss_AttackVector

// Information reported for an XXE.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type Xxe = src.Xxe

// Locations within a request where XML was substituted.
//
// Deprecated: Please use types in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
type Xxe_Location = src.Xxe_Location

// Deprecated: Please use funcs in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
func NewWebSecurityScannerClient(cc grpc.ClientConnInterface) WebSecurityScannerClient {
	return src.NewWebSecurityScannerClient(cc)
}

// Deprecated: Please use funcs in: cloud.google.com/go/websecurityscanner/apiv1/websecurityscannerpb
func RegisterWebSecurityScannerServer(s *grpc.Server, srv WebSecurityScannerServer) {
	src.RegisterWebSecurityScannerServer(s, srv)
}
