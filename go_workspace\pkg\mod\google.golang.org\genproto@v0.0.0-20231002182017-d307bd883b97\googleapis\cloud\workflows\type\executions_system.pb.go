// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.12.2
// source: google/cloud/workflows/type/executions_system.proto

package _type

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Possible states of the execution. There could be more states in the future.
type ExecutionsSystemLog_State int32

const (
	// Invalid state.
	ExecutionsSystemLog_STATE_UNSPECIFIED ExecutionsSystemLog_State = 0
	// The Workflow Execution is in progress.
	ExecutionsSystemLog_ACTIVE ExecutionsSystemLog_State = 1
	// The Workflow Execution has finished successfully.
	ExecutionsSystemLog_SUCCEEDED ExecutionsSystemLog_State = 2
	// The Workflow Execution failed with an error.
	ExecutionsSystemLog_FAILED ExecutionsSystemLog_State = 3
	// The Workflow Execution has been stopped intentionally.
	ExecutionsSystemLog_CANCELLED ExecutionsSystemLog_State = 4
)

// Enum value maps for ExecutionsSystemLog_State.
var (
	ExecutionsSystemLog_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "ACTIVE",
		2: "SUCCEEDED",
		3: "FAILED",
		4: "CANCELLED",
	}
	ExecutionsSystemLog_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"ACTIVE":            1,
		"SUCCEEDED":         2,
		"FAILED":            3,
		"CANCELLED":         4,
	}
)

func (x ExecutionsSystemLog_State) Enum() *ExecutionsSystemLog_State {
	p := new(ExecutionsSystemLog_State)
	*p = x
	return p
}

func (x ExecutionsSystemLog_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExecutionsSystemLog_State) Descriptor() protoreflect.EnumDescriptor {
	return file_google_cloud_workflows_type_executions_system_proto_enumTypes[0].Descriptor()
}

func (ExecutionsSystemLog_State) Type() protoreflect.EnumType {
	return &file_google_cloud_workflows_type_executions_system_proto_enumTypes[0]
}

func (x ExecutionsSystemLog_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExecutionsSystemLog_State.Descriptor instead.
func (ExecutionsSystemLog_State) EnumDescriptor() ([]byte, []int) {
	return file_google_cloud_workflows_type_executions_system_proto_rawDescGZIP(), []int{0, 0}
}

// Logged during the lifetime of Workflow Execution.
type ExecutionsSystemLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Human readable contents of the log in English. The size limit is 5 kB.
	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	// The absolute point in time when the activity happened.
	ActivityTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=activity_time,json=activityTime,proto3" json:"activity_time,omitempty"`
	// State of the execution when the log was created.
	State ExecutionsSystemLog_State `protobuf:"varint,3,opt,name=state,proto3,enum=google.cloud.workflows.type.ExecutionsSystemLog_State" json:"state,omitempty"`
	// Detailed log information.
	//
	// Types that are assignable to Details:
	//	*ExecutionsSystemLog_Start_
	//	*ExecutionsSystemLog_Success_
	//	*ExecutionsSystemLog_Failure_
	Details isExecutionsSystemLog_Details `protobuf_oneof:"details"`
}

func (x *ExecutionsSystemLog) Reset() {
	*x = ExecutionsSystemLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_workflows_type_executions_system_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecutionsSystemLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutionsSystemLog) ProtoMessage() {}

func (x *ExecutionsSystemLog) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_workflows_type_executions_system_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutionsSystemLog.ProtoReflect.Descriptor instead.
func (*ExecutionsSystemLog) Descriptor() ([]byte, []int) {
	return file_google_cloud_workflows_type_executions_system_proto_rawDescGZIP(), []int{0}
}

func (x *ExecutionsSystemLog) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ExecutionsSystemLog) GetActivityTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ActivityTime
	}
	return nil
}

func (x *ExecutionsSystemLog) GetState() ExecutionsSystemLog_State {
	if x != nil {
		return x.State
	}
	return ExecutionsSystemLog_STATE_UNSPECIFIED
}

func (m *ExecutionsSystemLog) GetDetails() isExecutionsSystemLog_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *ExecutionsSystemLog) GetStart() *ExecutionsSystemLog_Start {
	if x, ok := x.GetDetails().(*ExecutionsSystemLog_Start_); ok {
		return x.Start
	}
	return nil
}

func (x *ExecutionsSystemLog) GetSuccess() *ExecutionsSystemLog_Success {
	if x, ok := x.GetDetails().(*ExecutionsSystemLog_Success_); ok {
		return x.Success
	}
	return nil
}

func (x *ExecutionsSystemLog) GetFailure() *ExecutionsSystemLog_Failure {
	if x, ok := x.GetDetails().(*ExecutionsSystemLog_Failure_); ok {
		return x.Failure
	}
	return nil
}

type isExecutionsSystemLog_Details interface {
	isExecutionsSystemLog_Details()
}

type ExecutionsSystemLog_Start_ struct {
	// Appears only in the log created when the execution has started.
	Start *ExecutionsSystemLog_Start `protobuf:"bytes,4,opt,name=start,proto3,oneof"`
}

type ExecutionsSystemLog_Success_ struct {
	// Appears only in the log created when the execution has finished
	// successfully.
	Success *ExecutionsSystemLog_Success `protobuf:"bytes,5,opt,name=success,proto3,oneof"`
}

type ExecutionsSystemLog_Failure_ struct {
	// Appears only in the log created when the execution has failed.
	Failure *ExecutionsSystemLog_Failure `protobuf:"bytes,6,opt,name=failure,proto3,oneof"`
}

func (*ExecutionsSystemLog_Start_) isExecutionsSystemLog_Details() {}

func (*ExecutionsSystemLog_Success_) isExecutionsSystemLog_Details() {}

func (*ExecutionsSystemLog_Failure_) isExecutionsSystemLog_Details() {}

// Detailed information about the start of the execution.
type ExecutionsSystemLog_Start struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The execution input argument.
	Argument string `protobuf:"bytes,2,opt,name=argument,proto3" json:"argument,omitempty"`
}

func (x *ExecutionsSystemLog_Start) Reset() {
	*x = ExecutionsSystemLog_Start{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_workflows_type_executions_system_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecutionsSystemLog_Start) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutionsSystemLog_Start) ProtoMessage() {}

func (x *ExecutionsSystemLog_Start) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_workflows_type_executions_system_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutionsSystemLog_Start.ProtoReflect.Descriptor instead.
func (*ExecutionsSystemLog_Start) Descriptor() ([]byte, []int) {
	return file_google_cloud_workflows_type_executions_system_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ExecutionsSystemLog_Start) GetArgument() string {
	if x != nil {
		return x.Argument
	}
	return ""
}

// Detailed information about the successful finish of the execution.
type ExecutionsSystemLog_Success struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The final result of the execution.
	Result string `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *ExecutionsSystemLog_Success) Reset() {
	*x = ExecutionsSystemLog_Success{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_workflows_type_executions_system_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecutionsSystemLog_Success) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutionsSystemLog_Success) ProtoMessage() {}

func (x *ExecutionsSystemLog_Success) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_workflows_type_executions_system_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutionsSystemLog_Success.ProtoReflect.Descriptor instead.
func (*ExecutionsSystemLog_Success) Descriptor() ([]byte, []int) {
	return file_google_cloud_workflows_type_executions_system_proto_rawDescGZIP(), []int{0, 1}
}

func (x *ExecutionsSystemLog_Success) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

// Detailed information about the execution failure.
type ExecutionsSystemLog_Failure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The exception message, e.g. "division by zero". The size limit is 1 kB.
	Exception string `protobuf:"bytes,1,opt,name=exception,proto3" json:"exception,omitempty"`
	// The code location of the statement that has created the log. For example,
	// a log created in subworkflow 'Foo' in step 'bar' will have its source
	// equal to 'Foo.bar'. The size limit is 1 kB.
	Source string `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
}

func (x *ExecutionsSystemLog_Failure) Reset() {
	*x = ExecutionsSystemLog_Failure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_workflows_type_executions_system_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecutionsSystemLog_Failure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutionsSystemLog_Failure) ProtoMessage() {}

func (x *ExecutionsSystemLog_Failure) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_workflows_type_executions_system_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutionsSystemLog_Failure.ProtoReflect.Descriptor instead.
func (*ExecutionsSystemLog_Failure) Descriptor() ([]byte, []int) {
	return file_google_cloud_workflows_type_executions_system_proto_rawDescGZIP(), []int{0, 2}
}

func (x *ExecutionsSystemLog_Failure) GetException() string {
	if x != nil {
		return x.Exception
	}
	return ""
}

func (x *ExecutionsSystemLog_Failure) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

var File_google_cloud_workflows_type_executions_system_proto protoreflect.FileDescriptor

var file_google_cloud_workflows_type_executions_system_proto_rawDesc = []byte{
	0x0a, 0x33, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xa4, 0x05, 0x0a, 0x13, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4c, 0x6f, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3f, 0x0a, 0x0d, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4c, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x4c, 0x6f, 0x67, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x4e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x4c, 0x6f, 0x67, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x48, 0x00, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x12, 0x54, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x4c, 0x6f, 0x67, 0x2e, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x48,
	0x00, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x54, 0x0a, 0x07, 0x66, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4c, 0x6f, 0x67, 0x2e, 0x46, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x48, 0x00, 0x52, 0x07, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x1a, 0x23, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x72, 0x67,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x72, 0x67,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x21, 0x0a, 0x07, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x1a, 0x3f, 0x0a, 0x07, 0x46, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x54, 0x0a, 0x05, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54,
	0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x55, 0x43, 0x43, 0x45, 0x45, 0x44,
	0x45, 0x44, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03,
	0x12, 0x0d, 0x0a, 0x09, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x42,
	0x09, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x3c, 0x5a, 0x3a, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_cloud_workflows_type_executions_system_proto_rawDescOnce sync.Once
	file_google_cloud_workflows_type_executions_system_proto_rawDescData = file_google_cloud_workflows_type_executions_system_proto_rawDesc
)

func file_google_cloud_workflows_type_executions_system_proto_rawDescGZIP() []byte {
	file_google_cloud_workflows_type_executions_system_proto_rawDescOnce.Do(func() {
		file_google_cloud_workflows_type_executions_system_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_cloud_workflows_type_executions_system_proto_rawDescData)
	})
	return file_google_cloud_workflows_type_executions_system_proto_rawDescData
}

var file_google_cloud_workflows_type_executions_system_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_google_cloud_workflows_type_executions_system_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_google_cloud_workflows_type_executions_system_proto_goTypes = []interface{}{
	(ExecutionsSystemLog_State)(0),      // 0: google.cloud.workflows.type.ExecutionsSystemLog.State
	(*ExecutionsSystemLog)(nil),         // 1: google.cloud.workflows.type.ExecutionsSystemLog
	(*ExecutionsSystemLog_Start)(nil),   // 2: google.cloud.workflows.type.ExecutionsSystemLog.Start
	(*ExecutionsSystemLog_Success)(nil), // 3: google.cloud.workflows.type.ExecutionsSystemLog.Success
	(*ExecutionsSystemLog_Failure)(nil), // 4: google.cloud.workflows.type.ExecutionsSystemLog.Failure
	(*timestamppb.Timestamp)(nil),       // 5: google.protobuf.Timestamp
}
var file_google_cloud_workflows_type_executions_system_proto_depIdxs = []int32{
	5, // 0: google.cloud.workflows.type.ExecutionsSystemLog.activity_time:type_name -> google.protobuf.Timestamp
	0, // 1: google.cloud.workflows.type.ExecutionsSystemLog.state:type_name -> google.cloud.workflows.type.ExecutionsSystemLog.State
	2, // 2: google.cloud.workflows.type.ExecutionsSystemLog.start:type_name -> google.cloud.workflows.type.ExecutionsSystemLog.Start
	3, // 3: google.cloud.workflows.type.ExecutionsSystemLog.success:type_name -> google.cloud.workflows.type.ExecutionsSystemLog.Success
	4, // 4: google.cloud.workflows.type.ExecutionsSystemLog.failure:type_name -> google.cloud.workflows.type.ExecutionsSystemLog.Failure
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_google_cloud_workflows_type_executions_system_proto_init() }
func file_google_cloud_workflows_type_executions_system_proto_init() {
	if File_google_cloud_workflows_type_executions_system_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_cloud_workflows_type_executions_system_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecutionsSystemLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_workflows_type_executions_system_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecutionsSystemLog_Start); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_workflows_type_executions_system_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecutionsSystemLog_Success); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_workflows_type_executions_system_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecutionsSystemLog_Failure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_cloud_workflows_type_executions_system_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*ExecutionsSystemLog_Start_)(nil),
		(*ExecutionsSystemLog_Success_)(nil),
		(*ExecutionsSystemLog_Failure_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_cloud_workflows_type_executions_system_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_cloud_workflows_type_executions_system_proto_goTypes,
		DependencyIndexes: file_google_cloud_workflows_type_executions_system_proto_depIdxs,
		EnumInfos:         file_google_cloud_workflows_type_executions_system_proto_enumTypes,
		MessageInfos:      file_google_cloud_workflows_type_executions_system_proto_msgTypes,
	}.Build()
	File_google_cloud_workflows_type_executions_system_proto = out.File
	file_google_cloud_workflows_type_executions_system_proto_rawDesc = nil
	file_google_cloud_workflows_type_executions_system_proto_goTypes = nil
	file_google_cloud_workflows_type_executions_system_proto_depIdxs = nil
}
