// Copyright 2017 Google Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.12.2
// source: google/container/v1alpha1/cluster_service.proto

package container

import (
	context "context"
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Possible values for Effect in taint.
type NodeTaint_Effect int32

const (
	// Not set
	NodeTaint_EFFECT_UNSPECIFIED NodeTaint_Effect = 0
	// NoSchedule
	NodeTaint_NO_SCHEDULE NodeTaint_Effect = 1
	// PreferNoSchedule
	NodeTaint_PREFER_NO_SCHEDULE NodeTaint_Effect = 2
	// NoExecute
	NodeTaint_NO_EXECUTE NodeTaint_Effect = 3
)

// Enum value maps for NodeTaint_Effect.
var (
	NodeTaint_Effect_name = map[int32]string{
		0: "EFFECT_UNSPECIFIED",
		1: "NO_SCHEDULE",
		2: "PREFER_NO_SCHEDULE",
		3: "NO_EXECUTE",
	}
	NodeTaint_Effect_value = map[string]int32{
		"EFFECT_UNSPECIFIED": 0,
		"NO_SCHEDULE":        1,
		"PREFER_NO_SCHEDULE": 2,
		"NO_EXECUTE":         3,
	}
)

func (x NodeTaint_Effect) Enum() *NodeTaint_Effect {
	p := new(NodeTaint_Effect)
	*p = x
	return p
}

func (x NodeTaint_Effect) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NodeTaint_Effect) Descriptor() protoreflect.EnumDescriptor {
	return file_google_container_v1alpha1_cluster_service_proto_enumTypes[0].Descriptor()
}

func (NodeTaint_Effect) Type() protoreflect.EnumType {
	return &file_google_container_v1alpha1_cluster_service_proto_enumTypes[0]
}

func (x NodeTaint_Effect) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NodeTaint_Effect.Descriptor instead.
func (NodeTaint_Effect) EnumDescriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{1, 0}
}

// Allowed Network Policy providers.
type NetworkPolicy_Provider int32

const (
	// Not set
	NetworkPolicy_PROVIDER_UNSPECIFIED NetworkPolicy_Provider = 0
	// Tigera (Calico Felix).
	NetworkPolicy_CALICO NetworkPolicy_Provider = 1
)

// Enum value maps for NetworkPolicy_Provider.
var (
	NetworkPolicy_Provider_name = map[int32]string{
		0: "PROVIDER_UNSPECIFIED",
		1: "CALICO",
	}
	NetworkPolicy_Provider_value = map[string]int32{
		"PROVIDER_UNSPECIFIED": 0,
		"CALICO":               1,
	}
)

func (x NetworkPolicy_Provider) Enum() *NetworkPolicy_Provider {
	p := new(NetworkPolicy_Provider)
	*p = x
	return p
}

func (x NetworkPolicy_Provider) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NetworkPolicy_Provider) Descriptor() protoreflect.EnumDescriptor {
	return file_google_container_v1alpha1_cluster_service_proto_enumTypes[1].Descriptor()
}

func (NetworkPolicy_Provider) Type() protoreflect.EnumType {
	return &file_google_container_v1alpha1_cluster_service_proto_enumTypes[1]
}

func (x NetworkPolicy_Provider) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NetworkPolicy_Provider.Descriptor instead.
func (NetworkPolicy_Provider) EnumDescriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{10, 0}
}

// The current status of the cluster.
type Cluster_Status int32

const (
	// Not set.
	Cluster_STATUS_UNSPECIFIED Cluster_Status = 0
	// The PROVISIONING state indicates the cluster is being created.
	Cluster_PROVISIONING Cluster_Status = 1
	// The RUNNING state indicates the cluster has been created and is fully
	// usable.
	Cluster_RUNNING Cluster_Status = 2
	// The RECONCILING state indicates that some work is actively being done on
	// the cluster, such as upgrading the master or node software. Details can
	// be found in the `statusMessage` field.
	Cluster_RECONCILING Cluster_Status = 3
	// The STOPPING state indicates the cluster is being deleted.
	Cluster_STOPPING Cluster_Status = 4
	// The ERROR state indicates the cluster may be unusable. Details
	// can be found in the `statusMessage` field.
	Cluster_ERROR Cluster_Status = 5
)

// Enum value maps for Cluster_Status.
var (
	Cluster_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "PROVISIONING",
		2: "RUNNING",
		3: "RECONCILING",
		4: "STOPPING",
		5: "ERROR",
	}
	Cluster_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"PROVISIONING":       1,
		"RUNNING":            2,
		"RECONCILING":        3,
		"STOPPING":           4,
		"ERROR":              5,
	}
)

func (x Cluster_Status) Enum() *Cluster_Status {
	p := new(Cluster_Status)
	*p = x
	return p
}

func (x Cluster_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Cluster_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_google_container_v1alpha1_cluster_service_proto_enumTypes[2].Descriptor()
}

func (Cluster_Status) Type() protoreflect.EnumType {
	return &file_google_container_v1alpha1_cluster_service_proto_enumTypes[2]
}

func (x Cluster_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Cluster_Status.Descriptor instead.
func (Cluster_Status) EnumDescriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{13, 0}
}

// Current status of the operation.
type Operation_Status int32

const (
	// Not set.
	Operation_STATUS_UNSPECIFIED Operation_Status = 0
	// The operation has been created.
	Operation_PENDING Operation_Status = 1
	// The operation is currently running.
	Operation_RUNNING Operation_Status = 2
	// The operation is done, either cancelled or completed.
	Operation_DONE Operation_Status = 3
	// The operation is aborting.
	Operation_ABORTING Operation_Status = 4
)

// Enum value maps for Operation_Status.
var (
	Operation_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "PENDING",
		2: "RUNNING",
		3: "DONE",
		4: "ABORTING",
	}
	Operation_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"PENDING":            1,
		"RUNNING":            2,
		"DONE":               3,
		"ABORTING":           4,
	}
)

func (x Operation_Status) Enum() *Operation_Status {
	p := new(Operation_Status)
	*p = x
	return p
}

func (x Operation_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Operation_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_google_container_v1alpha1_cluster_service_proto_enumTypes[3].Descriptor()
}

func (Operation_Status) Type() protoreflect.EnumType {
	return &file_google_container_v1alpha1_cluster_service_proto_enumTypes[3]
}

func (x Operation_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Operation_Status.Descriptor instead.
func (Operation_Status) EnumDescriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{15, 0}
}

// Operation type.
type Operation_Type int32

const (
	// Not set.
	Operation_TYPE_UNSPECIFIED Operation_Type = 0
	// Cluster create.
	Operation_CREATE_CLUSTER Operation_Type = 1
	// Cluster delete.
	Operation_DELETE_CLUSTER Operation_Type = 2
	// A master upgrade.
	Operation_UPGRADE_MASTER Operation_Type = 3
	// A node upgrade.
	Operation_UPGRADE_NODES Operation_Type = 4
	// Cluster repair.
	Operation_REPAIR_CLUSTER Operation_Type = 5
	// Cluster update.
	Operation_UPDATE_CLUSTER Operation_Type = 6
	// Node pool create.
	Operation_CREATE_NODE_POOL Operation_Type = 7
	// Node pool delete.
	Operation_DELETE_NODE_POOL Operation_Type = 8
	// Set node pool management.
	Operation_SET_NODE_POOL_MANAGEMENT Operation_Type = 9
	// Automatic node pool repair.
	Operation_AUTO_REPAIR_NODES Operation_Type = 10
	// Automatic node upgrade.
	Operation_AUTO_UPGRADE_NODES Operation_Type = 11
	// Set labels.
	Operation_SET_LABELS Operation_Type = 12
	// Set/generate master auth materials
	Operation_SET_MASTER_AUTH Operation_Type = 13
	// Set node pool size.
	Operation_SET_NODE_POOL_SIZE Operation_Type = 14
	// Updates network policy for a cluster.
	Operation_SET_NETWORK_POLICY Operation_Type = 15
	// Set the maintenance policy.
	Operation_SET_MAINTENANCE_POLICY Operation_Type = 16
)

// Enum value maps for Operation_Type.
var (
	Operation_Type_name = map[int32]string{
		0:  "TYPE_UNSPECIFIED",
		1:  "CREATE_CLUSTER",
		2:  "DELETE_CLUSTER",
		3:  "UPGRADE_MASTER",
		4:  "UPGRADE_NODES",
		5:  "REPAIR_CLUSTER",
		6:  "UPDATE_CLUSTER",
		7:  "CREATE_NODE_POOL",
		8:  "DELETE_NODE_POOL",
		9:  "SET_NODE_POOL_MANAGEMENT",
		10: "AUTO_REPAIR_NODES",
		11: "AUTO_UPGRADE_NODES",
		12: "SET_LABELS",
		13: "SET_MASTER_AUTH",
		14: "SET_NODE_POOL_SIZE",
		15: "SET_NETWORK_POLICY",
		16: "SET_MAINTENANCE_POLICY",
	}
	Operation_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED":         0,
		"CREATE_CLUSTER":           1,
		"DELETE_CLUSTER":           2,
		"UPGRADE_MASTER":           3,
		"UPGRADE_NODES":            4,
		"REPAIR_CLUSTER":           5,
		"UPDATE_CLUSTER":           6,
		"CREATE_NODE_POOL":         7,
		"DELETE_NODE_POOL":         8,
		"SET_NODE_POOL_MANAGEMENT": 9,
		"AUTO_REPAIR_NODES":        10,
		"AUTO_UPGRADE_NODES":       11,
		"SET_LABELS":               12,
		"SET_MASTER_AUTH":          13,
		"SET_NODE_POOL_SIZE":       14,
		"SET_NETWORK_POLICY":       15,
		"SET_MAINTENANCE_POLICY":   16,
	}
)

func (x Operation_Type) Enum() *Operation_Type {
	p := new(Operation_Type)
	*p = x
	return p
}

func (x Operation_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Operation_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_google_container_v1alpha1_cluster_service_proto_enumTypes[4].Descriptor()
}

func (Operation_Type) Type() protoreflect.EnumType {
	return &file_google_container_v1alpha1_cluster_service_proto_enumTypes[4]
}

func (x Operation_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Operation_Type.Descriptor instead.
func (Operation_Type) EnumDescriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{15, 1}
}

// Operation type: what type update to perform.
type SetMasterAuthRequest_Action int32

const (
	// Operation is unknown and will error out.
	SetMasterAuthRequest_UNKNOWN SetMasterAuthRequest_Action = 0
	// Set the password to a user generated value.
	SetMasterAuthRequest_SET_PASSWORD SetMasterAuthRequest_Action = 1
	// Generate a new password and set it to that.
	SetMasterAuthRequest_GENERATE_PASSWORD SetMasterAuthRequest_Action = 2
	// Set the username.  If an empty username is provided, basic authentication
	// is disabled for the cluster.  If a non-empty username is provided, basic
	// authentication is enabled, with either a provided password or a generated
	// one.
	SetMasterAuthRequest_SET_USERNAME SetMasterAuthRequest_Action = 3
)

// Enum value maps for SetMasterAuthRequest_Action.
var (
	SetMasterAuthRequest_Action_name = map[int32]string{
		0: "UNKNOWN",
		1: "SET_PASSWORD",
		2: "GENERATE_PASSWORD",
		3: "SET_USERNAME",
	}
	SetMasterAuthRequest_Action_value = map[string]int32{
		"UNKNOWN":           0,
		"SET_PASSWORD":      1,
		"GENERATE_PASSWORD": 2,
		"SET_USERNAME":      3,
	}
)

func (x SetMasterAuthRequest_Action) Enum() *SetMasterAuthRequest_Action {
	p := new(SetMasterAuthRequest_Action)
	*p = x
	return p
}

func (x SetMasterAuthRequest_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SetMasterAuthRequest_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_google_container_v1alpha1_cluster_service_proto_enumTypes[5].Descriptor()
}

func (SetMasterAuthRequest_Action) Type() protoreflect.EnumType {
	return &file_google_container_v1alpha1_cluster_service_proto_enumTypes[5]
}

func (x SetMasterAuthRequest_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SetMasterAuthRequest_Action.Descriptor instead.
func (SetMasterAuthRequest_Action) EnumDescriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{26, 0}
}

// The current status of the node pool instance.
type NodePool_Status int32

const (
	// Not set.
	NodePool_STATUS_UNSPECIFIED NodePool_Status = 0
	// The PROVISIONING state indicates the node pool is being created.
	NodePool_PROVISIONING NodePool_Status = 1
	// The RUNNING state indicates the node pool has been created
	// and is fully usable.
	NodePool_RUNNING NodePool_Status = 2
	// The RUNNING_WITH_ERROR state indicates the node pool has been created
	// and is partially usable. Some error state has occurred and some
	// functionality may be impaired. Customer may need to reissue a request
	// or trigger a new update.
	NodePool_RUNNING_WITH_ERROR NodePool_Status = 3
	// The RECONCILING state indicates that some work is actively being done on
	// the node pool, such as upgrading node software. Details can
	// be found in the `statusMessage` field.
	NodePool_RECONCILING NodePool_Status = 4
	// The STOPPING state indicates the node pool is being deleted.
	NodePool_STOPPING NodePool_Status = 5
	// The ERROR state indicates the node pool may be unusable. Details
	// can be found in the `statusMessage` field.
	NodePool_ERROR NodePool_Status = 6
)

// Enum value maps for NodePool_Status.
var (
	NodePool_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "PROVISIONING",
		2: "RUNNING",
		3: "RUNNING_WITH_ERROR",
		4: "RECONCILING",
		5: "STOPPING",
		6: "ERROR",
	}
	NodePool_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"PROVISIONING":       1,
		"RUNNING":            2,
		"RUNNING_WITH_ERROR": 3,
		"RECONCILING":        4,
		"STOPPING":           5,
		"ERROR":              6,
	}
)

func (x NodePool_Status) Enum() *NodePool_Status {
	p := new(NodePool_Status)
	*p = x
	return p
}

func (x NodePool_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NodePool_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_google_container_v1alpha1_cluster_service_proto_enumTypes[6].Descriptor()
}

func (NodePool_Status) Type() protoreflect.EnumType {
	return &file_google_container_v1alpha1_cluster_service_proto_enumTypes[6]
}

func (x NodePool_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NodePool_Status.Descriptor instead.
func (NodePool_Status) EnumDescriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{40, 0}
}

// Parameters that describe the nodes in a cluster.
type NodeConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of a Google Compute Engine [machine
	// type](/compute/docs/machine-types) (e.g.
	// `n1-standard-1`).
	//
	// If unspecified, the default machine type is
	// `n1-standard-1`.
	MachineType string `protobuf:"bytes,1,opt,name=machine_type,json=machineType,proto3" json:"machine_type,omitempty"`
	// Size of the disk attached to each node, specified in GB.
	// The smallest allowed disk size is 10GB.
	//
	// If unspecified, the default disk size is 100GB.
	DiskSizeGb int32 `protobuf:"varint,2,opt,name=disk_size_gb,json=diskSizeGb,proto3" json:"disk_size_gb,omitempty"`
	// The set of Google API scopes to be made available on all of the
	// node VMs under the "default" service account.
	//
	// The following scopes are recommended, but not required, and by default are
	// not included:
	//
	// * `https://www.googleapis.com/auth/compute` is required for mounting
	// persistent storage on your nodes.
	// * `https://www.googleapis.com/auth/devstorage.read_only` is required for
	// communicating with **gcr.io**
	// (the [Google Container Registry](/container-registry/)).
	//
	// If unspecified, no scopes are added, unless Cloud Logging or Cloud
	// Monitoring are enabled, in which case their required scopes will be added.
	OauthScopes []string `protobuf:"bytes,3,rep,name=oauth_scopes,json=oauthScopes,proto3" json:"oauth_scopes,omitempty"`
	// The Google Cloud Platform Service Account to be used by the node VMs. If
	// no Service Account is specified, the "default" service account is used.
	ServiceAccount string `protobuf:"bytes,9,opt,name=service_account,json=serviceAccount,proto3" json:"service_account,omitempty"`
	// The metadata key/value pairs assigned to instances in the cluster.
	//
	// Keys must conform to the regexp [a-zA-Z0-9-_]+ and be less than 128 bytes
	// in length. These are reflected as part of a URL in the metadata server.
	// Additionally, to avoid ambiguity, keys must not conflict with any other
	// metadata keys for the project or be one of the four reserved keys:
	// "instance-template", "kube-env", "startup-script", and "user-data"
	//
	// Values are free-form strings, and only have meaning as interpreted by
	// the image running in the instance. The only restriction placed on them is
	// that each value's size must be less than or equal to 32 KB.
	//
	// The total size of all keys and values must be less than 512 KB.
	Metadata map[string]string `protobuf:"bytes,4,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// The image type to use for this node. Note that for a given image type,
	// the latest version of it will be used.
	ImageType string `protobuf:"bytes,5,opt,name=image_type,json=imageType,proto3" json:"image_type,omitempty"`
	// The map of Kubernetes labels (key/value pairs) to be applied to each node.
	// These will added in addition to any default label(s) that
	// Kubernetes may apply to the node.
	// In case of conflict in label keys, the applied set may differ depending on
	// the Kubernetes version -- it's best to assume the behavior is undefined
	// and conflicts should be avoided.
	// For more information, including usage and the valid values, see:
	// https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
	Labels map[string]string `protobuf:"bytes,6,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// The number of local SSD disks to be attached to the node.
	//
	// The limit for this value is dependant upon the maximum number of
	// disks available on a machine per zone. See:
	// https://cloud.google.com/compute/docs/disks/local-ssd#local_ssd_limits
	// for more information.
	LocalSsdCount int32 `protobuf:"varint,7,opt,name=local_ssd_count,json=localSsdCount,proto3" json:"local_ssd_count,omitempty"`
	// The list of instance tags applied to all nodes. Tags are used to identify
	// valid sources or targets for network firewalls and are specified by
	// the client during cluster or node pool creation. Each tag within the list
	// must comply with RFC1035.
	Tags []string `protobuf:"bytes,8,rep,name=tags,proto3" json:"tags,omitempty"`
	// Whether the nodes are created as preemptible VM instances. See:
	// https://cloud.google.com/compute/docs/instances/preemptible for more
	// inforamtion about preemptible VM instances.
	Preemptible bool `protobuf:"varint,10,opt,name=preemptible,proto3" json:"preemptible,omitempty"`
	// A list of hardware accelerators to be attached to each node.
	// See https://cloud.google.com/compute/docs/gpus for more information about
	// support for GPUs.
	Accelerators []*AcceleratorConfig `protobuf:"bytes,11,rep,name=accelerators,proto3" json:"accelerators,omitempty"`
	// Minimum CPU platform to be used by this instance. The instance may be
	// scheduled on the specified or newer CPU platform. Applicable values are the
	// friendly names of CPU platforms, such as
	// <code>minCpuPlatform: &quot;Intel Haswell&quot;</code> or
	// <code>minCpuPlatform: &quot;Intel Sandy Bridge&quot;</code>. For more
	// information, read [how to specify min CPU platform](https://cloud.google.com/compute/docs/instances/specify-min-cpu-platform)
	MinCpuPlatform string `protobuf:"bytes,13,opt,name=min_cpu_platform,json=minCpuPlatform,proto3" json:"min_cpu_platform,omitempty"`
	// List of kubernetes taints to be applied to each node.
	//
	// For more information, including usage and the valid values, see:
	// https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
	Taints []*NodeTaint `protobuf:"bytes,15,rep,name=taints,proto3" json:"taints,omitempty"`
}

func (x *NodeConfig) Reset() {
	*x = NodeConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeConfig) ProtoMessage() {}

func (x *NodeConfig) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeConfig.ProtoReflect.Descriptor instead.
func (*NodeConfig) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{0}
}

func (x *NodeConfig) GetMachineType() string {
	if x != nil {
		return x.MachineType
	}
	return ""
}

func (x *NodeConfig) GetDiskSizeGb() int32 {
	if x != nil {
		return x.DiskSizeGb
	}
	return 0
}

func (x *NodeConfig) GetOauthScopes() []string {
	if x != nil {
		return x.OauthScopes
	}
	return nil
}

func (x *NodeConfig) GetServiceAccount() string {
	if x != nil {
		return x.ServiceAccount
	}
	return ""
}

func (x *NodeConfig) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *NodeConfig) GetImageType() string {
	if x != nil {
		return x.ImageType
	}
	return ""
}

func (x *NodeConfig) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *NodeConfig) GetLocalSsdCount() int32 {
	if x != nil {
		return x.LocalSsdCount
	}
	return 0
}

func (x *NodeConfig) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *NodeConfig) GetPreemptible() bool {
	if x != nil {
		return x.Preemptible
	}
	return false
}

func (x *NodeConfig) GetAccelerators() []*AcceleratorConfig {
	if x != nil {
		return x.Accelerators
	}
	return nil
}

func (x *NodeConfig) GetMinCpuPlatform() string {
	if x != nil {
		return x.MinCpuPlatform
	}
	return ""
}

func (x *NodeConfig) GetTaints() []*NodeTaint {
	if x != nil {
		return x.Taints
	}
	return nil
}

// Kubernetes taint is comprised of three fields: key, value, and effect. Effect
// can only be one of three types:  NoSchedule, PreferNoSchedule or NoExecute.
//
// For more information, including usage and the valid values, see:
// https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
type NodeTaint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Key for taint.
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// Value for taint.
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	// Effect for taint.
	Effect NodeTaint_Effect `protobuf:"varint,3,opt,name=effect,proto3,enum=google.container.v1alpha1.NodeTaint_Effect" json:"effect,omitempty"`
}

func (x *NodeTaint) Reset() {
	*x = NodeTaint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeTaint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeTaint) ProtoMessage() {}

func (x *NodeTaint) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeTaint.ProtoReflect.Descriptor instead.
func (*NodeTaint) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{1}
}

func (x *NodeTaint) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *NodeTaint) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *NodeTaint) GetEffect() NodeTaint_Effect {
	if x != nil {
		return x.Effect
	}
	return NodeTaint_EFFECT_UNSPECIFIED
}

// The authentication information for accessing the master endpoint.
// Authentication can be done using HTTP basic auth or using client
// certificates.
type MasterAuth struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The username to use for HTTP basic authentication to the master endpoint.
	// For clusters v1.6.0 and later, you can disable basic authentication by
	// providing an empty username.
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	// The password to use for HTTP basic authentication to the master endpoint.
	// Because the master endpoint is open to the Internet, you should create a
	// strong password.  If a password is provided for cluster creation, username
	// must be non-empty.
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	// Configuration for client certificate authentication on the cluster.  If no
	// configuration is specified, a client certificate is issued.
	ClientCertificateConfig *ClientCertificateConfig `protobuf:"bytes,3,opt,name=client_certificate_config,json=clientCertificateConfig,proto3" json:"client_certificate_config,omitempty"`
	// [Output only] Base64-encoded public certificate that is the root of
	// trust for the cluster.
	ClusterCaCertificate string `protobuf:"bytes,100,opt,name=cluster_ca_certificate,json=clusterCaCertificate,proto3" json:"cluster_ca_certificate,omitempty"`
	// [Output only] Base64-encoded public certificate used by clients to
	// authenticate to the cluster endpoint.
	ClientCertificate string `protobuf:"bytes,101,opt,name=client_certificate,json=clientCertificate,proto3" json:"client_certificate,omitempty"`
	// [Output only] Base64-encoded private key used by clients to authenticate
	// to the cluster endpoint.
	ClientKey string `protobuf:"bytes,102,opt,name=client_key,json=clientKey,proto3" json:"client_key,omitempty"`
}

func (x *MasterAuth) Reset() {
	*x = MasterAuth{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MasterAuth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MasterAuth) ProtoMessage() {}

func (x *MasterAuth) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MasterAuth.ProtoReflect.Descriptor instead.
func (*MasterAuth) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{2}
}

func (x *MasterAuth) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *MasterAuth) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *MasterAuth) GetClientCertificateConfig() *ClientCertificateConfig {
	if x != nil {
		return x.ClientCertificateConfig
	}
	return nil
}

func (x *MasterAuth) GetClusterCaCertificate() string {
	if x != nil {
		return x.ClusterCaCertificate
	}
	return ""
}

func (x *MasterAuth) GetClientCertificate() string {
	if x != nil {
		return x.ClientCertificate
	}
	return ""
}

func (x *MasterAuth) GetClientKey() string {
	if x != nil {
		return x.ClientKey
	}
	return ""
}

// Configuration for client certificates on the cluster.
type ClientCertificateConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Issue a client certificate.
	IssueClientCertificate bool `protobuf:"varint,1,opt,name=issue_client_certificate,json=issueClientCertificate,proto3" json:"issue_client_certificate,omitempty"`
}

func (x *ClientCertificateConfig) Reset() {
	*x = ClientCertificateConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientCertificateConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientCertificateConfig) ProtoMessage() {}

func (x *ClientCertificateConfig) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientCertificateConfig.ProtoReflect.Descriptor instead.
func (*ClientCertificateConfig) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{3}
}

func (x *ClientCertificateConfig) GetIssueClientCertificate() bool {
	if x != nil {
		return x.IssueClientCertificate
	}
	return false
}

// Configuration for the addons that can be automatically spun up in the
// cluster, enabling additional functionality.
type AddonsConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Configuration for the HTTP (L7) load balancing controller addon, which
	// makes it easy to set up HTTP load balancers for services in a cluster.
	HttpLoadBalancing *HttpLoadBalancing `protobuf:"bytes,1,opt,name=http_load_balancing,json=httpLoadBalancing,proto3" json:"http_load_balancing,omitempty"`
	// Configuration for the horizontal pod autoscaling feature, which
	// increases or decreases the number of replica pods a replication controller
	// has based on the resource usage of the existing pods.
	HorizontalPodAutoscaling *HorizontalPodAutoscaling `protobuf:"bytes,2,opt,name=horizontal_pod_autoscaling,json=horizontalPodAutoscaling,proto3" json:"horizontal_pod_autoscaling,omitempty"`
	// Configuration for the Kubernetes Dashboard.
	KubernetesDashboard *KubernetesDashboard `protobuf:"bytes,3,opt,name=kubernetes_dashboard,json=kubernetesDashboard,proto3" json:"kubernetes_dashboard,omitempty"`
	// Configuration for NetworkPolicy. This only tracks whether the addon
	// is enabled or not on the Master, it does not track whether network policy
	// is enabled for the nodes.
	NetworkPolicyConfig *NetworkPolicyConfig `protobuf:"bytes,4,opt,name=network_policy_config,json=networkPolicyConfig,proto3" json:"network_policy_config,omitempty"`
}

func (x *AddonsConfig) Reset() {
	*x = AddonsConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddonsConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddonsConfig) ProtoMessage() {}

func (x *AddonsConfig) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddonsConfig.ProtoReflect.Descriptor instead.
func (*AddonsConfig) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{4}
}

func (x *AddonsConfig) GetHttpLoadBalancing() *HttpLoadBalancing {
	if x != nil {
		return x.HttpLoadBalancing
	}
	return nil
}

func (x *AddonsConfig) GetHorizontalPodAutoscaling() *HorizontalPodAutoscaling {
	if x != nil {
		return x.HorizontalPodAutoscaling
	}
	return nil
}

func (x *AddonsConfig) GetKubernetesDashboard() *KubernetesDashboard {
	if x != nil {
		return x.KubernetesDashboard
	}
	return nil
}

func (x *AddonsConfig) GetNetworkPolicyConfig() *NetworkPolicyConfig {
	if x != nil {
		return x.NetworkPolicyConfig
	}
	return nil
}

// Configuration options for the HTTP (L7) load balancing controller addon,
// which makes it easy to set up HTTP load balancers for services in a cluster.
type HttpLoadBalancing struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether the HTTP Load Balancing controller is enabled in the cluster.
	// When enabled, it runs a small pod in the cluster that manages the load
	// balancers.
	Disabled bool `protobuf:"varint,1,opt,name=disabled,proto3" json:"disabled,omitempty"`
}

func (x *HttpLoadBalancing) Reset() {
	*x = HttpLoadBalancing{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HttpLoadBalancing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpLoadBalancing) ProtoMessage() {}

func (x *HttpLoadBalancing) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpLoadBalancing.ProtoReflect.Descriptor instead.
func (*HttpLoadBalancing) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{5}
}

func (x *HttpLoadBalancing) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}
	return false
}

// Configuration options for the horizontal pod autoscaling feature, which
// increases or decreases the number of replica pods a replication controller
// has based on the resource usage of the existing pods.
type HorizontalPodAutoscaling struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether the Horizontal Pod Autoscaling feature is enabled in the cluster.
	// When enabled, it ensures that a Heapster pod is running in the cluster,
	// which is also used by the Cloud Monitoring service.
	Disabled bool `protobuf:"varint,1,opt,name=disabled,proto3" json:"disabled,omitempty"`
}

func (x *HorizontalPodAutoscaling) Reset() {
	*x = HorizontalPodAutoscaling{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HorizontalPodAutoscaling) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HorizontalPodAutoscaling) ProtoMessage() {}

func (x *HorizontalPodAutoscaling) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HorizontalPodAutoscaling.ProtoReflect.Descriptor instead.
func (*HorizontalPodAutoscaling) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{6}
}

func (x *HorizontalPodAutoscaling) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}
	return false
}

// Configuration for the Kubernetes Dashboard.
type KubernetesDashboard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether the Kubernetes Dashboard is enabled for this cluster.
	Disabled bool `protobuf:"varint,1,opt,name=disabled,proto3" json:"disabled,omitempty"`
}

func (x *KubernetesDashboard) Reset() {
	*x = KubernetesDashboard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KubernetesDashboard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KubernetesDashboard) ProtoMessage() {}

func (x *KubernetesDashboard) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KubernetesDashboard.ProtoReflect.Descriptor instead.
func (*KubernetesDashboard) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{7}
}

func (x *KubernetesDashboard) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}
	return false
}

// Configuration for NetworkPolicy. This only tracks whether the addon
// is enabled or not on the Master, it does not track whether network policy
// is enabled for the nodes.
type NetworkPolicyConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether NetworkPolicy is enabled for this cluster.
	Disabled bool `protobuf:"varint,1,opt,name=disabled,proto3" json:"disabled,omitempty"`
}

func (x *NetworkPolicyConfig) Reset() {
	*x = NetworkPolicyConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworkPolicyConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkPolicyConfig) ProtoMessage() {}

func (x *NetworkPolicyConfig) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkPolicyConfig.ProtoReflect.Descriptor instead.
func (*NetworkPolicyConfig) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{8}
}

func (x *NetworkPolicyConfig) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}
	return false
}

// Configuration options for the master authorized networks feature. Enabled
// master authorized networks will disallow all external traffic to access
// Kubernetes master through HTTPS except traffic from the given CIDR blocks,
// Google Compute Engine Public IPs and Google Prod IPs.
type MasterAuthorizedNetworksConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether or not master authorized networks is enabled.
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// cidr_blocks define up to 10 external networks that could access
	// Kubernetes master through HTTPS.
	CidrBlocks []*MasterAuthorizedNetworksConfig_CidrBlock `protobuf:"bytes,2,rep,name=cidr_blocks,json=cidrBlocks,proto3" json:"cidr_blocks,omitempty"`
}

func (x *MasterAuthorizedNetworksConfig) Reset() {
	*x = MasterAuthorizedNetworksConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MasterAuthorizedNetworksConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MasterAuthorizedNetworksConfig) ProtoMessage() {}

func (x *MasterAuthorizedNetworksConfig) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MasterAuthorizedNetworksConfig.ProtoReflect.Descriptor instead.
func (*MasterAuthorizedNetworksConfig) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{9}
}

func (x *MasterAuthorizedNetworksConfig) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *MasterAuthorizedNetworksConfig) GetCidrBlocks() []*MasterAuthorizedNetworksConfig_CidrBlock {
	if x != nil {
		return x.CidrBlocks
	}
	return nil
}

// Configuration options for the NetworkPolicy feature.
// https://kubernetes.io/docs/concepts/services-networking/networkpolicies/
type NetworkPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The selected network policy provider.
	Provider NetworkPolicy_Provider `protobuf:"varint,1,opt,name=provider,proto3,enum=google.container.v1alpha1.NetworkPolicy_Provider" json:"provider,omitempty"`
	// Whether network policy is enabled on the cluster.
	Enabled bool `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *NetworkPolicy) Reset() {
	*x = NetworkPolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworkPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkPolicy) ProtoMessage() {}

func (x *NetworkPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkPolicy.ProtoReflect.Descriptor instead.
func (*NetworkPolicy) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{10}
}

func (x *NetworkPolicy) GetProvider() NetworkPolicy_Provider {
	if x != nil {
		return x.Provider
	}
	return NetworkPolicy_PROVIDER_UNSPECIFIED
}

func (x *NetworkPolicy) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

// Configuration for controlling how IPs are allocated in the cluster.
type IPAllocationPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether alias IPs will be used for pod IPs in the cluster.
	UseIpAliases bool `protobuf:"varint,1,opt,name=use_ip_aliases,json=useIpAliases,proto3" json:"use_ip_aliases,omitempty"`
	// Whether a new subnetwork will be created automatically for the cluster.
	//
	// This field is only applicable when `use_ip_aliases` is true.
	CreateSubnetwork bool `protobuf:"varint,2,opt,name=create_subnetwork,json=createSubnetwork,proto3" json:"create_subnetwork,omitempty"`
	// A custom subnetwork name to be used if `create_subnetwork` is true.  If
	// this field is empty, then an automatic name will be chosen for the new
	// subnetwork.
	SubnetworkName string `protobuf:"bytes,3,opt,name=subnetwork_name,json=subnetworkName,proto3" json:"subnetwork_name,omitempty"`
	// This field is deprecated, use cluster_ipv4_cidr_block.
	ClusterIpv4Cidr string `protobuf:"bytes,4,opt,name=cluster_ipv4_cidr,json=clusterIpv4Cidr,proto3" json:"cluster_ipv4_cidr,omitempty"`
	// This field is deprecated, use node_ipv4_cidr_block.
	NodeIpv4Cidr string `protobuf:"bytes,5,opt,name=node_ipv4_cidr,json=nodeIpv4Cidr,proto3" json:"node_ipv4_cidr,omitempty"`
	// This field is deprecated, use services_ipv4_cidr_block.
	ServicesIpv4Cidr string `protobuf:"bytes,6,opt,name=services_ipv4_cidr,json=servicesIpv4Cidr,proto3" json:"services_ipv4_cidr,omitempty"`
	// The name of the secondary range to be used for the cluster CIDR
	// block.  The secondary range will be used for pod IP
	// addresses. This must be an existing secondary range associated
	// with the cluster subnetwork.
	//
	// This field is only applicable if use_ip_aliases is true and
	// create_subnetwork is false.
	ClusterSecondaryRangeName string `protobuf:"bytes,7,opt,name=cluster_secondary_range_name,json=clusterSecondaryRangeName,proto3" json:"cluster_secondary_range_name,omitempty"`
	// The name of the secondary range to be used as for the services
	// CIDR block.  The secondary range will be used for service
	// ClusterIPs. This must be an existing secondary range associated
	// with the cluster subnetwork.
	//
	// This field is only applicable with use_ip_aliases is true and
	// create_subnetwork is false.
	ServicesSecondaryRangeName string `protobuf:"bytes,8,opt,name=services_secondary_range_name,json=servicesSecondaryRangeName,proto3" json:"services_secondary_range_name,omitempty"`
	// The IP address range for the cluster pod IPs. If this field is set, then
	// `cluster.cluster_ipv4_cidr` must be left blank.
	//
	// This field is only applicable when `use_ip_aliases` is true.
	//
	// Set to blank to have a range chosen with the default size.
	//
	// Set to /netmask (e.g. `/14`) to have a range chosen with a specific
	// netmask.
	//
	// Set to a
	// [CIDR](http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
	// notation (e.g. `*********/14`) from the RFC-1918 private networks (e.g.
	// `10.0.0.0/8`, `**********/12`, `***********/16`) to pick a specific range
	// to use.
	ClusterIpv4CidrBlock string `protobuf:"bytes,9,opt,name=cluster_ipv4_cidr_block,json=clusterIpv4CidrBlock,proto3" json:"cluster_ipv4_cidr_block,omitempty"`
	// The IP address range of the instance IPs in this cluster.
	//
	// This is applicable only if `create_subnetwork` is true.
	//
	// Set to blank to have a range chosen with the default size.
	//
	// Set to /netmask (e.g. `/14`) to have a range chosen with a specific
	// netmask.
	//
	// Set to a
	// [CIDR](http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
	// notation (e.g. `*********/14`) from the RFC-1918 private networks (e.g.
	// `10.0.0.0/8`, `**********/12`, `***********/16`) to pick a specific range
	// to use.
	NodeIpv4CidrBlock string `protobuf:"bytes,10,opt,name=node_ipv4_cidr_block,json=nodeIpv4CidrBlock,proto3" json:"node_ipv4_cidr_block,omitempty"`
	// The IP address range of the services IPs in this cluster. If blank, a range
	// will be automatically chosen with the default size.
	//
	// This field is only applicable when `use_ip_aliases` is true.
	//
	// Set to blank to have a range chosen with the default size.
	//
	// Set to /netmask (e.g. `/14`) to have a range chosen with a specific
	// netmask.
	//
	// Set to a
	// [CIDR](http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
	// notation (e.g. `*********/14`) from the RFC-1918 private networks (e.g.
	// `10.0.0.0/8`, `**********/12`, `***********/16`) to pick a specific range
	// to use.
	ServicesIpv4CidrBlock string `protobuf:"bytes,11,opt,name=services_ipv4_cidr_block,json=servicesIpv4CidrBlock,proto3" json:"services_ipv4_cidr_block,omitempty"`
}

func (x *IPAllocationPolicy) Reset() {
	*x = IPAllocationPolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IPAllocationPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPAllocationPolicy) ProtoMessage() {}

func (x *IPAllocationPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPAllocationPolicy.ProtoReflect.Descriptor instead.
func (*IPAllocationPolicy) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{11}
}

func (x *IPAllocationPolicy) GetUseIpAliases() bool {
	if x != nil {
		return x.UseIpAliases
	}
	return false
}

func (x *IPAllocationPolicy) GetCreateSubnetwork() bool {
	if x != nil {
		return x.CreateSubnetwork
	}
	return false
}

func (x *IPAllocationPolicy) GetSubnetworkName() string {
	if x != nil {
		return x.SubnetworkName
	}
	return ""
}

func (x *IPAllocationPolicy) GetClusterIpv4Cidr() string {
	if x != nil {
		return x.ClusterIpv4Cidr
	}
	return ""
}

func (x *IPAllocationPolicy) GetNodeIpv4Cidr() string {
	if x != nil {
		return x.NodeIpv4Cidr
	}
	return ""
}

func (x *IPAllocationPolicy) GetServicesIpv4Cidr() string {
	if x != nil {
		return x.ServicesIpv4Cidr
	}
	return ""
}

func (x *IPAllocationPolicy) GetClusterSecondaryRangeName() string {
	if x != nil {
		return x.ClusterSecondaryRangeName
	}
	return ""
}

func (x *IPAllocationPolicy) GetServicesSecondaryRangeName() string {
	if x != nil {
		return x.ServicesSecondaryRangeName
	}
	return ""
}

func (x *IPAllocationPolicy) GetClusterIpv4CidrBlock() string {
	if x != nil {
		return x.ClusterIpv4CidrBlock
	}
	return ""
}

func (x *IPAllocationPolicy) GetNodeIpv4CidrBlock() string {
	if x != nil {
		return x.NodeIpv4CidrBlock
	}
	return ""
}

func (x *IPAllocationPolicy) GetServicesIpv4CidrBlock() string {
	if x != nil {
		return x.ServicesIpv4CidrBlock
	}
	return ""
}

// Configuration for the PodSecurityPolicy feature.
type PodSecurityPolicyConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Enable the PodSecurityPolicy controller for this cluster. If enabled, pods
	// must be valid under a PodSecurityPolicy to be created.
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *PodSecurityPolicyConfig) Reset() {
	*x = PodSecurityPolicyConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PodSecurityPolicyConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PodSecurityPolicyConfig) ProtoMessage() {}

func (x *PodSecurityPolicyConfig) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PodSecurityPolicyConfig.ProtoReflect.Descriptor instead.
func (*PodSecurityPolicyConfig) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{12}
}

func (x *PodSecurityPolicyConfig) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

// A Google Container Engine cluster.
type Cluster struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of this cluster. The name must be unique within this project
	// and zone, and can be up to 40 characters with the following restrictions:
	//
	// * Lowercase letters, numbers, and hyphens only.
	// * Must start with a letter.
	// * Must end with a number or a letter.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// An optional description of this cluster.
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// The number of nodes to create in this cluster. You must ensure that your
	// Compute Engine <a href="/compute/docs/resource-quotas">resource quota</a>
	// is sufficient for this number of instances. You must also have available
	// firewall and routes quota.
	// For requests, this field should only be used in lieu of a
	// "node_pool" object, since this configuration (along with the
	// "node_config") will be used to create a "NodePool" object with an
	// auto-generated name. Do not use this and a node_pool at the same time.
	InitialNodeCount int32 `protobuf:"varint,3,opt,name=initial_node_count,json=initialNodeCount,proto3" json:"initial_node_count,omitempty"`
	// Parameters used in creating the cluster's nodes.
	// See `nodeConfig` for the description of its properties.
	// For requests, this field should only be used in lieu of a
	// "node_pool" object, since this configuration (along with the
	// "initial_node_count") will be used to create a "NodePool" object with an
	// auto-generated name. Do not use this and a node_pool at the same time.
	// For responses, this field will be populated with the node configuration of
	// the first node pool.
	//
	// If unspecified, the defaults are used.
	NodeConfig *NodeConfig `protobuf:"bytes,4,opt,name=node_config,json=nodeConfig,proto3" json:"node_config,omitempty"`
	// The authentication information for accessing the master endpoint.
	MasterAuth *MasterAuth `protobuf:"bytes,5,opt,name=master_auth,json=masterAuth,proto3" json:"master_auth,omitempty"`
	// The logging service the cluster should use to write logs.
	// Currently available options:
	//
	// * `logging.googleapis.com` - the Google Cloud Logging service.
	// * `none` - no logs will be exported from the cluster.
	// * if left as an empty string,`logging.googleapis.com` will be used.
	LoggingService string `protobuf:"bytes,6,opt,name=logging_service,json=loggingService,proto3" json:"logging_service,omitempty"`
	// The monitoring service the cluster should use to write metrics.
	// Currently available options:
	//
	// * `monitoring.googleapis.com` - the Google Cloud Monitoring service.
	// * `none` - no metrics will be exported from the cluster.
	// * if left as an empty string, `monitoring.googleapis.com` will be used.
	MonitoringService string `protobuf:"bytes,7,opt,name=monitoring_service,json=monitoringService,proto3" json:"monitoring_service,omitempty"`
	// The name of the Google Compute Engine
	// [network](/compute/docs/networks-and-firewalls#networks) to which the
	// cluster is connected. If left unspecified, the `default` network
	// will be used.
	Network string `protobuf:"bytes,8,opt,name=network,proto3" json:"network,omitempty"`
	// The IP address range of the container pods in this cluster, in
	// [CIDR](http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
	// notation (e.g. `*********/14`). Leave blank to have
	// one automatically chosen or specify a `/14` block in `10.0.0.0/8`.
	ClusterIpv4Cidr string `protobuf:"bytes,9,opt,name=cluster_ipv4_cidr,json=clusterIpv4Cidr,proto3" json:"cluster_ipv4_cidr,omitempty"`
	// Configurations for the various addons available to run in the cluster.
	AddonsConfig *AddonsConfig `protobuf:"bytes,10,opt,name=addons_config,json=addonsConfig,proto3" json:"addons_config,omitempty"`
	// The name of the Google Compute Engine
	// [subnetwork](/compute/docs/subnetworks) to which the
	// cluster is connected.
	Subnetwork string `protobuf:"bytes,11,opt,name=subnetwork,proto3" json:"subnetwork,omitempty"`
	// The node pools associated with this cluster.
	// This field should not be set if "node_config" or "initial_node_count" are
	// specified.
	NodePools []*NodePool `protobuf:"bytes,12,rep,name=node_pools,json=nodePools,proto3" json:"node_pools,omitempty"`
	// The list of Google Compute Engine
	// [locations](/compute/docs/zones#available) in which the cluster's nodes
	// should be located.
	Locations []string `protobuf:"bytes,13,rep,name=locations,proto3" json:"locations,omitempty"`
	// Kubernetes alpha features are enabled on this cluster. This includes alpha
	// API groups (e.g. v1alpha1) and features that may not be production ready in
	// the kubernetes version of the master and nodes.
	// The cluster has no SLA for uptime and master/node upgrades are disabled.
	// Alpha enabled clusters are automatically deleted thirty days after
	// creation.
	EnableKubernetesAlpha bool `protobuf:"varint,14,opt,name=enable_kubernetes_alpha,json=enableKubernetesAlpha,proto3" json:"enable_kubernetes_alpha,omitempty"`
	// Configuration options for the NetworkPolicy feature.
	NetworkPolicy *NetworkPolicy `protobuf:"bytes,19,opt,name=network_policy,json=networkPolicy,proto3" json:"network_policy,omitempty"`
	// Configuration for cluster IP allocation.
	IpAllocationPolicy *IPAllocationPolicy `protobuf:"bytes,20,opt,name=ip_allocation_policy,json=ipAllocationPolicy,proto3" json:"ip_allocation_policy,omitempty"`
	// The configuration options for master authorized networks feature.
	MasterAuthorizedNetworksConfig *MasterAuthorizedNetworksConfig `protobuf:"bytes,22,opt,name=master_authorized_networks_config,json=masterAuthorizedNetworksConfig,proto3" json:"master_authorized_networks_config,omitempty"`
	// Configure the maintenance policy for this cluster.
	MaintenancePolicy *MaintenancePolicy `protobuf:"bytes,23,opt,name=maintenance_policy,json=maintenancePolicy,proto3" json:"maintenance_policy,omitempty"`
	// Configuration for the PodSecurityPolicy feature.
	PodSecurityPolicyConfig *PodSecurityPolicyConfig `protobuf:"bytes,25,opt,name=pod_security_policy_config,json=podSecurityPolicyConfig,proto3" json:"pod_security_policy_config,omitempty"`
	// [Output only] Server-defined URL for the resource.
	SelfLink string `protobuf:"bytes,100,opt,name=self_link,json=selfLink,proto3" json:"self_link,omitempty"`
	// [Output only] The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use location instead.
	Zone string `protobuf:"bytes,101,opt,name=zone,proto3" json:"zone,omitempty"`
	// [Output only] The IP address of this cluster's master endpoint.
	// The endpoint can be accessed from the internet at
	// `**********************************/`.
	//
	// See the `masterAuth` property of this resource for username and
	// password information.
	Endpoint string `protobuf:"bytes,102,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
	// The initial Kubernetes version for this cluster.  Valid versions are those
	// found in validMasterVersions returned by getServerConfig.  The version can
	// be upgraded over time; such upgrades are reflected in
	// currentMasterVersion and currentNodeVersion.
	InitialClusterVersion string `protobuf:"bytes,103,opt,name=initial_cluster_version,json=initialClusterVersion,proto3" json:"initial_cluster_version,omitempty"`
	// [Output only] The current software version of the master endpoint.
	CurrentMasterVersion string `protobuf:"bytes,104,opt,name=current_master_version,json=currentMasterVersion,proto3" json:"current_master_version,omitempty"`
	// [Output only] The current version of the node software components.
	// If they are currently at multiple versions because they're in the process
	// of being upgraded, this reflects the minimum version of all nodes.
	CurrentNodeVersion string `protobuf:"bytes,105,opt,name=current_node_version,json=currentNodeVersion,proto3" json:"current_node_version,omitempty"`
	// [Output only] The time the cluster was created, in
	// [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
	CreateTime string `protobuf:"bytes,106,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// [Output only] The current status of this cluster.
	Status Cluster_Status `protobuf:"varint,107,opt,name=status,proto3,enum=google.container.v1alpha1.Cluster_Status" json:"status,omitempty"`
	// [Output only] Additional information about the current status of this
	// cluster, if available.
	StatusMessage string `protobuf:"bytes,108,opt,name=status_message,json=statusMessage,proto3" json:"status_message,omitempty"`
	// [Output only] The size of the address space on each node for hosting
	// containers. This is provisioned from within the `container_ipv4_cidr`
	// range.
	NodeIpv4CidrSize int32 `protobuf:"varint,109,opt,name=node_ipv4_cidr_size,json=nodeIpv4CidrSize,proto3" json:"node_ipv4_cidr_size,omitempty"`
	// [Output only] The IP address range of the Kubernetes services in
	// this cluster, in
	// [CIDR](http://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing)
	// notation (e.g. `*******/29`). Service addresses are
	// typically put in the last `/16` from the container CIDR.
	ServicesIpv4Cidr string `protobuf:"bytes,110,opt,name=services_ipv4_cidr,json=servicesIpv4Cidr,proto3" json:"services_ipv4_cidr,omitempty"`
	// [Output only] The resource URLs of [instance
	// groups](/compute/docs/instance-groups/) associated with this
	// cluster.
	InstanceGroupUrls []string `protobuf:"bytes,111,rep,name=instance_group_urls,json=instanceGroupUrls,proto3" json:"instance_group_urls,omitempty"`
	// [Output only] The number of nodes currently in the cluster.
	CurrentNodeCount int32 `protobuf:"varint,112,opt,name=current_node_count,json=currentNodeCount,proto3" json:"current_node_count,omitempty"`
	// [Output only] The time the cluster will be automatically
	// deleted in [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
	ExpireTime string `protobuf:"bytes,113,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	// [Output only] The name of the Google Compute Engine
	// [zone](/compute/docs/regions-zones/regions-zones#available) or
	// [region](/compute/docs/regions-zones/regions-zones#available) in which
	// the cluster resides.
	Location string `protobuf:"bytes,114,opt,name=location,proto3" json:"location,omitempty"`
}

func (x *Cluster) Reset() {
	*x = Cluster{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Cluster) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cluster) ProtoMessage() {}

func (x *Cluster) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cluster.ProtoReflect.Descriptor instead.
func (*Cluster) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{13}
}

func (x *Cluster) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Cluster) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Cluster) GetInitialNodeCount() int32 {
	if x != nil {
		return x.InitialNodeCount
	}
	return 0
}

func (x *Cluster) GetNodeConfig() *NodeConfig {
	if x != nil {
		return x.NodeConfig
	}
	return nil
}

func (x *Cluster) GetMasterAuth() *MasterAuth {
	if x != nil {
		return x.MasterAuth
	}
	return nil
}

func (x *Cluster) GetLoggingService() string {
	if x != nil {
		return x.LoggingService
	}
	return ""
}

func (x *Cluster) GetMonitoringService() string {
	if x != nil {
		return x.MonitoringService
	}
	return ""
}

func (x *Cluster) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Cluster) GetClusterIpv4Cidr() string {
	if x != nil {
		return x.ClusterIpv4Cidr
	}
	return ""
}

func (x *Cluster) GetAddonsConfig() *AddonsConfig {
	if x != nil {
		return x.AddonsConfig
	}
	return nil
}

func (x *Cluster) GetSubnetwork() string {
	if x != nil {
		return x.Subnetwork
	}
	return ""
}

func (x *Cluster) GetNodePools() []*NodePool {
	if x != nil {
		return x.NodePools
	}
	return nil
}

func (x *Cluster) GetLocations() []string {
	if x != nil {
		return x.Locations
	}
	return nil
}

func (x *Cluster) GetEnableKubernetesAlpha() bool {
	if x != nil {
		return x.EnableKubernetesAlpha
	}
	return false
}

func (x *Cluster) GetNetworkPolicy() *NetworkPolicy {
	if x != nil {
		return x.NetworkPolicy
	}
	return nil
}

func (x *Cluster) GetIpAllocationPolicy() *IPAllocationPolicy {
	if x != nil {
		return x.IpAllocationPolicy
	}
	return nil
}

func (x *Cluster) GetMasterAuthorizedNetworksConfig() *MasterAuthorizedNetworksConfig {
	if x != nil {
		return x.MasterAuthorizedNetworksConfig
	}
	return nil
}

func (x *Cluster) GetMaintenancePolicy() *MaintenancePolicy {
	if x != nil {
		return x.MaintenancePolicy
	}
	return nil
}

func (x *Cluster) GetPodSecurityPolicyConfig() *PodSecurityPolicyConfig {
	if x != nil {
		return x.PodSecurityPolicyConfig
	}
	return nil
}

func (x *Cluster) GetSelfLink() string {
	if x != nil {
		return x.SelfLink
	}
	return ""
}

func (x *Cluster) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *Cluster) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

func (x *Cluster) GetInitialClusterVersion() string {
	if x != nil {
		return x.InitialClusterVersion
	}
	return ""
}

func (x *Cluster) GetCurrentMasterVersion() string {
	if x != nil {
		return x.CurrentMasterVersion
	}
	return ""
}

func (x *Cluster) GetCurrentNodeVersion() string {
	if x != nil {
		return x.CurrentNodeVersion
	}
	return ""
}

func (x *Cluster) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Cluster) GetStatus() Cluster_Status {
	if x != nil {
		return x.Status
	}
	return Cluster_STATUS_UNSPECIFIED
}

func (x *Cluster) GetStatusMessage() string {
	if x != nil {
		return x.StatusMessage
	}
	return ""
}

func (x *Cluster) GetNodeIpv4CidrSize() int32 {
	if x != nil {
		return x.NodeIpv4CidrSize
	}
	return 0
}

func (x *Cluster) GetServicesIpv4Cidr() string {
	if x != nil {
		return x.ServicesIpv4Cidr
	}
	return ""
}

func (x *Cluster) GetInstanceGroupUrls() []string {
	if x != nil {
		return x.InstanceGroupUrls
	}
	return nil
}

func (x *Cluster) GetCurrentNodeCount() int32 {
	if x != nil {
		return x.CurrentNodeCount
	}
	return 0
}

func (x *Cluster) GetExpireTime() string {
	if x != nil {
		return x.ExpireTime
	}
	return ""
}

func (x *Cluster) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

// ClusterUpdate describes an update to the cluster. Exactly one update can
// be applied to a cluster with each request, so at most one field can be
// provided.
type ClusterUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Kubernetes version to change the nodes to (typically an
	// upgrade). Use `-` to upgrade to the latest version supported by
	// the server.
	DesiredNodeVersion string `protobuf:"bytes,4,opt,name=desired_node_version,json=desiredNodeVersion,proto3" json:"desired_node_version,omitempty"`
	// The monitoring service the cluster should use to write metrics.
	// Currently available options:
	//
	// * "monitoring.googleapis.com" - the Google Cloud Monitoring service
	// * "none" - no metrics will be exported from the cluster
	DesiredMonitoringService string `protobuf:"bytes,5,opt,name=desired_monitoring_service,json=desiredMonitoringService,proto3" json:"desired_monitoring_service,omitempty"`
	// Configurations for the various addons available to run in the cluster.
	DesiredAddonsConfig *AddonsConfig `protobuf:"bytes,6,opt,name=desired_addons_config,json=desiredAddonsConfig,proto3" json:"desired_addons_config,omitempty"`
	// The node pool to be upgraded. This field is mandatory if
	// "desired_node_version", "desired_image_family" or
	// "desired_node_pool_autoscaling" is specified and there is more than one
	// node pool on the cluster.
	DesiredNodePoolId string `protobuf:"bytes,7,opt,name=desired_node_pool_id,json=desiredNodePoolId,proto3" json:"desired_node_pool_id,omitempty"`
	// The desired image type for the node pool.
	// NOTE: Set the "desired_node_pool" field as well.
	DesiredImageType string `protobuf:"bytes,8,opt,name=desired_image_type,json=desiredImageType,proto3" json:"desired_image_type,omitempty"`
	// Autoscaler configuration for the node pool specified in
	// desired_node_pool_id. If there is only one pool in the
	// cluster and desired_node_pool_id is not provided then
	// the change applies to that single node pool.
	DesiredNodePoolAutoscaling *NodePoolAutoscaling `protobuf:"bytes,9,opt,name=desired_node_pool_autoscaling,json=desiredNodePoolAutoscaling,proto3" json:"desired_node_pool_autoscaling,omitempty"`
	// The desired list of Google Compute Engine
	// [locations](/compute/docs/zones#available) in which the cluster's nodes
	// should be located. Changing the locations a cluster is in will result
	// in nodes being either created or removed from the cluster, depending on
	// whether locations are being added or removed.
	//
	// This list must always include the cluster's primary zone.
	DesiredLocations []string `protobuf:"bytes,10,rep,name=desired_locations,json=desiredLocations,proto3" json:"desired_locations,omitempty"`
	// The desired configuration options for master authorized networks feature.
	DesiredMasterAuthorizedNetworksConfig *MasterAuthorizedNetworksConfig `protobuf:"bytes,12,opt,name=desired_master_authorized_networks_config,json=desiredMasterAuthorizedNetworksConfig,proto3" json:"desired_master_authorized_networks_config,omitempty"`
	// The desired configuration options for the PodSecurityPolicy feature.
	DesiredPodSecurityPolicyConfig *PodSecurityPolicyConfig `protobuf:"bytes,14,opt,name=desired_pod_security_policy_config,json=desiredPodSecurityPolicyConfig,proto3" json:"desired_pod_security_policy_config,omitempty"`
	// The Kubernetes version to change the master to. The only valid value is the
	// latest supported version. Use "-" to have the server automatically select
	// the latest version.
	DesiredMasterVersion string `protobuf:"bytes,100,opt,name=desired_master_version,json=desiredMasterVersion,proto3" json:"desired_master_version,omitempty"`
}

func (x *ClusterUpdate) Reset() {
	*x = ClusterUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClusterUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClusterUpdate) ProtoMessage() {}

func (x *ClusterUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClusterUpdate.ProtoReflect.Descriptor instead.
func (*ClusterUpdate) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{14}
}

func (x *ClusterUpdate) GetDesiredNodeVersion() string {
	if x != nil {
		return x.DesiredNodeVersion
	}
	return ""
}

func (x *ClusterUpdate) GetDesiredMonitoringService() string {
	if x != nil {
		return x.DesiredMonitoringService
	}
	return ""
}

func (x *ClusterUpdate) GetDesiredAddonsConfig() *AddonsConfig {
	if x != nil {
		return x.DesiredAddonsConfig
	}
	return nil
}

func (x *ClusterUpdate) GetDesiredNodePoolId() string {
	if x != nil {
		return x.DesiredNodePoolId
	}
	return ""
}

func (x *ClusterUpdate) GetDesiredImageType() string {
	if x != nil {
		return x.DesiredImageType
	}
	return ""
}

func (x *ClusterUpdate) GetDesiredNodePoolAutoscaling() *NodePoolAutoscaling {
	if x != nil {
		return x.DesiredNodePoolAutoscaling
	}
	return nil
}

func (x *ClusterUpdate) GetDesiredLocations() []string {
	if x != nil {
		return x.DesiredLocations
	}
	return nil
}

func (x *ClusterUpdate) GetDesiredMasterAuthorizedNetworksConfig() *MasterAuthorizedNetworksConfig {
	if x != nil {
		return x.DesiredMasterAuthorizedNetworksConfig
	}
	return nil
}

func (x *ClusterUpdate) GetDesiredPodSecurityPolicyConfig() *PodSecurityPolicyConfig {
	if x != nil {
		return x.DesiredPodSecurityPolicyConfig
	}
	return nil
}

func (x *ClusterUpdate) GetDesiredMasterVersion() string {
	if x != nil {
		return x.DesiredMasterVersion
	}
	return ""
}

// This operation resource represents operations that may have happened or are
// happening on the cluster. All fields are output only.
type Operation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The server-assigned ID for the operation.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the operation
	// is taking place.
	// This field is deprecated, use location instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The operation type.
	OperationType Operation_Type `protobuf:"varint,3,opt,name=operation_type,json=operationType,proto3,enum=google.container.v1alpha1.Operation_Type" json:"operation_type,omitempty"`
	// The current status of the operation.
	Status Operation_Status `protobuf:"varint,4,opt,name=status,proto3,enum=google.container.v1alpha1.Operation_Status" json:"status,omitempty"`
	// Detailed operation progress, if available.
	Detail string `protobuf:"bytes,8,opt,name=detail,proto3" json:"detail,omitempty"`
	// If an error has occurred, a textual description of the error.
	StatusMessage string `protobuf:"bytes,5,opt,name=status_message,json=statusMessage,proto3" json:"status_message,omitempty"`
	// Server-defined URL for the resource.
	SelfLink string `protobuf:"bytes,6,opt,name=self_link,json=selfLink,proto3" json:"self_link,omitempty"`
	// Server-defined URL for the target of the operation.
	TargetLink string `protobuf:"bytes,7,opt,name=target_link,json=targetLink,proto3" json:"target_link,omitempty"`
	// [Output only] The name of the Google Compute Engine
	// [zone](/compute/docs/regions-zones/regions-zones#available) or
	// [region](/compute/docs/regions-zones/regions-zones#available) in which
	// the cluster resides.
	Location string `protobuf:"bytes,9,opt,name=location,proto3" json:"location,omitempty"`
	// [Output only] The time the operation started, in
	// [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
	StartTime string `protobuf:"bytes,10,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// [Output only] The time the operation completed, in
	// [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
	EndTime string `protobuf:"bytes,11,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (x *Operation) Reset() {
	*x = Operation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Operation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Operation) ProtoMessage() {}

func (x *Operation) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Operation.ProtoReflect.Descriptor instead.
func (*Operation) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{15}
}

func (x *Operation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Operation) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *Operation) GetOperationType() Operation_Type {
	if x != nil {
		return x.OperationType
	}
	return Operation_TYPE_UNSPECIFIED
}

func (x *Operation) GetStatus() Operation_Status {
	if x != nil {
		return x.Status
	}
	return Operation_STATUS_UNSPECIFIED
}

func (x *Operation) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

func (x *Operation) GetStatusMessage() string {
	if x != nil {
		return x.StatusMessage
	}
	return ""
}

func (x *Operation) GetSelfLink() string {
	if x != nil {
		return x.SelfLink
	}
	return ""
}

func (x *Operation) GetTargetLink() string {
	if x != nil {
		return x.TargetLink
	}
	return ""
}

func (x *Operation) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *Operation) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *Operation) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

// CreateClusterRequest creates a cluster.
type CreateClusterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	// This field is deprecated, use parent instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use parent instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// A [cluster
	// resource](/container-engine/reference/rest/v1alpha1/projects.zones.clusters)
	Cluster *Cluster `protobuf:"bytes,3,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// The parent (project and location) where the cluster will be created.
	// Specified in the format 'projects/*/locations/*'.
	Parent string `protobuf:"bytes,5,opt,name=parent,proto3" json:"parent,omitempty"`
}

func (x *CreateClusterRequest) Reset() {
	*x = CreateClusterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateClusterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateClusterRequest) ProtoMessage() {}

func (x *CreateClusterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateClusterRequest.ProtoReflect.Descriptor instead.
func (*CreateClusterRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{16}
}

func (x *CreateClusterRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *CreateClusterRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateClusterRequest) GetCluster() *Cluster {
	if x != nil {
		return x.Cluster
	}
	return nil
}

func (x *CreateClusterRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

// GetClusterRequest gets the settings of a cluster.
type GetClusterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster to retrieve.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The name (project, location, cluster) of the cluster to retrieve.
	// Specified in the format 'projects/*/locations/*/clusters/*'.
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetClusterRequest) Reset() {
	*x = GetClusterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetClusterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClusterRequest) ProtoMessage() {}

func (x *GetClusterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClusterRequest.ProtoReflect.Descriptor instead.
func (*GetClusterRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetClusterRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *GetClusterRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetClusterRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *GetClusterRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// UpdateClusterRequest updates the settings of a cluster.
type UpdateClusterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster to upgrade.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// A description of the update.
	Update *ClusterUpdate `protobuf:"bytes,4,opt,name=update,proto3" json:"update,omitempty"`
	// The name (project, location, cluster) of the cluster to update.
	// Specified in the format 'projects/*/locations/*/clusters/*'.
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *UpdateClusterRequest) Reset() {
	*x = UpdateClusterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateClusterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateClusterRequest) ProtoMessage() {}

func (x *UpdateClusterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateClusterRequest.ProtoReflect.Descriptor instead.
func (*UpdateClusterRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{18}
}

func (x *UpdateClusterRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UpdateClusterRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UpdateClusterRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *UpdateClusterRequest) GetUpdate() *ClusterUpdate {
	if x != nil {
		return x.Update
	}
	return nil
}

func (x *UpdateClusterRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// SetNodePoolVersionRequest updates the version of a node pool.
type UpdateNodePoolRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster to upgrade.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The name of the node pool to upgrade.
	// This field is deprecated, use name instead.
	NodePoolId string `protobuf:"bytes,4,opt,name=node_pool_id,json=nodePoolId,proto3" json:"node_pool_id,omitempty"`
	// The Kubernetes version to change the nodes to (typically an
	// upgrade). Use `-` to upgrade to the latest version supported by
	// the server.
	NodeVersion string `protobuf:"bytes,5,opt,name=node_version,json=nodeVersion,proto3" json:"node_version,omitempty"`
	// The desired image type for the node pool.
	ImageType string `protobuf:"bytes,6,opt,name=image_type,json=imageType,proto3" json:"image_type,omitempty"`
	// The name (project, location, cluster, node pool) of the node pool to update.
	// Specified in the format 'projects/*/locations/*/clusters/*/nodePools/*'.
	Name string `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *UpdateNodePoolRequest) Reset() {
	*x = UpdateNodePoolRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateNodePoolRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNodePoolRequest) ProtoMessage() {}

func (x *UpdateNodePoolRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNodePoolRequest.ProtoReflect.Descriptor instead.
func (*UpdateNodePoolRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{19}
}

func (x *UpdateNodePoolRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UpdateNodePoolRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UpdateNodePoolRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *UpdateNodePoolRequest) GetNodePoolId() string {
	if x != nil {
		return x.NodePoolId
	}
	return ""
}

func (x *UpdateNodePoolRequest) GetNodeVersion() string {
	if x != nil {
		return x.NodeVersion
	}
	return ""
}

func (x *UpdateNodePoolRequest) GetImageType() string {
	if x != nil {
		return x.ImageType
	}
	return ""
}

func (x *UpdateNodePoolRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// SetNodePoolAutoscalingRequest sets the autoscaler settings of a node pool.
type SetNodePoolAutoscalingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster to upgrade.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The name of the node pool to upgrade.
	// This field is deprecated, use name instead.
	NodePoolId string `protobuf:"bytes,4,opt,name=node_pool_id,json=nodePoolId,proto3" json:"node_pool_id,omitempty"`
	// Autoscaling configuration for the node pool.
	Autoscaling *NodePoolAutoscaling `protobuf:"bytes,5,opt,name=autoscaling,proto3" json:"autoscaling,omitempty"`
	// The name (project, location, cluster, node pool) of the node pool to set
	// autoscaler settings. Specified in the format
	// 'projects/*/locations/*/clusters/*/nodePools/*'.
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SetNodePoolAutoscalingRequest) Reset() {
	*x = SetNodePoolAutoscalingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetNodePoolAutoscalingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetNodePoolAutoscalingRequest) ProtoMessage() {}

func (x *SetNodePoolAutoscalingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetNodePoolAutoscalingRequest.ProtoReflect.Descriptor instead.
func (*SetNodePoolAutoscalingRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{20}
}

func (x *SetNodePoolAutoscalingRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SetNodePoolAutoscalingRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *SetNodePoolAutoscalingRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *SetNodePoolAutoscalingRequest) GetNodePoolId() string {
	if x != nil {
		return x.NodePoolId
	}
	return ""
}

func (x *SetNodePoolAutoscalingRequest) GetAutoscaling() *NodePoolAutoscaling {
	if x != nil {
		return x.Autoscaling
	}
	return nil
}

func (x *SetNodePoolAutoscalingRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// SetLoggingServiceRequest sets the logging service of a cluster.
type SetLoggingServiceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster to upgrade.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The logging service the cluster should use to write metrics.
	// Currently available options:
	//
	// * "logging.googleapis.com" - the Google Cloud Logging service
	// * "none" - no metrics will be exported from the cluster
	LoggingService string `protobuf:"bytes,4,opt,name=logging_service,json=loggingService,proto3" json:"logging_service,omitempty"`
	// The name (project, location, cluster) of the cluster to set logging.
	// Specified in the format 'projects/*/locations/*/clusters/*'.
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SetLoggingServiceRequest) Reset() {
	*x = SetLoggingServiceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetLoggingServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetLoggingServiceRequest) ProtoMessage() {}

func (x *SetLoggingServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetLoggingServiceRequest.ProtoReflect.Descriptor instead.
func (*SetLoggingServiceRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{21}
}

func (x *SetLoggingServiceRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SetLoggingServiceRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *SetLoggingServiceRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *SetLoggingServiceRequest) GetLoggingService() string {
	if x != nil {
		return x.LoggingService
	}
	return ""
}

func (x *SetLoggingServiceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// SetMonitoringServiceRequest sets the monitoring service of a cluster.
type SetMonitoringServiceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster to upgrade.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The monitoring service the cluster should use to write metrics.
	// Currently available options:
	//
	// * "monitoring.googleapis.com" - the Google Cloud Monitoring service
	// * "none" - no metrics will be exported from the cluster
	MonitoringService string `protobuf:"bytes,4,opt,name=monitoring_service,json=monitoringService,proto3" json:"monitoring_service,omitempty"`
	// The name (project, location, cluster) of the cluster to set monitoring.
	// Specified in the format 'projects/*/locations/*/clusters/*'.
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SetMonitoringServiceRequest) Reset() {
	*x = SetMonitoringServiceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetMonitoringServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetMonitoringServiceRequest) ProtoMessage() {}

func (x *SetMonitoringServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetMonitoringServiceRequest.ProtoReflect.Descriptor instead.
func (*SetMonitoringServiceRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{22}
}

func (x *SetMonitoringServiceRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SetMonitoringServiceRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *SetMonitoringServiceRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *SetMonitoringServiceRequest) GetMonitoringService() string {
	if x != nil {
		return x.MonitoringService
	}
	return ""
}

func (x *SetMonitoringServiceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// SetAddonsRequest sets the addons associated with the cluster.
type SetAddonsConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster to upgrade.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The desired configurations for the various addons available to run in the
	// cluster.
	AddonsConfig *AddonsConfig `protobuf:"bytes,4,opt,name=addons_config,json=addonsConfig,proto3" json:"addons_config,omitempty"`
	// The name (project, location, cluster) of the cluster to set addons.
	// Specified in the format 'projects/*/locations/*/clusters/*'.
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SetAddonsConfigRequest) Reset() {
	*x = SetAddonsConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetAddonsConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAddonsConfigRequest) ProtoMessage() {}

func (x *SetAddonsConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAddonsConfigRequest.ProtoReflect.Descriptor instead.
func (*SetAddonsConfigRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{23}
}

func (x *SetAddonsConfigRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SetAddonsConfigRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *SetAddonsConfigRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *SetAddonsConfigRequest) GetAddonsConfig() *AddonsConfig {
	if x != nil {
		return x.AddonsConfig
	}
	return nil
}

func (x *SetAddonsConfigRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// SetLocationsRequest sets the locations of the cluster.
type SetLocationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster to upgrade.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The desired list of Google Compute Engine
	// [locations](/compute/docs/zones#available) in which the cluster's nodes
	// should be located. Changing the locations a cluster is in will result
	// in nodes being either created or removed from the cluster, depending on
	// whether locations are being added or removed.
	//
	// This list must always include the cluster's primary zone.
	Locations []string `protobuf:"bytes,4,rep,name=locations,proto3" json:"locations,omitempty"`
	// The name (project, location, cluster) of the cluster to set locations.
	// Specified in the format 'projects/*/locations/*/clusters/*'.
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SetLocationsRequest) Reset() {
	*x = SetLocationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetLocationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetLocationsRequest) ProtoMessage() {}

func (x *SetLocationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetLocationsRequest.ProtoReflect.Descriptor instead.
func (*SetLocationsRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{24}
}

func (x *SetLocationsRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SetLocationsRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *SetLocationsRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *SetLocationsRequest) GetLocations() []string {
	if x != nil {
		return x.Locations
	}
	return nil
}

func (x *SetLocationsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// UpdateMasterRequest updates the master of the cluster.
type UpdateMasterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster to upgrade.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The Kubernetes version to change the master to. The only valid value is the
	// latest supported version. Use "-" to have the server automatically select
	// the latest version.
	MasterVersion string `protobuf:"bytes,4,opt,name=master_version,json=masterVersion,proto3" json:"master_version,omitempty"`
	// The name (project, location, cluster) of the cluster to update.
	// Specified in the format 'projects/*/locations/*/clusters/*'.
	Name string `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *UpdateMasterRequest) Reset() {
	*x = UpdateMasterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateMasterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMasterRequest) ProtoMessage() {}

func (x *UpdateMasterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMasterRequest.ProtoReflect.Descriptor instead.
func (*UpdateMasterRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{25}
}

func (x *UpdateMasterRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UpdateMasterRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UpdateMasterRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *UpdateMasterRequest) GetMasterVersion() string {
	if x != nil {
		return x.MasterVersion
	}
	return ""
}

func (x *UpdateMasterRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// SetMasterAuthRequest updates the admin password of a cluster.
type SetMasterAuthRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster to upgrade.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The exact form of action to be taken on the master auth.
	Action SetMasterAuthRequest_Action `protobuf:"varint,4,opt,name=action,proto3,enum=google.container.v1alpha1.SetMasterAuthRequest_Action" json:"action,omitempty"`
	// A description of the update.
	Update *MasterAuth `protobuf:"bytes,5,opt,name=update,proto3" json:"update,omitempty"`
	// The name (project, location, cluster) of the cluster to set auth.
	// Specified in the format 'projects/*/locations/*/clusters/*'.
	Name string `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SetMasterAuthRequest) Reset() {
	*x = SetMasterAuthRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetMasterAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetMasterAuthRequest) ProtoMessage() {}

func (x *SetMasterAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetMasterAuthRequest.ProtoReflect.Descriptor instead.
func (*SetMasterAuthRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{26}
}

func (x *SetMasterAuthRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SetMasterAuthRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *SetMasterAuthRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *SetMasterAuthRequest) GetAction() SetMasterAuthRequest_Action {
	if x != nil {
		return x.Action
	}
	return SetMasterAuthRequest_UNKNOWN
}

func (x *SetMasterAuthRequest) GetUpdate() *MasterAuth {
	if x != nil {
		return x.Update
	}
	return nil
}

func (x *SetMasterAuthRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// DeleteClusterRequest deletes a cluster.
type DeleteClusterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster to delete.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The name (project, location, cluster) of the cluster to delete.
	// Specified in the format 'projects/*/locations/*/clusters/*'.
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteClusterRequest) Reset() {
	*x = DeleteClusterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteClusterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteClusterRequest) ProtoMessage() {}

func (x *DeleteClusterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteClusterRequest.ProtoReflect.Descriptor instead.
func (*DeleteClusterRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{27}
}

func (x *DeleteClusterRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *DeleteClusterRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DeleteClusterRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *DeleteClusterRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// ListClustersRequest lists clusters.
type ListClustersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	// This field is deprecated, use parent instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides, or "-" for all zones.
	// This field is deprecated, use parent instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The parent (project and location) where the clusters will be listed.
	// Specified in the format 'projects/*/locations/*'.
	// Location "-" matches all zones and all regions.
	Parent string `protobuf:"bytes,4,opt,name=parent,proto3" json:"parent,omitempty"`
}

func (x *ListClustersRequest) Reset() {
	*x = ListClustersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClustersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClustersRequest) ProtoMessage() {}

func (x *ListClustersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClustersRequest.ProtoReflect.Descriptor instead.
func (*ListClustersRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{28}
}

func (x *ListClustersRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ListClustersRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListClustersRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

// ListClustersResponse is the result of ListClustersRequest.
type ListClustersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of clusters in the project in the specified zone, or
	// across all ones.
	Clusters []*Cluster `protobuf:"bytes,1,rep,name=clusters,proto3" json:"clusters,omitempty"`
	// If any zones are listed here, the list of clusters returned
	// may be missing those zones.
	MissingZones []string `protobuf:"bytes,2,rep,name=missing_zones,json=missingZones,proto3" json:"missing_zones,omitempty"`
}

func (x *ListClustersResponse) Reset() {
	*x = ListClustersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListClustersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClustersResponse) ProtoMessage() {}

func (x *ListClustersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClustersResponse.ProtoReflect.Descriptor instead.
func (*ListClustersResponse) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{29}
}

func (x *ListClustersResponse) GetClusters() []*Cluster {
	if x != nil {
		return x.Clusters
	}
	return nil
}

func (x *ListClustersResponse) GetMissingZones() []string {
	if x != nil {
		return x.MissingZones
	}
	return nil
}

// GetOperationRequest gets a single operation.
type GetOperationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The server-assigned `name` of the operation.
	// This field is deprecated, use name instead.
	OperationId string `protobuf:"bytes,3,opt,name=operation_id,json=operationId,proto3" json:"operation_id,omitempty"`
	// The name (project, location, operation id) of the operation to get.
	// Specified in the format 'projects/*/locations/*/operations/*'.
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetOperationRequest) Reset() {
	*x = GetOperationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOperationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOperationRequest) ProtoMessage() {}

func (x *GetOperationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOperationRequest.ProtoReflect.Descriptor instead.
func (*GetOperationRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{30}
}

func (x *GetOperationRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *GetOperationRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetOperationRequest) GetOperationId() string {
	if x != nil {
		return x.OperationId
	}
	return ""
}

func (x *GetOperationRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// ListOperationsRequest lists operations.
type ListOperationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	// This field is deprecated, use parent instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine [zone](/compute/docs/zones#available)
	// to return operations for, or `-` for all zones.
	// This field is deprecated, use parent instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The parent (project and location) where the operations will be listed.
	// Specified in the format 'projects/*/locations/*'.
	// Location "-" matches all zones and all regions.
	Parent string `protobuf:"bytes,4,opt,name=parent,proto3" json:"parent,omitempty"`
}

func (x *ListOperationsRequest) Reset() {
	*x = ListOperationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOperationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOperationsRequest) ProtoMessage() {}

func (x *ListOperationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOperationsRequest.ProtoReflect.Descriptor instead.
func (*ListOperationsRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{31}
}

func (x *ListOperationsRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ListOperationsRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListOperationsRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

// CancelOperationRequest cancels a single operation.
type CancelOperationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the operation resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The server-assigned `name` of the operation.
	// This field is deprecated, use name instead.
	OperationId string `protobuf:"bytes,3,opt,name=operation_id,json=operationId,proto3" json:"operation_id,omitempty"`
	// The name (project, location, operation id) of the operation to cancel.
	// Specified in the format 'projects/*/locations/*/operations/*'.
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *CancelOperationRequest) Reset() {
	*x = CancelOperationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelOperationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelOperationRequest) ProtoMessage() {}

func (x *CancelOperationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelOperationRequest.ProtoReflect.Descriptor instead.
func (*CancelOperationRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{32}
}

func (x *CancelOperationRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *CancelOperationRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CancelOperationRequest) GetOperationId() string {
	if x != nil {
		return x.OperationId
	}
	return ""
}

func (x *CancelOperationRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// ListOperationsResponse is the result of ListOperationsRequest.
type ListOperationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of operations in the project in the specified zone.
	Operations []*Operation `protobuf:"bytes,1,rep,name=operations,proto3" json:"operations,omitempty"`
	// If any zones are listed here, the list of operations returned
	// may be missing the operations from those zones.
	MissingZones []string `protobuf:"bytes,2,rep,name=missing_zones,json=missingZones,proto3" json:"missing_zones,omitempty"`
}

func (x *ListOperationsResponse) Reset() {
	*x = ListOperationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOperationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOperationsResponse) ProtoMessage() {}

func (x *ListOperationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOperationsResponse.ProtoReflect.Descriptor instead.
func (*ListOperationsResponse) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{33}
}

func (x *ListOperationsResponse) GetOperations() []*Operation {
	if x != nil {
		return x.Operations
	}
	return nil
}

func (x *ListOperationsResponse) GetMissingZones() []string {
	if x != nil {
		return x.MissingZones
	}
	return nil
}

// Gets the current Container Engine service configuration.
type GetServerConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine [zone](/compute/docs/zones#available)
	// to return operations for.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name (project and location) of the server config to get
	// Specified in the format 'projects/*/locations/*'.
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetServerConfigRequest) Reset() {
	*x = GetServerConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServerConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServerConfigRequest) ProtoMessage() {}

func (x *GetServerConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServerConfigRequest.ProtoReflect.Descriptor instead.
func (*GetServerConfigRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{34}
}

func (x *GetServerConfigRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *GetServerConfigRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetServerConfigRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Container Engine service configuration.
type ServerConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Version of Kubernetes the service deploys by default.
	DefaultClusterVersion string `protobuf:"bytes,1,opt,name=default_cluster_version,json=defaultClusterVersion,proto3" json:"default_cluster_version,omitempty"`
	// List of valid node upgrade target versions.
	ValidNodeVersions []string `protobuf:"bytes,3,rep,name=valid_node_versions,json=validNodeVersions,proto3" json:"valid_node_versions,omitempty"`
	// Default image type.
	DefaultImageType string `protobuf:"bytes,4,opt,name=default_image_type,json=defaultImageType,proto3" json:"default_image_type,omitempty"`
	// List of valid image types.
	ValidImageTypes []string `protobuf:"bytes,5,rep,name=valid_image_types,json=validImageTypes,proto3" json:"valid_image_types,omitempty"`
	// List of valid master versions.
	ValidMasterVersions []string `protobuf:"bytes,6,rep,name=valid_master_versions,json=validMasterVersions,proto3" json:"valid_master_versions,omitempty"`
}

func (x *ServerConfig) Reset() {
	*x = ServerConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerConfig) ProtoMessage() {}

func (x *ServerConfig) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerConfig.ProtoReflect.Descriptor instead.
func (*ServerConfig) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{35}
}

func (x *ServerConfig) GetDefaultClusterVersion() string {
	if x != nil {
		return x.DefaultClusterVersion
	}
	return ""
}

func (x *ServerConfig) GetValidNodeVersions() []string {
	if x != nil {
		return x.ValidNodeVersions
	}
	return nil
}

func (x *ServerConfig) GetDefaultImageType() string {
	if x != nil {
		return x.DefaultImageType
	}
	return ""
}

func (x *ServerConfig) GetValidImageTypes() []string {
	if x != nil {
		return x.ValidImageTypes
	}
	return nil
}

func (x *ServerConfig) GetValidMasterVersions() []string {
	if x != nil {
		return x.ValidMasterVersions
	}
	return nil
}

// CreateNodePoolRequest creates a node pool for a cluster.
type CreateNodePoolRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://developers.google.com/console/help/new/#projectnumber).
	// This field is deprecated, use parent instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use parent instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster.
	// This field is deprecated, use parent instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The node pool to create.
	NodePool *NodePool `protobuf:"bytes,4,opt,name=node_pool,json=nodePool,proto3" json:"node_pool,omitempty"`
	// The parent (project, location, cluster id) where the node pool will be created.
	// Specified in the format 'projects/*/locations/*/clusters/*/nodePools/*'.
	Parent string `protobuf:"bytes,6,opt,name=parent,proto3" json:"parent,omitempty"`
}

func (x *CreateNodePoolRequest) Reset() {
	*x = CreateNodePoolRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateNodePoolRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNodePoolRequest) ProtoMessage() {}

func (x *CreateNodePoolRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNodePoolRequest.ProtoReflect.Descriptor instead.
func (*CreateNodePoolRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{36}
}

func (x *CreateNodePoolRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *CreateNodePoolRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateNodePoolRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *CreateNodePoolRequest) GetNodePool() *NodePool {
	if x != nil {
		return x.NodePool
	}
	return nil
}

func (x *CreateNodePoolRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

// DeleteNodePoolRequest deletes a node pool for a cluster.
type DeleteNodePoolRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://developers.google.com/console/help/new/#projectnumber).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The name of the node pool to delete.
	// This field is deprecated, use name instead.
	NodePoolId string `protobuf:"bytes,4,opt,name=node_pool_id,json=nodePoolId,proto3" json:"node_pool_id,omitempty"`
	// The name (project, location, cluster, node pool id) of the node pool to delete.
	// Specified in the format 'projects/*/locations/*/clusters/*/nodePools/*'.
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteNodePoolRequest) Reset() {
	*x = DeleteNodePoolRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteNodePoolRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNodePoolRequest) ProtoMessage() {}

func (x *DeleteNodePoolRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNodePoolRequest.ProtoReflect.Descriptor instead.
func (*DeleteNodePoolRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{37}
}

func (x *DeleteNodePoolRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *DeleteNodePoolRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DeleteNodePoolRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *DeleteNodePoolRequest) GetNodePoolId() string {
	if x != nil {
		return x.NodePoolId
	}
	return ""
}

func (x *DeleteNodePoolRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// ListNodePoolsRequest lists the node pool(s) for a cluster.
type ListNodePoolsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://developers.google.com/console/help/new/#projectnumber).
	// This field is deprecated, use parent instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use parent instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster.
	// This field is deprecated, use parent instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The parent (project, location, cluster id) where the node pools will be listed.
	// Specified in the format 'projects/*/locations/*/clusters/*'.
	Parent string `protobuf:"bytes,5,opt,name=parent,proto3" json:"parent,omitempty"`
}

func (x *ListNodePoolsRequest) Reset() {
	*x = ListNodePoolsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListNodePoolsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNodePoolsRequest) ProtoMessage() {}

func (x *ListNodePoolsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNodePoolsRequest.ProtoReflect.Descriptor instead.
func (*ListNodePoolsRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{38}
}

func (x *ListNodePoolsRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ListNodePoolsRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListNodePoolsRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *ListNodePoolsRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

// GetNodePoolRequest retrieves a node pool for a cluster.
type GetNodePoolRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://developers.google.com/console/help/new/#projectnumber).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The name of the node pool.
	// This field is deprecated, use name instead.
	NodePoolId string `protobuf:"bytes,4,opt,name=node_pool_id,json=nodePoolId,proto3" json:"node_pool_id,omitempty"`
	// The name (project, location, cluster, node pool id) of the node pool to get.
	// Specified in the format 'projects/*/locations/*/clusters/*/nodePools/*'.
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetNodePoolRequest) Reset() {
	*x = GetNodePoolRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNodePoolRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNodePoolRequest) ProtoMessage() {}

func (x *GetNodePoolRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNodePoolRequest.ProtoReflect.Descriptor instead.
func (*GetNodePoolRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{39}
}

func (x *GetNodePoolRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *GetNodePoolRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetNodePoolRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *GetNodePoolRequest) GetNodePoolId() string {
	if x != nil {
		return x.NodePoolId
	}
	return ""
}

func (x *GetNodePoolRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// NodePool contains the name and configuration for a cluster's node pool.
// Node pools are a set of nodes (i.e. VM's), with a common configuration and
// specification, under the control of the cluster master. They may have a set
// of Kubernetes labels applied to them, which may be used to reference them
// during pod scheduling. They may also be resized up or down, to accommodate
// the workload.
type NodePool struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the node pool.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The node configuration of the pool.
	Config *NodeConfig `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
	// The initial node count for the pool. You must ensure that your
	// Compute Engine <a href="/compute/docs/resource-quotas">resource quota</a>
	// is sufficient for this number of instances. You must also have available
	// firewall and routes quota.
	InitialNodeCount int32 `protobuf:"varint,3,opt,name=initial_node_count,json=initialNodeCount,proto3" json:"initial_node_count,omitempty"`
	// Autoscaler configuration for this NodePool. Autoscaler is enabled
	// only if a valid configuration is present.
	Autoscaling *NodePoolAutoscaling `protobuf:"bytes,4,opt,name=autoscaling,proto3" json:"autoscaling,omitempty"`
	// NodeManagement configuration for this NodePool.
	Management *NodeManagement `protobuf:"bytes,5,opt,name=management,proto3" json:"management,omitempty"`
	// [Output only] Server-defined URL for the resource.
	SelfLink string `protobuf:"bytes,100,opt,name=self_link,json=selfLink,proto3" json:"self_link,omitempty"`
	// [Output only] The version of the Kubernetes of this node.
	Version string `protobuf:"bytes,101,opt,name=version,proto3" json:"version,omitempty"`
	// [Output only] The resource URLs of [instance
	// groups](/compute/docs/instance-groups/) associated with this
	// node pool.
	InstanceGroupUrls []string `protobuf:"bytes,102,rep,name=instance_group_urls,json=instanceGroupUrls,proto3" json:"instance_group_urls,omitempty"`
	// [Output only] The status of the nodes in this pool instance.
	Status NodePool_Status `protobuf:"varint,103,opt,name=status,proto3,enum=google.container.v1alpha1.NodePool_Status" json:"status,omitempty"`
	// [Output only] Additional information about the current status of this
	// node pool instance, if available.
	StatusMessage string `protobuf:"bytes,104,opt,name=status_message,json=statusMessage,proto3" json:"status_message,omitempty"`
}

func (x *NodePool) Reset() {
	*x = NodePool{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodePool) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodePool) ProtoMessage() {}

func (x *NodePool) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodePool.ProtoReflect.Descriptor instead.
func (*NodePool) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{40}
}

func (x *NodePool) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NodePool) GetConfig() *NodeConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *NodePool) GetInitialNodeCount() int32 {
	if x != nil {
		return x.InitialNodeCount
	}
	return 0
}

func (x *NodePool) GetAutoscaling() *NodePoolAutoscaling {
	if x != nil {
		return x.Autoscaling
	}
	return nil
}

func (x *NodePool) GetManagement() *NodeManagement {
	if x != nil {
		return x.Management
	}
	return nil
}

func (x *NodePool) GetSelfLink() string {
	if x != nil {
		return x.SelfLink
	}
	return ""
}

func (x *NodePool) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *NodePool) GetInstanceGroupUrls() []string {
	if x != nil {
		return x.InstanceGroupUrls
	}
	return nil
}

func (x *NodePool) GetStatus() NodePool_Status {
	if x != nil {
		return x.Status
	}
	return NodePool_STATUS_UNSPECIFIED
}

func (x *NodePool) GetStatusMessage() string {
	if x != nil {
		return x.StatusMessage
	}
	return ""
}

// NodeManagement defines the set of node management services turned on for the
// node pool.
type NodeManagement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether the nodes will be automatically upgraded.
	AutoUpgrade bool `protobuf:"varint,1,opt,name=auto_upgrade,json=autoUpgrade,proto3" json:"auto_upgrade,omitempty"`
	// Whether the nodes will be automatically repaired.
	AutoRepair bool `protobuf:"varint,2,opt,name=auto_repair,json=autoRepair,proto3" json:"auto_repair,omitempty"`
	// Specifies the Auto Upgrade knobs for the node pool.
	UpgradeOptions *AutoUpgradeOptions `protobuf:"bytes,10,opt,name=upgrade_options,json=upgradeOptions,proto3" json:"upgrade_options,omitempty"`
}

func (x *NodeManagement) Reset() {
	*x = NodeManagement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeManagement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeManagement) ProtoMessage() {}

func (x *NodeManagement) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeManagement.ProtoReflect.Descriptor instead.
func (*NodeManagement) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{41}
}

func (x *NodeManagement) GetAutoUpgrade() bool {
	if x != nil {
		return x.AutoUpgrade
	}
	return false
}

func (x *NodeManagement) GetAutoRepair() bool {
	if x != nil {
		return x.AutoRepair
	}
	return false
}

func (x *NodeManagement) GetUpgradeOptions() *AutoUpgradeOptions {
	if x != nil {
		return x.UpgradeOptions
	}
	return nil
}

// AutoUpgradeOptions defines the set of options for the user to control how
// the Auto Upgrades will proceed.
type AutoUpgradeOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [Output only] This field is set when upgrades are about to commence
	// with the approximate start time for the upgrades, in
	// [RFC3339](https://www.ietf.org/rfc/rfc3339.txt) text format.
	AutoUpgradeStartTime string `protobuf:"bytes,1,opt,name=auto_upgrade_start_time,json=autoUpgradeStartTime,proto3" json:"auto_upgrade_start_time,omitempty"`
	// [Output only] This field is set when upgrades are about to commence
	// with the description of the upgrade.
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *AutoUpgradeOptions) Reset() {
	*x = AutoUpgradeOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutoUpgradeOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoUpgradeOptions) ProtoMessage() {}

func (x *AutoUpgradeOptions) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoUpgradeOptions.ProtoReflect.Descriptor instead.
func (*AutoUpgradeOptions) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{42}
}

func (x *AutoUpgradeOptions) GetAutoUpgradeStartTime() string {
	if x != nil {
		return x.AutoUpgradeStartTime
	}
	return ""
}

func (x *AutoUpgradeOptions) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// MaintenancePolicy defines the maintenance policy to be used for the cluster.
type MaintenancePolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Specifies the maintenance window in which maintenance may be performed.
	Window *MaintenanceWindow `protobuf:"bytes,1,opt,name=window,proto3" json:"window,omitempty"`
}

func (x *MaintenancePolicy) Reset() {
	*x = MaintenancePolicy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaintenancePolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintenancePolicy) ProtoMessage() {}

func (x *MaintenancePolicy) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintenancePolicy.ProtoReflect.Descriptor instead.
func (*MaintenancePolicy) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{43}
}

func (x *MaintenancePolicy) GetWindow() *MaintenanceWindow {
	if x != nil {
		return x.Window
	}
	return nil
}

// MaintenanceWindow defines the maintenance window to be used for the cluster.
type MaintenanceWindow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unimplemented, reserved for future use.
	// HourlyMaintenanceWindow hourly_maintenance_window = 1;
	//
	// Types that are assignable to Policy:
	//	*MaintenanceWindow_DailyMaintenanceWindow
	Policy isMaintenanceWindow_Policy `protobuf_oneof:"policy"`
}

func (x *MaintenanceWindow) Reset() {
	*x = MaintenanceWindow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaintenanceWindow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintenanceWindow) ProtoMessage() {}

func (x *MaintenanceWindow) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintenanceWindow.ProtoReflect.Descriptor instead.
func (*MaintenanceWindow) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{44}
}

func (m *MaintenanceWindow) GetPolicy() isMaintenanceWindow_Policy {
	if m != nil {
		return m.Policy
	}
	return nil
}

func (x *MaintenanceWindow) GetDailyMaintenanceWindow() *DailyMaintenanceWindow {
	if x, ok := x.GetPolicy().(*MaintenanceWindow_DailyMaintenanceWindow); ok {
		return x.DailyMaintenanceWindow
	}
	return nil
}

type isMaintenanceWindow_Policy interface {
	isMaintenanceWindow_Policy()
}

type MaintenanceWindow_DailyMaintenanceWindow struct {
	// DailyMaintenanceWindow specifies a daily maintenance operation window.
	DailyMaintenanceWindow *DailyMaintenanceWindow `protobuf:"bytes,2,opt,name=daily_maintenance_window,json=dailyMaintenanceWindow,proto3,oneof"`
}

func (*MaintenanceWindow_DailyMaintenanceWindow) isMaintenanceWindow_Policy() {}

// Time window specified for daily maintenance operations.
type DailyMaintenanceWindow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Time within the maintenance window to start the maintenance operations.
	// It must be in format "HH:MM”, where HH : [00-23] and MM : [00-59] GMT.
	StartTime string `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// [Output only] Duration of the time window, automatically chosen to be
	// smallest possible in the given scenario.
	Duration string `protobuf:"bytes,3,opt,name=duration,proto3" json:"duration,omitempty"`
}

func (x *DailyMaintenanceWindow) Reset() {
	*x = DailyMaintenanceWindow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DailyMaintenanceWindow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DailyMaintenanceWindow) ProtoMessage() {}

func (x *DailyMaintenanceWindow) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DailyMaintenanceWindow.ProtoReflect.Descriptor instead.
func (*DailyMaintenanceWindow) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{45}
}

func (x *DailyMaintenanceWindow) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *DailyMaintenanceWindow) GetDuration() string {
	if x != nil {
		return x.Duration
	}
	return ""
}

// SetNodePoolManagementRequest sets the node management properties of a node
// pool.
type SetNodePoolManagementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster to update.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The name of the node pool to update.
	// This field is deprecated, use name instead.
	NodePoolId string `protobuf:"bytes,4,opt,name=node_pool_id,json=nodePoolId,proto3" json:"node_pool_id,omitempty"`
	// NodeManagement configuration for the node pool.
	Management *NodeManagement `protobuf:"bytes,5,opt,name=management,proto3" json:"management,omitempty"`
	// The name (project, location, cluster, node pool id) of the node pool to set
	// management properties. Specified in the format
	// 'projects/*/locations/*/clusters/*/nodePools/*'.
	Name string `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SetNodePoolManagementRequest) Reset() {
	*x = SetNodePoolManagementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetNodePoolManagementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetNodePoolManagementRequest) ProtoMessage() {}

func (x *SetNodePoolManagementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetNodePoolManagementRequest.ProtoReflect.Descriptor instead.
func (*SetNodePoolManagementRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{46}
}

func (x *SetNodePoolManagementRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SetNodePoolManagementRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *SetNodePoolManagementRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *SetNodePoolManagementRequest) GetNodePoolId() string {
	if x != nil {
		return x.NodePoolId
	}
	return ""
}

func (x *SetNodePoolManagementRequest) GetManagement() *NodeManagement {
	if x != nil {
		return x.Management
	}
	return nil
}

func (x *SetNodePoolManagementRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// SetNodePoolSizeRequest sets the size a node
// pool.
type SetNodePoolSizeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster to update.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The name of the node pool to update.
	// This field is deprecated, use name instead.
	NodePoolId string `protobuf:"bytes,4,opt,name=node_pool_id,json=nodePoolId,proto3" json:"node_pool_id,omitempty"`
	// The desired node count for the pool.
	NodeCount int32 `protobuf:"varint,5,opt,name=node_count,json=nodeCount,proto3" json:"node_count,omitempty"`
	// The name (project, location, cluster, node pool id) of the node pool to set
	// size.
	// Specified in the format 'projects/*/locations/*/clusters/*/nodePools/*'.
	Name string `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SetNodePoolSizeRequest) Reset() {
	*x = SetNodePoolSizeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetNodePoolSizeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetNodePoolSizeRequest) ProtoMessage() {}

func (x *SetNodePoolSizeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetNodePoolSizeRequest.ProtoReflect.Descriptor instead.
func (*SetNodePoolSizeRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{47}
}

func (x *SetNodePoolSizeRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SetNodePoolSizeRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *SetNodePoolSizeRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *SetNodePoolSizeRequest) GetNodePoolId() string {
	if x != nil {
		return x.NodePoolId
	}
	return ""
}

func (x *SetNodePoolSizeRequest) GetNodeCount() int32 {
	if x != nil {
		return x.NodeCount
	}
	return 0
}

func (x *SetNodePoolSizeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// RollbackNodePoolUpgradeRequest rollbacks the previously Aborted or Failed
// NodePool upgrade. This will be an no-op if the last upgrade successfully
// completed.
type RollbackNodePoolUpgradeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster to rollback.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The name of the node pool to rollback.
	// This field is deprecated, use name instead.
	NodePoolId string `protobuf:"bytes,4,opt,name=node_pool_id,json=nodePoolId,proto3" json:"node_pool_id,omitempty"`
	// The name (project, location, cluster, node pool id) of the node poll to
	// rollback upgrade.
	// Specified in the format 'projects/*/locations/*/clusters/*/nodePools/*'.
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *RollbackNodePoolUpgradeRequest) Reset() {
	*x = RollbackNodePoolUpgradeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RollbackNodePoolUpgradeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RollbackNodePoolUpgradeRequest) ProtoMessage() {}

func (x *RollbackNodePoolUpgradeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RollbackNodePoolUpgradeRequest.ProtoReflect.Descriptor instead.
func (*RollbackNodePoolUpgradeRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{48}
}

func (x *RollbackNodePoolUpgradeRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *RollbackNodePoolUpgradeRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *RollbackNodePoolUpgradeRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *RollbackNodePoolUpgradeRequest) GetNodePoolId() string {
	if x != nil {
		return x.NodePoolId
	}
	return ""
}

func (x *RollbackNodePoolUpgradeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// ListNodePoolsResponse is the result of ListNodePoolsRequest.
type ListNodePoolsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of node pools for a cluster.
	NodePools []*NodePool `protobuf:"bytes,1,rep,name=node_pools,json=nodePools,proto3" json:"node_pools,omitempty"`
}

func (x *ListNodePoolsResponse) Reset() {
	*x = ListNodePoolsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListNodePoolsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNodePoolsResponse) ProtoMessage() {}

func (x *ListNodePoolsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNodePoolsResponse.ProtoReflect.Descriptor instead.
func (*ListNodePoolsResponse) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{49}
}

func (x *ListNodePoolsResponse) GetNodePools() []*NodePool {
	if x != nil {
		return x.NodePools
	}
	return nil
}

// NodePoolAutoscaling contains information required by cluster autoscaler to
// adjust the size of the node pool to the current cluster usage.
type NodePoolAutoscaling struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Is autoscaling enabled for this node pool.
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// Minimum number of nodes in the NodePool. Must be >= 1 and <=
	// max_node_count.
	MinNodeCount int32 `protobuf:"varint,2,opt,name=min_node_count,json=minNodeCount,proto3" json:"min_node_count,omitempty"`
	// Maximum number of nodes in the NodePool. Must be >= min_node_count. There
	// has to enough quota to scale up the cluster.
	MaxNodeCount int32 `protobuf:"varint,3,opt,name=max_node_count,json=maxNodeCount,proto3" json:"max_node_count,omitempty"`
}

func (x *NodePoolAutoscaling) Reset() {
	*x = NodePoolAutoscaling{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodePoolAutoscaling) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodePoolAutoscaling) ProtoMessage() {}

func (x *NodePoolAutoscaling) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodePoolAutoscaling.ProtoReflect.Descriptor instead.
func (*NodePoolAutoscaling) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{50}
}

func (x *NodePoolAutoscaling) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *NodePoolAutoscaling) GetMinNodeCount() int32 {
	if x != nil {
		return x.MinNodeCount
	}
	return 0
}

func (x *NodePoolAutoscaling) GetMaxNodeCount() int32 {
	if x != nil {
		return x.MaxNodeCount
	}
	return 0
}

// SetLabelsRequest sets the Google Cloud Platform labels on a Google Container
// Engine cluster, which will in turn set them for Google Compute Engine
// resources used by that cluster
type SetLabelsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://developers.google.com/console/help/new/#projectnumber).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The labels to set for that cluster.
	ResourceLabels map[string]string `protobuf:"bytes,4,rep,name=resource_labels,json=resourceLabels,proto3" json:"resource_labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// The fingerprint of the previous set of labels for this resource,
	// used to detect conflicts. The fingerprint is initially generated by
	// Container Engine and changes after every request to modify or update
	// labels. You must always provide an up-to-date fingerprint hash when
	// updating or changing labels. Make a <code>get()</code> request to the
	// resource to get the latest fingerprint.
	LabelFingerprint string `protobuf:"bytes,5,opt,name=label_fingerprint,json=labelFingerprint,proto3" json:"label_fingerprint,omitempty"`
	// The name (project, location, cluster id) of the cluster to set labels.
	// Specified in the format 'projects/*/locations/*/clusters/*'.
	Name string `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SetLabelsRequest) Reset() {
	*x = SetLabelsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetLabelsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetLabelsRequest) ProtoMessage() {}

func (x *SetLabelsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetLabelsRequest.ProtoReflect.Descriptor instead.
func (*SetLabelsRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{51}
}

func (x *SetLabelsRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SetLabelsRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *SetLabelsRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *SetLabelsRequest) GetResourceLabels() map[string]string {
	if x != nil {
		return x.ResourceLabels
	}
	return nil
}

func (x *SetLabelsRequest) GetLabelFingerprint() string {
	if x != nil {
		return x.LabelFingerprint
	}
	return ""
}

func (x *SetLabelsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// SetLegacyAbacRequest enables or disables the ABAC authorization mechanism for
// a cluster.
type SetLegacyAbacRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster to update.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// Whether ABAC authorization will be enabled in the cluster.
	Enabled bool `protobuf:"varint,4,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// The name (project, location, cluster id) of the cluster to set legacy abac.
	// Specified in the format 'projects/*/locations/*/clusters/*'.
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SetLegacyAbacRequest) Reset() {
	*x = SetLegacyAbacRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetLegacyAbacRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetLegacyAbacRequest) ProtoMessage() {}

func (x *SetLegacyAbacRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetLegacyAbacRequest.ProtoReflect.Descriptor instead.
func (*SetLegacyAbacRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{52}
}

func (x *SetLegacyAbacRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SetLegacyAbacRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *SetLegacyAbacRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *SetLegacyAbacRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *SetLegacyAbacRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// StartIPRotationRequest creates a new IP for the cluster and then performs
// a node upgrade on each node pool to point to the new IP.
type StartIPRotationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://developers.google.com/console/help/new/#projectnumber).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The name (project, location, cluster id) of the cluster to start IP rotation.
	// Specified in the format 'projects/*/locations/*/clusters/*'.
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *StartIPRotationRequest) Reset() {
	*x = StartIPRotationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartIPRotationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartIPRotationRequest) ProtoMessage() {}

func (x *StartIPRotationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartIPRotationRequest.ProtoReflect.Descriptor instead.
func (*StartIPRotationRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{53}
}

func (x *StartIPRotationRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *StartIPRotationRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *StartIPRotationRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *StartIPRotationRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// CompleteIPRotationRequest moves the cluster master back into single-IP mode.
type CompleteIPRotationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://developers.google.com/console/help/new/#projectnumber).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The name (project, location, cluster id) of the cluster to complete IP rotation.
	// Specified in the format 'projects/*/locations/*/clusters/*'.
	Name string `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *CompleteIPRotationRequest) Reset() {
	*x = CompleteIPRotationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompleteIPRotationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteIPRotationRequest) ProtoMessage() {}

func (x *CompleteIPRotationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteIPRotationRequest.ProtoReflect.Descriptor instead.
func (*CompleteIPRotationRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{54}
}

func (x *CompleteIPRotationRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *CompleteIPRotationRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CompleteIPRotationRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *CompleteIPRotationRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// AcceleratorConfig represents a Hardware Accelerator request.
type AcceleratorConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The number of the accelerator cards exposed to an instance.
	AcceleratorCount int64 `protobuf:"varint,1,opt,name=accelerator_count,json=acceleratorCount,proto3" json:"accelerator_count,omitempty"`
	// The accelerator type resource name. List of supported accelerators
	// [here](/compute/docs/gpus/#Introduction)
	AcceleratorType string `protobuf:"bytes,2,opt,name=accelerator_type,json=acceleratorType,proto3" json:"accelerator_type,omitempty"`
}

func (x *AcceleratorConfig) Reset() {
	*x = AcceleratorConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceleratorConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceleratorConfig) ProtoMessage() {}

func (x *AcceleratorConfig) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceleratorConfig.ProtoReflect.Descriptor instead.
func (*AcceleratorConfig) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{55}
}

func (x *AcceleratorConfig) GetAcceleratorCount() int64 {
	if x != nil {
		return x.AcceleratorCount
	}
	return 0
}

func (x *AcceleratorConfig) GetAcceleratorType() string {
	if x != nil {
		return x.AcceleratorType
	}
	return ""
}

// SetNetworkPolicyRequest enables/disables network policy for a cluster.
type SetNetworkPolicyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://developers.google.com/console/help/new/#projectnumber).
	// This field is deprecated, use name instead.
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	// This field is deprecated, use name instead.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster.
	// This field is deprecated, use name instead.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// Configuration options for the NetworkPolicy feature.
	NetworkPolicy *NetworkPolicy `protobuf:"bytes,4,opt,name=network_policy,json=networkPolicy,proto3" json:"network_policy,omitempty"`
	// The name (project, location, cluster id) of the cluster to set networking
	// policy.
	// Specified in the format 'projects/*/locations/*/clusters/*'.
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SetNetworkPolicyRequest) Reset() {
	*x = SetNetworkPolicyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetNetworkPolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetNetworkPolicyRequest) ProtoMessage() {}

func (x *SetNetworkPolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetNetworkPolicyRequest.ProtoReflect.Descriptor instead.
func (*SetNetworkPolicyRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{56}
}

func (x *SetNetworkPolicyRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SetNetworkPolicyRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *SetNetworkPolicyRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *SetNetworkPolicyRequest) GetNetworkPolicy() *NetworkPolicy {
	if x != nil {
		return x.NetworkPolicy
	}
	return nil
}

func (x *SetNetworkPolicyRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// SetMaintenancePolicyRequest sets the maintenance policy for a cluster.
type SetMaintenancePolicyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Google Developers Console [project ID or project
	// number](https://support.google.com/cloud/answer/6158840).
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// The name of the Google Compute Engine
	// [zone](/compute/docs/zones#available) in which the cluster
	// resides.
	Zone string `protobuf:"bytes,2,opt,name=zone,proto3" json:"zone,omitempty"`
	// The name of the cluster to update.
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// The maintenance policy to be set for the cluster. An empty field
	// clears the existing maintenance policy.
	MaintenancePolicy *MaintenancePolicy `protobuf:"bytes,4,opt,name=maintenance_policy,json=maintenancePolicy,proto3" json:"maintenance_policy,omitempty"`
	// The name (project, location, cluster id) of the cluster to set maintenance
	// policy.
	// Specified in the format 'projects/*/locations/*/clusters/*'.
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SetMaintenancePolicyRequest) Reset() {
	*x = SetMaintenancePolicyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetMaintenancePolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetMaintenancePolicyRequest) ProtoMessage() {}

func (x *SetMaintenancePolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetMaintenancePolicyRequest.ProtoReflect.Descriptor instead.
func (*SetMaintenancePolicyRequest) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{57}
}

func (x *SetMaintenancePolicyRequest) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SetMaintenancePolicyRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *SetMaintenancePolicyRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *SetMaintenancePolicyRequest) GetMaintenancePolicy() *MaintenancePolicy {
	if x != nil {
		return x.MaintenancePolicy
	}
	return nil
}

func (x *SetMaintenancePolicyRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// CidrBlock contains an optional name and one CIDR block.
type MasterAuthorizedNetworksConfig_CidrBlock struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// display_name is an optional field for users to identify CIDR blocks.
	DisplayName string `protobuf:"bytes,1,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// cidr_block must be specified in CIDR notation.
	CidrBlock string `protobuf:"bytes,2,opt,name=cidr_block,json=cidrBlock,proto3" json:"cidr_block,omitempty"`
}

func (x *MasterAuthorizedNetworksConfig_CidrBlock) Reset() {
	*x = MasterAuthorizedNetworksConfig_CidrBlock{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MasterAuthorizedNetworksConfig_CidrBlock) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MasterAuthorizedNetworksConfig_CidrBlock) ProtoMessage() {}

func (x *MasterAuthorizedNetworksConfig_CidrBlock) ProtoReflect() protoreflect.Message {
	mi := &file_google_container_v1alpha1_cluster_service_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MasterAuthorizedNetworksConfig_CidrBlock.ProtoReflect.Descriptor instead.
func (*MasterAuthorizedNetworksConfig_CidrBlock) Descriptor() ([]byte, []int) {
	return file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP(), []int{9, 0}
}

func (x *MasterAuthorizedNetworksConfig_CidrBlock) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *MasterAuthorizedNetworksConfig_CidrBlock) GetCidrBlock() string {
	if x != nil {
		return x.CidrBlock
	}
	return ""
}

var File_google_container_v1alpha1_cluster_service_proto protoreflect.FileDescriptor

var file_google_container_v1alpha1_cluster_service_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe8, 0x05, 0x0a, 0x0a, 0x4e, 0x6f, 0x64, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x64, 0x69, 0x73,
	0x6b, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x67, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x64, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x47, 0x62, 0x12, 0x21, 0x0a, 0x0c, 0x6f,
	0x61, 0x75, 0x74, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0b, 0x6f, 0x61, 0x75, 0x74, 0x68, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x73, 0x12, 0x27,
	0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4f, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61,
	0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x49, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x73, 0x73, 0x64, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x53, 0x73, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61,
	0x67, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x20,
	0x0a, 0x0b, 0x70, 0x72, 0x65, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x62, 0x6c, 0x65,
	0x12, 0x50, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x73,
	0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x6d, 0x69, 0x6e, 0x5f, 0x63, 0x70, 0x75, 0x5f, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6d, 0x69,
	0x6e, 0x43, 0x70, 0x75, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x3c, 0x0a, 0x06,
	0x74, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x61, 0x69,
	0x6e, 0x74, 0x52, 0x06, 0x74, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x1a, 0x3b, 0x0a, 0x0d, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0xd3, 0x01, 0x0a, 0x09, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x61, 0x69, 0x6e, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x43, 0x0a, 0x06, 0x65, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c,
	0x70, 0x68, 0x61, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x61, 0x69, 0x6e, 0x74, 0x2e, 0x45,
	0x66, 0x66, 0x65, 0x63, 0x74, 0x52, 0x06, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x22, 0x59, 0x0a,
	0x06, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x12, 0x16, 0x0a, 0x12, 0x45, 0x46, 0x46, 0x45, 0x43,
	0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0f, 0x0a, 0x0b, 0x4e, 0x4f, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x10, 0x01,
	0x12, 0x16, 0x0a, 0x12, 0x50, 0x52, 0x45, 0x46, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x5f, 0x53, 0x43,
	0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x4e, 0x4f, 0x5f, 0x45,
	0x58, 0x45, 0x43, 0x55, 0x54, 0x45, 0x10, 0x03, 0x22, 0xb8, 0x02, 0x0a, 0x0a, 0x4d, 0x61, 0x73,
	0x74, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12,
	0x6e, 0x0a, 0x19, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x65, 0x72, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x17, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x65,
	0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x34, 0x0a, 0x16, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x5f, 0x63, 0x65,
	0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x18, 0x64, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x14, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x43, 0x61, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x63, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x18, 0x65, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x66, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x4b, 0x65, 0x79, 0x22, 0x53, 0x0a, 0x17, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x65, 0x72,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x38,
	0x0a, 0x18, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x63,
	0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x16, 0x69, 0x73, 0x73, 0x75, 0x65, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x65, 0x72,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x22, 0xa6, 0x03, 0x0a, 0x0c, 0x41, 0x64, 0x64,
	0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x5c, 0x0a, 0x13, 0x68, 0x74, 0x74,
	0x70, 0x5f, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x31, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x11, 0x68, 0x74, 0x74, 0x70, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x12, 0x71, 0x0a, 0x1a, 0x68, 0x6f, 0x72, 0x69, 0x7a,
	0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x6f, 0x64, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x73, 0x63,
	0x61, 0x6c, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x48, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74,
	0x61, 0x6c, 0x50, 0x6f, 0x64, 0x41, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67,
	0x52, 0x18, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x50, 0x6f, 0x64, 0x41,
	0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x12, 0x61, 0x0a, 0x14, 0x6b, 0x75,
	0x62, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x65, 0x73, 0x5f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c,
	0x70, 0x68, 0x61, 0x31, 0x2e, 0x4b, 0x75, 0x62, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x65, 0x73, 0x44,
	0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x13, 0x6b, 0x75, 0x62, 0x65, 0x72, 0x6e,
	0x65, 0x74, 0x65, 0x73, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x62, 0x0a,
	0x15, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x13, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x22, 0x2f, 0x0a, 0x11, 0x48, 0x74, 0x74, 0x70, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x22, 0x36, 0x0a, 0x18, 0x48, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c,
	0x50, 0x6f, 0x64, 0x41, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x31, 0x0a, 0x13, 0x4b, 0x75,
	0x62, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x65, 0x73, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x31, 0x0a,
	0x13, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x22, 0xef, 0x01, 0x0a, 0x1e, 0x4d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x6f,
	0x72, 0x69, 0x7a, 0x65, 0x64, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x64, 0x0a,
	0x0b, 0x63, 0x69, 0x64, 0x72, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x43, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4d,
	0x61, 0x73, 0x74, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x4e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x69,
	0x64, 0x72, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x0a, 0x63, 0x69, 0x64, 0x72, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x73, 0x1a, 0x4d, 0x0a, 0x09, 0x43, 0x69, 0x64, 0x72, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x69, 0x64, 0x72, 0x5f, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x69, 0x64, 0x72, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x22, 0xaa, 0x01, 0x0a, 0x0d, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x12, 0x4d, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x31, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x2e, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x30, 0x0a,
	0x08, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x52, 0x4f,
	0x56, 0x49, 0x44, 0x45, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x41, 0x4c, 0x49, 0x43, 0x4f, 0x10, 0x01, 0x22,
	0xb5, 0x04, 0x0a, 0x12, 0x49, 0x50, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x24, 0x0a, 0x0e, 0x75, 0x73, 0x65, 0x5f, 0x69, 0x70,
	0x5f, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x75, 0x73, 0x65, 0x49, 0x70, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x65, 0x73, 0x12, 0x2b, 0x0a, 0x11,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x75, 0x62, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x75, 0x62,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x70,
	0x76, 0x34, 0x5f, 0x63, 0x69, 0x64, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x70, 0x76, 0x34, 0x43, 0x69, 0x64, 0x72, 0x12, 0x24,
	0x0a, 0x0e, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x70, 0x76, 0x34, 0x5f, 0x63, 0x69, 0x64, 0x72,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x70, 0x76, 0x34,
	0x43, 0x69, 0x64, 0x72, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x5f, 0x69, 0x70, 0x76, 0x34, 0x5f, 0x63, 0x69, 0x64, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x49, 0x70, 0x76, 0x34, 0x43, 0x69,
	0x64, 0x72, 0x12, 0x3f, 0x0a, 0x1c, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x73, 0x65,
	0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x19, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x1d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x5f,
	0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x17, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x5f, 0x69, 0x70, 0x76, 0x34, 0x5f, 0x63, 0x69, 0x64, 0x72, 0x5f, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x49, 0x70, 0x76, 0x34, 0x43, 0x69, 0x64, 0x72, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x2f, 0x0a,
	0x14, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x70, 0x76, 0x34, 0x5f, 0x63, 0x69, 0x64, 0x72, 0x5f,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6e, 0x6f, 0x64,
	0x65, 0x49, 0x70, 0x76, 0x34, 0x43, 0x69, 0x64, 0x72, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x37,
	0x0a, 0x18, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x5f, 0x69, 0x70, 0x76, 0x34, 0x5f,
	0x63, 0x69, 0x64, 0x72, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x15, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x49, 0x70, 0x76, 0x34, 0x43, 0x69,
	0x64, 0x72, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x22, 0x33, 0x0a, 0x17, 0x50, 0x6f, 0x64, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x85, 0x0f, 0x0a,
	0x07, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c,
	0x0a, 0x12, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x46, 0x0a, 0x0b,
	0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4e, 0x6f,
	0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x46, 0x0a, 0x0b, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x61,
	0x75, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61,
	0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68,
	0x52, 0x0a, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x12, 0x27, 0x0a, 0x0f,
	0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x2a,
	0x0a, 0x11, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x70, 0x76, 0x34, 0x5f, 0x63,
	0x69, 0x64, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x49, 0x70, 0x76, 0x34, 0x43, 0x69, 0x64, 0x72, 0x12, 0x4c, 0x0a, 0x0d, 0x61, 0x64,
	0x64, 0x6f, 0x6e, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x41, 0x64,
	0x64, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x61, 0x64, 0x64, 0x6f,
	0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x75,
	0x62, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x42, 0x0a, 0x0a, 0x6e, 0x6f, 0x64, 0x65,
	0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f,
	0x6c, 0x52, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x73, 0x12, 0x1c, 0x0a, 0x09,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x09, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6b, 0x75, 0x62, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x65, 0x73, 0x5f,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x4b, 0x75, 0x62, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x65, 0x73, 0x41, 0x6c, 0x70,
	0x68, 0x61, 0x12, 0x4f, 0x0a, 0x0e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x70, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x52, 0x0d, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x12, 0x5f, 0x0a, 0x14, 0x69, 0x70, 0x5f, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x49, 0x50,
	0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x52, 0x12, 0x69, 0x70, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x12, 0x84, 0x01, 0x0a, 0x21, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x5f,
	0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x39, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4d, 0x61, 0x73,
	0x74, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x1e, 0x6d, 0x61, 0x73,
	0x74, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x5b, 0x0a, 0x12, 0x6d,
	0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x31, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x11, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x63, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x6f, 0x0a, 0x1a, 0x70, 0x6f, 0x64, 0x5f,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x50, 0x6f, 0x64, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x17, 0x70, 0x6f, 0x64, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x6c,
	0x66, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x64, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65,
	0x6c, 0x66, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x65,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x66, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x36, 0x0a, 0x17, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61,
	0x6c, 0x5f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x67, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c,
	0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x34,
	0x0a, 0x16, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x68, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x69, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x6a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x6b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x31, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x6c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x2d, 0x0a, 0x13, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x70, 0x76, 0x34, 0x5f, 0x63,
	0x69, 0x64, 0x72, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x6d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10,
	0x6e, 0x6f, 0x64, 0x65, 0x49, 0x70, 0x76, 0x34, 0x43, 0x69, 0x64, 0x72, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x2c, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x5f, 0x69, 0x70, 0x76,
	0x34, 0x5f, 0x63, 0x69, 0x64, 0x72, 0x18, 0x6e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x49, 0x70, 0x76, 0x34, 0x43, 0x69, 0x64, 0x72, 0x12, 0x2e,
	0x0a, 0x13, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x6f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x2c,
	0x0a, 0x12, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x70, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x71, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x72, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x69, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x50,
	0x52, 0x4f, 0x56, 0x49, 0x53, 0x49, 0x4f, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0b, 0x0a,
	0x07, 0x52, 0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x45,
	0x43, 0x4f, 0x4e, 0x43, 0x49, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x53,
	0x54, 0x4f, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x52, 0x52,
	0x4f, 0x52, 0x10, 0x05, 0x22, 0xa7, 0x06, 0x0a, 0x0d, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65,
	0x64, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x4e, 0x6f, 0x64,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x1a, 0x64, 0x65, 0x73, 0x69,
	0x72, 0x65, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x64, 0x65,
	0x73, 0x69, 0x72, 0x65, 0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5b, 0x0a, 0x15, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65,
	0x64, 0x5f, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x31, 0x2e, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x13,
	0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x2f, 0x0a, 0x14, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x6e,
	0x6f, 0x64, 0x65, 0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f,
	0x6f, 0x6c, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x5f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x71, 0x0a, 0x1d, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x6e, 0x6f,
	0x64, 0x65, 0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c,
	0x69, 0x6e, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61,
	0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x41, 0x75,
	0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x1a, 0x64, 0x65, 0x73, 0x69, 0x72,
	0x65, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x41, 0x75, 0x74, 0x6f, 0x73, 0x63,
	0x61, 0x6c, 0x69, 0x6e, 0x67, 0x12, 0x2b, 0x0a, 0x11, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64,
	0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x10, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x93, 0x01, 0x0a, 0x29, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x6d,
	0x61, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64,
	0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x31, 0x2e, 0x4d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69,
	0x7a, 0x65, 0x64, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x25, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x4d, 0x61, 0x73, 0x74, 0x65, 0x72,
	0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x64, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x7e, 0x0a, 0x22, 0x64, 0x65, 0x73, 0x69,
	0x72, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x64, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31,
	0x2e, 0x50, 0x6f, 0x64, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x1e, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65,
	0x64, 0x50, 0x6f, 0x64, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x34, 0x0a, 0x16, 0x64, 0x65, 0x73, 0x69,
	0x72, 0x65, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x64, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65,
	0x64, 0x4d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xf1,
	0x06, 0x0a, 0x09, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x50, 0x0a, 0x0e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65,
	0x6c, 0x66, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73,
	0x65, 0x6c, 0x66, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x52,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0b, 0x0a,
	0x07, 0x52, 0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x4f,
	0x4e, 0x45, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x42, 0x4f, 0x52, 0x54, 0x49, 0x4e, 0x47,
	0x10, 0x04, 0x22, 0xfd, 0x02, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x4c, 0x55, 0x53,
	0x54, 0x45, 0x52, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f,
	0x43, 0x4c, 0x55, 0x53, 0x54, 0x45, 0x52, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x55, 0x50, 0x47,
	0x52, 0x41, 0x44, 0x45, 0x5f, 0x4d, 0x41, 0x53, 0x54, 0x45, 0x52, 0x10, 0x03, 0x12, 0x11, 0x0a,
	0x0d, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x4e, 0x4f, 0x44, 0x45, 0x53, 0x10, 0x04,
	0x12, 0x12, 0x0a, 0x0e, 0x52, 0x45, 0x50, 0x41, 0x49, 0x52, 0x5f, 0x43, 0x4c, 0x55, 0x53, 0x54,
	0x45, 0x52, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x43,
	0x4c, 0x55, 0x53, 0x54, 0x45, 0x52, 0x10, 0x06, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x45, 0x5f, 0x4e, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x4f, 0x4f, 0x4c, 0x10, 0x07, 0x12, 0x14,
	0x0a, 0x10, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x4e, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x4f,
	0x4f, 0x4c, 0x10, 0x08, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x45, 0x54, 0x5f, 0x4e, 0x4f, 0x44, 0x45,
	0x5f, 0x50, 0x4f, 0x4f, 0x4c, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45, 0x4d, 0x45, 0x4e, 0x54,
	0x10, 0x09, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x52, 0x45, 0x50, 0x41, 0x49,
	0x52, 0x5f, 0x4e, 0x4f, 0x44, 0x45, 0x53, 0x10, 0x0a, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x55, 0x54,
	0x4f, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x4e, 0x4f, 0x44, 0x45, 0x53, 0x10,
	0x0b, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x45, 0x54, 0x5f, 0x4c, 0x41, 0x42, 0x45, 0x4c, 0x53, 0x10,
	0x0c, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x45, 0x54, 0x5f, 0x4d, 0x41, 0x53, 0x54, 0x45, 0x52, 0x5f,
	0x41, 0x55, 0x54, 0x48, 0x10, 0x0d, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x45, 0x54, 0x5f, 0x4e, 0x4f,
	0x44, 0x45, 0x5f, 0x50, 0x4f, 0x4f, 0x4c, 0x5f, 0x53, 0x49, 0x5a, 0x45, 0x10, 0x0e, 0x12, 0x16,
	0x0a, 0x12, 0x53, 0x45, 0x54, 0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x50, 0x4f,
	0x4c, 0x49, 0x43, 0x59, 0x10, 0x0f, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x45, 0x54, 0x5f, 0x4d, 0x41,
	0x49, 0x4e, 0x54, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59,
	0x10, 0x10, 0x22, 0x9f, 0x01, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x3c,
	0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x43, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x22, 0x79, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0xbe, 0x01, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x06, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0xe1, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x50,
	0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c,
	0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x6f, 0x64, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0xf9, 0x01, 0x0a, 0x1d, 0x53, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65,
	0x50, 0x6f, 0x6f, 0x6c, 0x41, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x6f, 0x64, 0x65,
	0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x50, 0x0a, 0x0b, 0x61, 0x75,
	0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65,
	0x50, 0x6f, 0x6f, 0x6c, 0x41, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x52,
	0x0b, 0x61, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0xa9, 0x01, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x27, 0x0a, 0x0f, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e,
	0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xb2, 0x01, 0x0a,
	0x1b, 0x53, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a,
	0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2d,
	0x0a, 0x12, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x22, 0xcc, 0x01, 0x0a, 0x16, 0x53, 0x65, 0x74, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a,
	0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x4c,
	0x0a, 0x0d, 0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x31, 0x2e, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c,
	0x61, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0x99, 0x01, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xa2, 0x01, 0x0a,
	0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x22, 0xdd, 0x02, 0x0a, 0x14, 0x53, 0x65, 0x74, 0x4d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x41,
	0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x4e, 0x0a, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4d, 0x61, 0x73, 0x74,
	0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x06,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x41,
	0x75, 0x74, 0x68, 0x52, 0x06, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0x50, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x45, 0x54, 0x5f, 0x50, 0x41,
	0x53, 0x53, 0x57, 0x4f, 0x52, 0x44, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x47, 0x45, 0x4e, 0x45,
	0x52, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x57, 0x4f, 0x52, 0x44, 0x10, 0x02, 0x12,
	0x10, 0x0a, 0x0c, 0x53, 0x45, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x4e, 0x41, 0x4d, 0x45, 0x10,
	0x03, 0x22, 0x7c, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0x60, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x22, 0x7b, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a, 0x08, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52,
	0x08, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0c, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5a, 0x6f, 0x6e, 0x65, 0x73, 0x22, 0x7f,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0x62, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x22, 0x82, 0x01, 0x0a, 0x16, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x83, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0c, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5a, 0x6f, 0x6e, 0x65, 0x73, 0x22, 0x5f,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0x84, 0x02, 0x0a, 0x0c, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x36, 0x0a, 0x17, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x15, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x4e, 0x6f, 0x64, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x12, 0x32, 0x0a, 0x15, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x74,
	0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x13, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x4d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xc3, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a,
	0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x40, 0x0a, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65,
	0x50, 0x6f, 0x6f, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x22, 0x9f, 0x01, 0x0a,
	0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x6f, 0x64, 0x65,
	0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x80,
	0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x22, 0x9c, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x6f,
	0x64, 0x65, 0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0xfe, 0x04, 0x0a, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x3d, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4e, 0x6f,
	0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x2c, 0x0a, 0x12, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x64, 0x65,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x50,
	0x0a, 0x0b, 0x61, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e,
	0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x41, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c,
	0x69, 0x6e, 0x67, 0x52, 0x0b, 0x61, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67,
	0x12, 0x49, 0x0a, 0x0a, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31,
	0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x0a, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x73,
	0x65, 0x6c, 0x66, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x64, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x73, 0x65, 0x6c, 0x66, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x65, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x66, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x72,
	0x6c, 0x73, 0x12, 0x42, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x67, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x68, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x81, 0x01,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x10, 0x0a, 0x0c, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x53, 0x49, 0x4f, 0x4e, 0x49, 0x4e, 0x47,
	0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12,
	0x16, 0x0a, 0x12, 0x52, 0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x45, 0x43, 0x4f, 0x4e,
	0x43, 0x49, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x54, 0x4f, 0x50,
	0x50, 0x49, 0x4e, 0x47, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10,
	0x06, 0x22, 0xac, 0x01, 0x0a, 0x0e, 0x4e, 0x6f, 0x64, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x75, 0x70, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x61, 0x75, 0x74, 0x6f,
	0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x72, 0x65, 0x70, 0x61, 0x69, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x61, 0x75,
	0x74, 0x6f, 0x52, 0x65, 0x70, 0x61, 0x69, 0x72, 0x12, 0x56, 0x0a, 0x0f, 0x75, 0x70, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x41, 0x75,
	0x74, 0x6f, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x0e, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x22, 0x6d, 0x0a, 0x12, 0x41, 0x75, 0x74, 0x6f, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x35, 0x0a, 0x17, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x75,
	0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x61, 0x75, 0x74, 0x6f, 0x55, 0x70, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x59, 0x0a, 0x11, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x12, 0x44, 0x0a, 0x06, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31,
	0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x57, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x52, 0x06, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x22, 0x8c, 0x01, 0x0a, 0x11, 0x4d,
	0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77,
	0x12, 0x6d, 0x0a, 0x18, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x31, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x44,
	0x61, 0x69, 0x6c, 0x79, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x57,
	0x69, 0x6e, 0x64, 0x6f, 0x77, 0x48, 0x00, 0x52, 0x16, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x4d, 0x61,
	0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x42,
	0x08, 0x0a, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x22, 0x53, 0x0a, 0x16, 0x44, 0x61, 0x69,
	0x6c, 0x79, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x57, 0x69, 0x6e,
	0x64, 0x6f, 0x77, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xf1,
	0x01, 0x0a, 0x1c, 0x53, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x4d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f,
	0x6c, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x0a, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x0a, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0xbf, 0x01, 0x0a, 0x16, 0x53, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f,
	0x6f, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x20, 0x0a, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0xa8, 0x01, 0x0a, 0x1e, 0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x6f, 0x64,
	0x65, 0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0x5b, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x0a, 0x6e, 0x6f, 0x64, 0x65,
	0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f,
	0x6c, 0x52, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x73, 0x22, 0x7b, 0x0a, 0x13,
	0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x41, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c,
	0x69, 0x6e, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x24, 0x0a,
	0x0e, 0x6d, 0x69, 0x6e, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6d, 0x69, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x61, 0x78, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6d, 0x61, 0x78,
	0x4e, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xd2, 0x02, 0x0a, 0x10, 0x53, 0x65,
	0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x68, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61,
	0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x5f, 0x66, 0x69, 0x6e, 0x67, 0x65, 0x72, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x46, 0x69, 0x6e, 0x67,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x1a, 0x41, 0x0a, 0x13, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x96,
	0x01, 0x0a, 0x14, 0x53, 0x65, 0x74, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x41, 0x62, 0x61, 0x63,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x7e, 0x0a, 0x16, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x49, 0x50, 0x52, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x81, 0x01, 0x0a, 0x19, 0x43, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x65, 0x49, 0x50, 0x52, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x6b, 0x0a, 0x11, 0x41,
	0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x2b, 0x0a, 0x11, 0x61, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x61, 0x63, 0x63,
	0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x29, 0x0a,
	0x10, 0x61, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x63, 0x63, 0x65, 0x6c, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0xd0, 0x01, 0x0a, 0x17, 0x53, 0x65, 0x74,
	0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x4f, 0x0a, 0x0e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x0d, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xe0, 0x01, 0x0a, 0x1b,
	0x53, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x5b, 0x0a,
	0x12, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61,
	0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63,
	0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x11, 0x6d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x63, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x32, 0x84,
	0x2b, 0x0a, 0x0e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x12, 0xab, 0x01, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x73, 0x12, 0x2e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x3a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x34, 0x12, 0x32, 0x2f, 0x76, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x12,
	0x9a, 0x01, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x2c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x22, 0x3a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x34, 0x12, 0x32, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a,
	0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x12, 0xa5, 0x01, 0x0a,
	0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x2f,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x3d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x37, 0x22, 0x32, 0x2f,
	0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x73, 0x3a, 0x01, 0x2a, 0x12, 0xa5, 0x01, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x2f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x3d, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x37, 0x1a, 0x32, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f,
	0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x01, 0x2a, 0x12, 0xb3, 0x01, 0x0a,
	0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x12,
	0x30, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x49, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x43, 0x1a,
	0x3e, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65,
	0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73,
	0x2f, 0x2a, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x2a, 0x7d, 0x3a,
	0x01, 0x2a, 0x12, 0xd2, 0x01, 0x0a, 0x16, 0x53, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f,
	0x6f, 0x6c, 0x41, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67, 0x12, 0x38, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4e, 0x6f, 0x64,
	0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x41, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61, 0x6c, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x58, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x52, 0x22, 0x4d, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f,
	0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f,
	0x6c, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x73, 0x65, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x73, 0x63, 0x61,
	0x6c, 0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0xb8, 0x01, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x4c,
	0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x33, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4c, 0x6f, 0x67,
	0x67, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x48, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x42,
	0x22, 0x3d, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d,
	0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x73, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x3a,
	0x01, 0x2a, 0x12, 0xc1, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x36, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4b, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x45, 0x22, 0x40, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x6e, 0x61,
	0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x73, 0x65, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0xb3, 0x01, 0x0a, 0x0f, 0x53, 0x65, 0x74, 0x41, 0x64,
	0x64, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x31, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x73,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x47, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x41, 0x22, 0x3c, 0x2f, 0x76, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x3a,
	0x73, 0x65, 0x74, 0x41, 0x64, 0x64, 0x6f, 0x6e, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0xb0, 0x01, 0x0a,
	0x0c, 0x53, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2e, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x4a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x44, 0x22, 0x3f, 0x2f, 0x76, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x3a,
	0x73, 0x65, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x01, 0x2a, 0x12,
	0xb0, 0x01, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x74, 0x65, 0x72,
	0x12, 0x2e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x44, 0x22, 0x3f,
	0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f,
	0x2a, 0x7d, 0x3a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x3a,
	0x01, 0x2a, 0x12, 0xb3, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x4d, 0x61, 0x73, 0x74, 0x65, 0x72,
	0x41, 0x75, 0x74, 0x68, 0x12, 0x2f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31,
	0x2e, 0x53, 0x65, 0x74, 0x4d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4b, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x45, 0x22, 0x40, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b,
	0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x73, 0x65, 0x74, 0x4d, 0x61, 0x73, 0x74, 0x65,
	0x72, 0x41, 0x75, 0x74, 0x68, 0x3a, 0x01, 0x2a, 0x12, 0xa2, 0x01, 0x0a, 0x0d, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x2f, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x3a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x34, 0x2a, 0x32, 0x2f, 0x76, 0x31, 0x61, 0x6c,
	0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x2a, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x12, 0xb3, 0x01,
	0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x30, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x31, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x36, 0x12, 0x34, 0x2f,
	0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0xa2, 0x01, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31,
	0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x3c, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x36, 0x12, 0x34, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x6e,
	0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x12, 0xa4, 0x01, 0x0a, 0x0f, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x46, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x40, 0x22,
	0x3b, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65,
	0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x3a, 0x01, 0x2a, 0x12,
	0xab, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x31, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22,
	0x3c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x36, 0x12, 0x34, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0xba, 0x01,
	0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x73, 0x12,
	0x2f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x30, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x46, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x40, 0x12, 0x3e, 0x2f, 0x76, 0x31, 0x61,
	0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d,
	0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x73, 0x12, 0xa9, 0x01, 0x0a, 0x0b, 0x47,
	0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x12, 0x2d, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f,
	0x6f, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61,
	0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x22, 0x46,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x40, 0x12, 0x3e, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73,
	0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f,
	0x6f, 0x6c, 0x73, 0x2f, 0x2a, 0x7d, 0x12, 0xb3, 0x01, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x12, 0x30, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61,
	0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65,
	0x50, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x49, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x43, 0x22, 0x3e, 0x2f, 0x76, 0x31, 0x61, 0x6c,
	0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x2f,
	0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0xb0, 0x01, 0x0a,
	0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x12,
	0x30, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x46, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x40, 0x2a,
	0x3e, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65,
	0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73,
	0x2f, 0x2a, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x2a, 0x7d, 0x12,
	0xce, 0x01, 0x0a, 0x17, 0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x4e, 0x6f, 0x64, 0x65,
	0x50, 0x6f, 0x6f, 0x6c, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x12, 0x39, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b,
	0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x52, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x4c, 0x22, 0x47, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f,
	0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a,
	0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c,
	0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x72, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x3a, 0x01, 0x2a,
	0x12, 0xcf, 0x01, 0x0a, 0x15, 0x53, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f,
	0x6f, 0x6c, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x57, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x51, 0x22, 0x4c, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x6e, 0x61,
	0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x2a,
	0x7d, 0x3a, 0x73, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x3a,
	0x01, 0x2a, 0x12, 0xaf, 0x01, 0x0a, 0x09, 0x53, 0x65, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x12, 0x2b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x53, 0x65, 0x74,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x4f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x49, 0x22, 0x44, 0x2f, 0x76, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x3a,
	0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x3a, 0x01, 0x2a, 0x12, 0xb3, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x4c, 0x65, 0x67, 0x61,
	0x63, 0x79, 0x41, 0x62, 0x61, 0x63, 0x12, 0x2f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4c, 0x65, 0x67, 0x61, 0x63, 0x79, 0x41, 0x62, 0x61, 0x63,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4b, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x45, 0x22, 0x40, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f,
	0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x73, 0x65, 0x74, 0x4c, 0x65, 0x67,
	0x61, 0x63, 0x79, 0x41, 0x62, 0x61, 0x63, 0x3a, 0x01, 0x2a, 0x12, 0xb9, 0x01, 0x0a, 0x0f, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x49, 0x50, 0x52, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x31,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x49, 0x50, 0x52, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x47, 0x22,
	0x42, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65,
	0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73,
	0x2f, 0x2a, 0x7d, 0x3a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x49, 0x70, 0x52, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x3a, 0x01, 0x2a, 0x12, 0xc2, 0x01, 0x0a, 0x12, 0x43, 0x6f, 0x6d, 0x70, 0x6c,
	0x65, 0x74, 0x65, 0x49, 0x50, 0x52, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x49, 0x50, 0x52, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x50, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x4a, 0x22, 0x45, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x6e, 0x61,
	0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x70,
	0x52, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x3a, 0x01, 0x2a, 0x12, 0xbd, 0x01, 0x0a, 0x0f,
	0x53, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x31, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4e,
	0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x51, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x4b,
	0x22, 0x46, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d,
	0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x73, 0x2f, 0x2a, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x73, 0x2f, 0x2a, 0x7d,
	0x3a, 0x73, 0x65, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0xbc, 0x01, 0x0a, 0x10,
	0x53, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x12, 0x32, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x53, 0x65, 0x74,
	0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31,
	0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4e, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x48, 0x22, 0x43, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x6e,
	0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x73, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x3a, 0x01, 0x2a, 0x12, 0xc8, 0x01, 0x0a, 0x14, 0x53,
	0x65, 0x74, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x12, 0x36, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e,
	0x53, 0x65, 0x74, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x52, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x4c, 0x22, 0x47, 0x2f, 0x76, 0x31, 0x61, 0x6c,
	0x70, 0x68, 0x61, 0x31, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x2a, 0x2f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x73, 0x65,
	0x74, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x3a, 0x01, 0x2a, 0x42, 0xbe, 0x01, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x42, 0x13, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x42,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72,
	0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2f,
	0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x3b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0xaa, 0x02, 0x1f, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x43, 0x6c, 0x6f, 0x75,
	0x64, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x56, 0x31, 0x41, 0x6c,
	0x70, 0x68, 0x61, 0x31, 0xca, 0x02, 0x1f, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x43, 0x6c,
	0x6f, 0x75, 0x64, 0x5c, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5c, 0x56, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_container_v1alpha1_cluster_service_proto_rawDescOnce sync.Once
	file_google_container_v1alpha1_cluster_service_proto_rawDescData = file_google_container_v1alpha1_cluster_service_proto_rawDesc
)

func file_google_container_v1alpha1_cluster_service_proto_rawDescGZIP() []byte {
	file_google_container_v1alpha1_cluster_service_proto_rawDescOnce.Do(func() {
		file_google_container_v1alpha1_cluster_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_container_v1alpha1_cluster_service_proto_rawDescData)
	})
	return file_google_container_v1alpha1_cluster_service_proto_rawDescData
}

var file_google_container_v1alpha1_cluster_service_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_google_container_v1alpha1_cluster_service_proto_msgTypes = make([]protoimpl.MessageInfo, 62)
var file_google_container_v1alpha1_cluster_service_proto_goTypes = []interface{}{
	(NodeTaint_Effect)(0),                            // 0: google.container.v1alpha1.NodeTaint.Effect
	(NetworkPolicy_Provider)(0),                      // 1: google.container.v1alpha1.NetworkPolicy.Provider
	(Cluster_Status)(0),                              // 2: google.container.v1alpha1.Cluster.Status
	(Operation_Status)(0),                            // 3: google.container.v1alpha1.Operation.Status
	(Operation_Type)(0),                              // 4: google.container.v1alpha1.Operation.Type
	(SetMasterAuthRequest_Action)(0),                 // 5: google.container.v1alpha1.SetMasterAuthRequest.Action
	(NodePool_Status)(0),                             // 6: google.container.v1alpha1.NodePool.Status
	(*NodeConfig)(nil),                               // 7: google.container.v1alpha1.NodeConfig
	(*NodeTaint)(nil),                                // 8: google.container.v1alpha1.NodeTaint
	(*MasterAuth)(nil),                               // 9: google.container.v1alpha1.MasterAuth
	(*ClientCertificateConfig)(nil),                  // 10: google.container.v1alpha1.ClientCertificateConfig
	(*AddonsConfig)(nil),                             // 11: google.container.v1alpha1.AddonsConfig
	(*HttpLoadBalancing)(nil),                        // 12: google.container.v1alpha1.HttpLoadBalancing
	(*HorizontalPodAutoscaling)(nil),                 // 13: google.container.v1alpha1.HorizontalPodAutoscaling
	(*KubernetesDashboard)(nil),                      // 14: google.container.v1alpha1.KubernetesDashboard
	(*NetworkPolicyConfig)(nil),                      // 15: google.container.v1alpha1.NetworkPolicyConfig
	(*MasterAuthorizedNetworksConfig)(nil),           // 16: google.container.v1alpha1.MasterAuthorizedNetworksConfig
	(*NetworkPolicy)(nil),                            // 17: google.container.v1alpha1.NetworkPolicy
	(*IPAllocationPolicy)(nil),                       // 18: google.container.v1alpha1.IPAllocationPolicy
	(*PodSecurityPolicyConfig)(nil),                  // 19: google.container.v1alpha1.PodSecurityPolicyConfig
	(*Cluster)(nil),                                  // 20: google.container.v1alpha1.Cluster
	(*ClusterUpdate)(nil),                            // 21: google.container.v1alpha1.ClusterUpdate
	(*Operation)(nil),                                // 22: google.container.v1alpha1.Operation
	(*CreateClusterRequest)(nil),                     // 23: google.container.v1alpha1.CreateClusterRequest
	(*GetClusterRequest)(nil),                        // 24: google.container.v1alpha1.GetClusterRequest
	(*UpdateClusterRequest)(nil),                     // 25: google.container.v1alpha1.UpdateClusterRequest
	(*UpdateNodePoolRequest)(nil),                    // 26: google.container.v1alpha1.UpdateNodePoolRequest
	(*SetNodePoolAutoscalingRequest)(nil),            // 27: google.container.v1alpha1.SetNodePoolAutoscalingRequest
	(*SetLoggingServiceRequest)(nil),                 // 28: google.container.v1alpha1.SetLoggingServiceRequest
	(*SetMonitoringServiceRequest)(nil),              // 29: google.container.v1alpha1.SetMonitoringServiceRequest
	(*SetAddonsConfigRequest)(nil),                   // 30: google.container.v1alpha1.SetAddonsConfigRequest
	(*SetLocationsRequest)(nil),                      // 31: google.container.v1alpha1.SetLocationsRequest
	(*UpdateMasterRequest)(nil),                      // 32: google.container.v1alpha1.UpdateMasterRequest
	(*SetMasterAuthRequest)(nil),                     // 33: google.container.v1alpha1.SetMasterAuthRequest
	(*DeleteClusterRequest)(nil),                     // 34: google.container.v1alpha1.DeleteClusterRequest
	(*ListClustersRequest)(nil),                      // 35: google.container.v1alpha1.ListClustersRequest
	(*ListClustersResponse)(nil),                     // 36: google.container.v1alpha1.ListClustersResponse
	(*GetOperationRequest)(nil),                      // 37: google.container.v1alpha1.GetOperationRequest
	(*ListOperationsRequest)(nil),                    // 38: google.container.v1alpha1.ListOperationsRequest
	(*CancelOperationRequest)(nil),                   // 39: google.container.v1alpha1.CancelOperationRequest
	(*ListOperationsResponse)(nil),                   // 40: google.container.v1alpha1.ListOperationsResponse
	(*GetServerConfigRequest)(nil),                   // 41: google.container.v1alpha1.GetServerConfigRequest
	(*ServerConfig)(nil),                             // 42: google.container.v1alpha1.ServerConfig
	(*CreateNodePoolRequest)(nil),                    // 43: google.container.v1alpha1.CreateNodePoolRequest
	(*DeleteNodePoolRequest)(nil),                    // 44: google.container.v1alpha1.DeleteNodePoolRequest
	(*ListNodePoolsRequest)(nil),                     // 45: google.container.v1alpha1.ListNodePoolsRequest
	(*GetNodePoolRequest)(nil),                       // 46: google.container.v1alpha1.GetNodePoolRequest
	(*NodePool)(nil),                                 // 47: google.container.v1alpha1.NodePool
	(*NodeManagement)(nil),                           // 48: google.container.v1alpha1.NodeManagement
	(*AutoUpgradeOptions)(nil),                       // 49: google.container.v1alpha1.AutoUpgradeOptions
	(*MaintenancePolicy)(nil),                        // 50: google.container.v1alpha1.MaintenancePolicy
	(*MaintenanceWindow)(nil),                        // 51: google.container.v1alpha1.MaintenanceWindow
	(*DailyMaintenanceWindow)(nil),                   // 52: google.container.v1alpha1.DailyMaintenanceWindow
	(*SetNodePoolManagementRequest)(nil),             // 53: google.container.v1alpha1.SetNodePoolManagementRequest
	(*SetNodePoolSizeRequest)(nil),                   // 54: google.container.v1alpha1.SetNodePoolSizeRequest
	(*RollbackNodePoolUpgradeRequest)(nil),           // 55: google.container.v1alpha1.RollbackNodePoolUpgradeRequest
	(*ListNodePoolsResponse)(nil),                    // 56: google.container.v1alpha1.ListNodePoolsResponse
	(*NodePoolAutoscaling)(nil),                      // 57: google.container.v1alpha1.NodePoolAutoscaling
	(*SetLabelsRequest)(nil),                         // 58: google.container.v1alpha1.SetLabelsRequest
	(*SetLegacyAbacRequest)(nil),                     // 59: google.container.v1alpha1.SetLegacyAbacRequest
	(*StartIPRotationRequest)(nil),                   // 60: google.container.v1alpha1.StartIPRotationRequest
	(*CompleteIPRotationRequest)(nil),                // 61: google.container.v1alpha1.CompleteIPRotationRequest
	(*AcceleratorConfig)(nil),                        // 62: google.container.v1alpha1.AcceleratorConfig
	(*SetNetworkPolicyRequest)(nil),                  // 63: google.container.v1alpha1.SetNetworkPolicyRequest
	(*SetMaintenancePolicyRequest)(nil),              // 64: google.container.v1alpha1.SetMaintenancePolicyRequest
	nil,                                              // 65: google.container.v1alpha1.NodeConfig.MetadataEntry
	nil,                                              // 66: google.container.v1alpha1.NodeConfig.LabelsEntry
	(*MasterAuthorizedNetworksConfig_CidrBlock)(nil), // 67: google.container.v1alpha1.MasterAuthorizedNetworksConfig.CidrBlock
	nil,                   // 68: google.container.v1alpha1.SetLabelsRequest.ResourceLabelsEntry
	(*emptypb.Empty)(nil), // 69: google.protobuf.Empty
}
var file_google_container_v1alpha1_cluster_service_proto_depIdxs = []int32{
	65, // 0: google.container.v1alpha1.NodeConfig.metadata:type_name -> google.container.v1alpha1.NodeConfig.MetadataEntry
	66, // 1: google.container.v1alpha1.NodeConfig.labels:type_name -> google.container.v1alpha1.NodeConfig.LabelsEntry
	62, // 2: google.container.v1alpha1.NodeConfig.accelerators:type_name -> google.container.v1alpha1.AcceleratorConfig
	8,  // 3: google.container.v1alpha1.NodeConfig.taints:type_name -> google.container.v1alpha1.NodeTaint
	0,  // 4: google.container.v1alpha1.NodeTaint.effect:type_name -> google.container.v1alpha1.NodeTaint.Effect
	10, // 5: google.container.v1alpha1.MasterAuth.client_certificate_config:type_name -> google.container.v1alpha1.ClientCertificateConfig
	12, // 6: google.container.v1alpha1.AddonsConfig.http_load_balancing:type_name -> google.container.v1alpha1.HttpLoadBalancing
	13, // 7: google.container.v1alpha1.AddonsConfig.horizontal_pod_autoscaling:type_name -> google.container.v1alpha1.HorizontalPodAutoscaling
	14, // 8: google.container.v1alpha1.AddonsConfig.kubernetes_dashboard:type_name -> google.container.v1alpha1.KubernetesDashboard
	15, // 9: google.container.v1alpha1.AddonsConfig.network_policy_config:type_name -> google.container.v1alpha1.NetworkPolicyConfig
	67, // 10: google.container.v1alpha1.MasterAuthorizedNetworksConfig.cidr_blocks:type_name -> google.container.v1alpha1.MasterAuthorizedNetworksConfig.CidrBlock
	1,  // 11: google.container.v1alpha1.NetworkPolicy.provider:type_name -> google.container.v1alpha1.NetworkPolicy.Provider
	7,  // 12: google.container.v1alpha1.Cluster.node_config:type_name -> google.container.v1alpha1.NodeConfig
	9,  // 13: google.container.v1alpha1.Cluster.master_auth:type_name -> google.container.v1alpha1.MasterAuth
	11, // 14: google.container.v1alpha1.Cluster.addons_config:type_name -> google.container.v1alpha1.AddonsConfig
	47, // 15: google.container.v1alpha1.Cluster.node_pools:type_name -> google.container.v1alpha1.NodePool
	17, // 16: google.container.v1alpha1.Cluster.network_policy:type_name -> google.container.v1alpha1.NetworkPolicy
	18, // 17: google.container.v1alpha1.Cluster.ip_allocation_policy:type_name -> google.container.v1alpha1.IPAllocationPolicy
	16, // 18: google.container.v1alpha1.Cluster.master_authorized_networks_config:type_name -> google.container.v1alpha1.MasterAuthorizedNetworksConfig
	50, // 19: google.container.v1alpha1.Cluster.maintenance_policy:type_name -> google.container.v1alpha1.MaintenancePolicy
	19, // 20: google.container.v1alpha1.Cluster.pod_security_policy_config:type_name -> google.container.v1alpha1.PodSecurityPolicyConfig
	2,  // 21: google.container.v1alpha1.Cluster.status:type_name -> google.container.v1alpha1.Cluster.Status
	11, // 22: google.container.v1alpha1.ClusterUpdate.desired_addons_config:type_name -> google.container.v1alpha1.AddonsConfig
	57, // 23: google.container.v1alpha1.ClusterUpdate.desired_node_pool_autoscaling:type_name -> google.container.v1alpha1.NodePoolAutoscaling
	16, // 24: google.container.v1alpha1.ClusterUpdate.desired_master_authorized_networks_config:type_name -> google.container.v1alpha1.MasterAuthorizedNetworksConfig
	19, // 25: google.container.v1alpha1.ClusterUpdate.desired_pod_security_policy_config:type_name -> google.container.v1alpha1.PodSecurityPolicyConfig
	4,  // 26: google.container.v1alpha1.Operation.operation_type:type_name -> google.container.v1alpha1.Operation.Type
	3,  // 27: google.container.v1alpha1.Operation.status:type_name -> google.container.v1alpha1.Operation.Status
	20, // 28: google.container.v1alpha1.CreateClusterRequest.cluster:type_name -> google.container.v1alpha1.Cluster
	21, // 29: google.container.v1alpha1.UpdateClusterRequest.update:type_name -> google.container.v1alpha1.ClusterUpdate
	57, // 30: google.container.v1alpha1.SetNodePoolAutoscalingRequest.autoscaling:type_name -> google.container.v1alpha1.NodePoolAutoscaling
	11, // 31: google.container.v1alpha1.SetAddonsConfigRequest.addons_config:type_name -> google.container.v1alpha1.AddonsConfig
	5,  // 32: google.container.v1alpha1.SetMasterAuthRequest.action:type_name -> google.container.v1alpha1.SetMasterAuthRequest.Action
	9,  // 33: google.container.v1alpha1.SetMasterAuthRequest.update:type_name -> google.container.v1alpha1.MasterAuth
	20, // 34: google.container.v1alpha1.ListClustersResponse.clusters:type_name -> google.container.v1alpha1.Cluster
	22, // 35: google.container.v1alpha1.ListOperationsResponse.operations:type_name -> google.container.v1alpha1.Operation
	47, // 36: google.container.v1alpha1.CreateNodePoolRequest.node_pool:type_name -> google.container.v1alpha1.NodePool
	7,  // 37: google.container.v1alpha1.NodePool.config:type_name -> google.container.v1alpha1.NodeConfig
	57, // 38: google.container.v1alpha1.NodePool.autoscaling:type_name -> google.container.v1alpha1.NodePoolAutoscaling
	48, // 39: google.container.v1alpha1.NodePool.management:type_name -> google.container.v1alpha1.NodeManagement
	6,  // 40: google.container.v1alpha1.NodePool.status:type_name -> google.container.v1alpha1.NodePool.Status
	49, // 41: google.container.v1alpha1.NodeManagement.upgrade_options:type_name -> google.container.v1alpha1.AutoUpgradeOptions
	51, // 42: google.container.v1alpha1.MaintenancePolicy.window:type_name -> google.container.v1alpha1.MaintenanceWindow
	52, // 43: google.container.v1alpha1.MaintenanceWindow.daily_maintenance_window:type_name -> google.container.v1alpha1.DailyMaintenanceWindow
	48, // 44: google.container.v1alpha1.SetNodePoolManagementRequest.management:type_name -> google.container.v1alpha1.NodeManagement
	47, // 45: google.container.v1alpha1.ListNodePoolsResponse.node_pools:type_name -> google.container.v1alpha1.NodePool
	68, // 46: google.container.v1alpha1.SetLabelsRequest.resource_labels:type_name -> google.container.v1alpha1.SetLabelsRequest.ResourceLabelsEntry
	17, // 47: google.container.v1alpha1.SetNetworkPolicyRequest.network_policy:type_name -> google.container.v1alpha1.NetworkPolicy
	50, // 48: google.container.v1alpha1.SetMaintenancePolicyRequest.maintenance_policy:type_name -> google.container.v1alpha1.MaintenancePolicy
	35, // 49: google.container.v1alpha1.ClusterManager.ListClusters:input_type -> google.container.v1alpha1.ListClustersRequest
	24, // 50: google.container.v1alpha1.ClusterManager.GetCluster:input_type -> google.container.v1alpha1.GetClusterRequest
	23, // 51: google.container.v1alpha1.ClusterManager.CreateCluster:input_type -> google.container.v1alpha1.CreateClusterRequest
	25, // 52: google.container.v1alpha1.ClusterManager.UpdateCluster:input_type -> google.container.v1alpha1.UpdateClusterRequest
	26, // 53: google.container.v1alpha1.ClusterManager.UpdateNodePool:input_type -> google.container.v1alpha1.UpdateNodePoolRequest
	27, // 54: google.container.v1alpha1.ClusterManager.SetNodePoolAutoscaling:input_type -> google.container.v1alpha1.SetNodePoolAutoscalingRequest
	28, // 55: google.container.v1alpha1.ClusterManager.SetLoggingService:input_type -> google.container.v1alpha1.SetLoggingServiceRequest
	29, // 56: google.container.v1alpha1.ClusterManager.SetMonitoringService:input_type -> google.container.v1alpha1.SetMonitoringServiceRequest
	30, // 57: google.container.v1alpha1.ClusterManager.SetAddonsConfig:input_type -> google.container.v1alpha1.SetAddonsConfigRequest
	31, // 58: google.container.v1alpha1.ClusterManager.SetLocations:input_type -> google.container.v1alpha1.SetLocationsRequest
	32, // 59: google.container.v1alpha1.ClusterManager.UpdateMaster:input_type -> google.container.v1alpha1.UpdateMasterRequest
	33, // 60: google.container.v1alpha1.ClusterManager.SetMasterAuth:input_type -> google.container.v1alpha1.SetMasterAuthRequest
	34, // 61: google.container.v1alpha1.ClusterManager.DeleteCluster:input_type -> google.container.v1alpha1.DeleteClusterRequest
	38, // 62: google.container.v1alpha1.ClusterManager.ListOperations:input_type -> google.container.v1alpha1.ListOperationsRequest
	37, // 63: google.container.v1alpha1.ClusterManager.GetOperation:input_type -> google.container.v1alpha1.GetOperationRequest
	39, // 64: google.container.v1alpha1.ClusterManager.CancelOperation:input_type -> google.container.v1alpha1.CancelOperationRequest
	41, // 65: google.container.v1alpha1.ClusterManager.GetServerConfig:input_type -> google.container.v1alpha1.GetServerConfigRequest
	45, // 66: google.container.v1alpha1.ClusterManager.ListNodePools:input_type -> google.container.v1alpha1.ListNodePoolsRequest
	46, // 67: google.container.v1alpha1.ClusterManager.GetNodePool:input_type -> google.container.v1alpha1.GetNodePoolRequest
	43, // 68: google.container.v1alpha1.ClusterManager.CreateNodePool:input_type -> google.container.v1alpha1.CreateNodePoolRequest
	44, // 69: google.container.v1alpha1.ClusterManager.DeleteNodePool:input_type -> google.container.v1alpha1.DeleteNodePoolRequest
	55, // 70: google.container.v1alpha1.ClusterManager.RollbackNodePoolUpgrade:input_type -> google.container.v1alpha1.RollbackNodePoolUpgradeRequest
	53, // 71: google.container.v1alpha1.ClusterManager.SetNodePoolManagement:input_type -> google.container.v1alpha1.SetNodePoolManagementRequest
	58, // 72: google.container.v1alpha1.ClusterManager.SetLabels:input_type -> google.container.v1alpha1.SetLabelsRequest
	59, // 73: google.container.v1alpha1.ClusterManager.SetLegacyAbac:input_type -> google.container.v1alpha1.SetLegacyAbacRequest
	60, // 74: google.container.v1alpha1.ClusterManager.StartIPRotation:input_type -> google.container.v1alpha1.StartIPRotationRequest
	61, // 75: google.container.v1alpha1.ClusterManager.CompleteIPRotation:input_type -> google.container.v1alpha1.CompleteIPRotationRequest
	54, // 76: google.container.v1alpha1.ClusterManager.SetNodePoolSize:input_type -> google.container.v1alpha1.SetNodePoolSizeRequest
	63, // 77: google.container.v1alpha1.ClusterManager.SetNetworkPolicy:input_type -> google.container.v1alpha1.SetNetworkPolicyRequest
	64, // 78: google.container.v1alpha1.ClusterManager.SetMaintenancePolicy:input_type -> google.container.v1alpha1.SetMaintenancePolicyRequest
	36, // 79: google.container.v1alpha1.ClusterManager.ListClusters:output_type -> google.container.v1alpha1.ListClustersResponse
	20, // 80: google.container.v1alpha1.ClusterManager.GetCluster:output_type -> google.container.v1alpha1.Cluster
	22, // 81: google.container.v1alpha1.ClusterManager.CreateCluster:output_type -> google.container.v1alpha1.Operation
	22, // 82: google.container.v1alpha1.ClusterManager.UpdateCluster:output_type -> google.container.v1alpha1.Operation
	22, // 83: google.container.v1alpha1.ClusterManager.UpdateNodePool:output_type -> google.container.v1alpha1.Operation
	22, // 84: google.container.v1alpha1.ClusterManager.SetNodePoolAutoscaling:output_type -> google.container.v1alpha1.Operation
	22, // 85: google.container.v1alpha1.ClusterManager.SetLoggingService:output_type -> google.container.v1alpha1.Operation
	22, // 86: google.container.v1alpha1.ClusterManager.SetMonitoringService:output_type -> google.container.v1alpha1.Operation
	22, // 87: google.container.v1alpha1.ClusterManager.SetAddonsConfig:output_type -> google.container.v1alpha1.Operation
	22, // 88: google.container.v1alpha1.ClusterManager.SetLocations:output_type -> google.container.v1alpha1.Operation
	22, // 89: google.container.v1alpha1.ClusterManager.UpdateMaster:output_type -> google.container.v1alpha1.Operation
	22, // 90: google.container.v1alpha1.ClusterManager.SetMasterAuth:output_type -> google.container.v1alpha1.Operation
	22, // 91: google.container.v1alpha1.ClusterManager.DeleteCluster:output_type -> google.container.v1alpha1.Operation
	40, // 92: google.container.v1alpha1.ClusterManager.ListOperations:output_type -> google.container.v1alpha1.ListOperationsResponse
	22, // 93: google.container.v1alpha1.ClusterManager.GetOperation:output_type -> google.container.v1alpha1.Operation
	69, // 94: google.container.v1alpha1.ClusterManager.CancelOperation:output_type -> google.protobuf.Empty
	42, // 95: google.container.v1alpha1.ClusterManager.GetServerConfig:output_type -> google.container.v1alpha1.ServerConfig
	56, // 96: google.container.v1alpha1.ClusterManager.ListNodePools:output_type -> google.container.v1alpha1.ListNodePoolsResponse
	47, // 97: google.container.v1alpha1.ClusterManager.GetNodePool:output_type -> google.container.v1alpha1.NodePool
	22, // 98: google.container.v1alpha1.ClusterManager.CreateNodePool:output_type -> google.container.v1alpha1.Operation
	22, // 99: google.container.v1alpha1.ClusterManager.DeleteNodePool:output_type -> google.container.v1alpha1.Operation
	22, // 100: google.container.v1alpha1.ClusterManager.RollbackNodePoolUpgrade:output_type -> google.container.v1alpha1.Operation
	22, // 101: google.container.v1alpha1.ClusterManager.SetNodePoolManagement:output_type -> google.container.v1alpha1.Operation
	22, // 102: google.container.v1alpha1.ClusterManager.SetLabels:output_type -> google.container.v1alpha1.Operation
	22, // 103: google.container.v1alpha1.ClusterManager.SetLegacyAbac:output_type -> google.container.v1alpha1.Operation
	22, // 104: google.container.v1alpha1.ClusterManager.StartIPRotation:output_type -> google.container.v1alpha1.Operation
	22, // 105: google.container.v1alpha1.ClusterManager.CompleteIPRotation:output_type -> google.container.v1alpha1.Operation
	22, // 106: google.container.v1alpha1.ClusterManager.SetNodePoolSize:output_type -> google.container.v1alpha1.Operation
	22, // 107: google.container.v1alpha1.ClusterManager.SetNetworkPolicy:output_type -> google.container.v1alpha1.Operation
	22, // 108: google.container.v1alpha1.ClusterManager.SetMaintenancePolicy:output_type -> google.container.v1alpha1.Operation
	79, // [79:109] is the sub-list for method output_type
	49, // [49:79] is the sub-list for method input_type
	49, // [49:49] is the sub-list for extension type_name
	49, // [49:49] is the sub-list for extension extendee
	0,  // [0:49] is the sub-list for field type_name
}

func init() { file_google_container_v1alpha1_cluster_service_proto_init() }
func file_google_container_v1alpha1_cluster_service_proto_init() {
	if File_google_container_v1alpha1_cluster_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodeConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodeTaint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MasterAuth); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientCertificateConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddonsConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HttpLoadBalancing); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HorizontalPodAutoscaling); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KubernetesDashboard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworkPolicyConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MasterAuthorizedNetworksConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworkPolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IPAllocationPolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PodSecurityPolicyConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Cluster); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClusterUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Operation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateClusterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetClusterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateClusterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateNodePoolRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetNodePoolAutoscalingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetLoggingServiceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetMonitoringServiceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetAddonsConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetLocationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateMasterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetMasterAuthRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteClusterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListClustersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListClustersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOperationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOperationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelOperationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOperationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServerConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateNodePoolRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteNodePoolRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListNodePoolsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNodePoolRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodePool); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodeManagement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutoUpgradeOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaintenancePolicy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaintenanceWindow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DailyMaintenanceWindow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetNodePoolManagementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetNodePoolSizeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RollbackNodePoolUpgradeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListNodePoolsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodePoolAutoscaling); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetLabelsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetLegacyAbacRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartIPRotationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompleteIPRotationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceleratorConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetNetworkPolicyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetMaintenancePolicyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_container_v1alpha1_cluster_service_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MasterAuthorizedNetworksConfig_CidrBlock); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_container_v1alpha1_cluster_service_proto_msgTypes[44].OneofWrappers = []interface{}{
		(*MaintenanceWindow_DailyMaintenanceWindow)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_container_v1alpha1_cluster_service_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   62,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_google_container_v1alpha1_cluster_service_proto_goTypes,
		DependencyIndexes: file_google_container_v1alpha1_cluster_service_proto_depIdxs,
		EnumInfos:         file_google_container_v1alpha1_cluster_service_proto_enumTypes,
		MessageInfos:      file_google_container_v1alpha1_cluster_service_proto_msgTypes,
	}.Build()
	File_google_container_v1alpha1_cluster_service_proto = out.File
	file_google_container_v1alpha1_cluster_service_proto_rawDesc = nil
	file_google_container_v1alpha1_cluster_service_proto_goTypes = nil
	file_google_container_v1alpha1_cluster_service_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// ClusterManagerClient is the client API for ClusterManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ClusterManagerClient interface {
	// Lists all clusters owned by a project in either the specified zone or all
	// zones.
	ListClusters(ctx context.Context, in *ListClustersRequest, opts ...grpc.CallOption) (*ListClustersResponse, error)
	// Gets the details of a specific cluster.
	GetCluster(ctx context.Context, in *GetClusterRequest, opts ...grpc.CallOption) (*Cluster, error)
	// Creates a cluster, consisting of the specified number and type of Google
	// Compute Engine instances.
	//
	// By default, the cluster is created in the project's
	// [default network](/compute/docs/networks-and-firewalls#networks).
	//
	// One firewall is added for the cluster. After cluster creation,
	// the cluster creates routes for each node to allow the containers
	// on that node to communicate with all other instances in the
	// cluster.
	//
	// Finally, an entry is added to the project's global metadata indicating
	// which CIDR range is being used by the cluster.
	CreateCluster(ctx context.Context, in *CreateClusterRequest, opts ...grpc.CallOption) (*Operation, error)
	// Updates the settings of a specific cluster.
	UpdateCluster(ctx context.Context, in *UpdateClusterRequest, opts ...grpc.CallOption) (*Operation, error)
	// Updates the version and/or iamge type of a specific node pool.
	UpdateNodePool(ctx context.Context, in *UpdateNodePoolRequest, opts ...grpc.CallOption) (*Operation, error)
	// Sets the autoscaling settings of a specific node pool.
	SetNodePoolAutoscaling(ctx context.Context, in *SetNodePoolAutoscalingRequest, opts ...grpc.CallOption) (*Operation, error)
	// Sets the logging service of a specific cluster.
	SetLoggingService(ctx context.Context, in *SetLoggingServiceRequest, opts ...grpc.CallOption) (*Operation, error)
	// Sets the monitoring service of a specific cluster.
	SetMonitoringService(ctx context.Context, in *SetMonitoringServiceRequest, opts ...grpc.CallOption) (*Operation, error)
	// Sets the addons of a specific cluster.
	SetAddonsConfig(ctx context.Context, in *SetAddonsConfigRequest, opts ...grpc.CallOption) (*Operation, error)
	// Sets the locations of a specific cluster.
	SetLocations(ctx context.Context, in *SetLocationsRequest, opts ...grpc.CallOption) (*Operation, error)
	// Updates the master of a specific cluster.
	UpdateMaster(ctx context.Context, in *UpdateMasterRequest, opts ...grpc.CallOption) (*Operation, error)
	// Used to set master auth materials. Currently supports :-
	// Changing the admin password of a specific cluster.
	// This can be either via password generation or explicitly set.
	// Modify basic_auth.csv and reset the K8S API server.
	SetMasterAuth(ctx context.Context, in *SetMasterAuthRequest, opts ...grpc.CallOption) (*Operation, error)
	// Deletes the cluster, including the Kubernetes endpoint and all worker
	// nodes.
	//
	// Firewalls and routes that were configured during cluster creation
	// are also deleted.
	//
	// Other Google Compute Engine resources that might be in use by the cluster
	// (e.g. load balancer resources) will not be deleted if they weren't present
	// at the initial create time.
	DeleteCluster(ctx context.Context, in *DeleteClusterRequest, opts ...grpc.CallOption) (*Operation, error)
	// Lists all operations in a project in a specific zone or all zones.
	ListOperations(ctx context.Context, in *ListOperationsRequest, opts ...grpc.CallOption) (*ListOperationsResponse, error)
	// Gets the specified operation.
	GetOperation(ctx context.Context, in *GetOperationRequest, opts ...grpc.CallOption) (*Operation, error)
	// Cancels the specified operation.
	CancelOperation(ctx context.Context, in *CancelOperationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Returns configuration info about the Container Engine service.
	GetServerConfig(ctx context.Context, in *GetServerConfigRequest, opts ...grpc.CallOption) (*ServerConfig, error)
	// Lists the node pools for a cluster.
	ListNodePools(ctx context.Context, in *ListNodePoolsRequest, opts ...grpc.CallOption) (*ListNodePoolsResponse, error)
	// Retrieves the node pool requested.
	GetNodePool(ctx context.Context, in *GetNodePoolRequest, opts ...grpc.CallOption) (*NodePool, error)
	// Creates a node pool for a cluster.
	CreateNodePool(ctx context.Context, in *CreateNodePoolRequest, opts ...grpc.CallOption) (*Operation, error)
	// Deletes a node pool from a cluster.
	DeleteNodePool(ctx context.Context, in *DeleteNodePoolRequest, opts ...grpc.CallOption) (*Operation, error)
	// Roll back the previously Aborted or Failed NodePool upgrade.
	// This will be an no-op if the last upgrade successfully completed.
	RollbackNodePoolUpgrade(ctx context.Context, in *RollbackNodePoolUpgradeRequest, opts ...grpc.CallOption) (*Operation, error)
	// Sets the NodeManagement options for a node pool.
	SetNodePoolManagement(ctx context.Context, in *SetNodePoolManagementRequest, opts ...grpc.CallOption) (*Operation, error)
	// Sets labels on a cluster.
	SetLabels(ctx context.Context, in *SetLabelsRequest, opts ...grpc.CallOption) (*Operation, error)
	// Enables or disables the ABAC authorization mechanism on a cluster.
	SetLegacyAbac(ctx context.Context, in *SetLegacyAbacRequest, opts ...grpc.CallOption) (*Operation, error)
	// Start master IP rotation.
	StartIPRotation(ctx context.Context, in *StartIPRotationRequest, opts ...grpc.CallOption) (*Operation, error)
	// Completes master IP rotation.
	CompleteIPRotation(ctx context.Context, in *CompleteIPRotationRequest, opts ...grpc.CallOption) (*Operation, error)
	// Sets the size of a specific node pool.
	SetNodePoolSize(ctx context.Context, in *SetNodePoolSizeRequest, opts ...grpc.CallOption) (*Operation, error)
	// Enables/Disables Network Policy for a cluster.
	SetNetworkPolicy(ctx context.Context, in *SetNetworkPolicyRequest, opts ...grpc.CallOption) (*Operation, error)
	// Sets the maintenance policy for a cluster.
	SetMaintenancePolicy(ctx context.Context, in *SetMaintenancePolicyRequest, opts ...grpc.CallOption) (*Operation, error)
}

type clusterManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewClusterManagerClient(cc grpc.ClientConnInterface) ClusterManagerClient {
	return &clusterManagerClient{cc}
}

func (c *clusterManagerClient) ListClusters(ctx context.Context, in *ListClustersRequest, opts ...grpc.CallOption) (*ListClustersResponse, error) {
	out := new(ListClustersResponse)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/ListClusters", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) GetCluster(ctx context.Context, in *GetClusterRequest, opts ...grpc.CallOption) (*Cluster, error) {
	out := new(Cluster)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/GetCluster", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) CreateCluster(ctx context.Context, in *CreateClusterRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/CreateCluster", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) UpdateCluster(ctx context.Context, in *UpdateClusterRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/UpdateCluster", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) UpdateNodePool(ctx context.Context, in *UpdateNodePoolRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/UpdateNodePool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) SetNodePoolAutoscaling(ctx context.Context, in *SetNodePoolAutoscalingRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/SetNodePoolAutoscaling", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) SetLoggingService(ctx context.Context, in *SetLoggingServiceRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/SetLoggingService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) SetMonitoringService(ctx context.Context, in *SetMonitoringServiceRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/SetMonitoringService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) SetAddonsConfig(ctx context.Context, in *SetAddonsConfigRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/SetAddonsConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) SetLocations(ctx context.Context, in *SetLocationsRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/SetLocations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) UpdateMaster(ctx context.Context, in *UpdateMasterRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/UpdateMaster", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) SetMasterAuth(ctx context.Context, in *SetMasterAuthRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/SetMasterAuth", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) DeleteCluster(ctx context.Context, in *DeleteClusterRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/DeleteCluster", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) ListOperations(ctx context.Context, in *ListOperationsRequest, opts ...grpc.CallOption) (*ListOperationsResponse, error) {
	out := new(ListOperationsResponse)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/ListOperations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) GetOperation(ctx context.Context, in *GetOperationRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/GetOperation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) CancelOperation(ctx context.Context, in *CancelOperationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/CancelOperation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) GetServerConfig(ctx context.Context, in *GetServerConfigRequest, opts ...grpc.CallOption) (*ServerConfig, error) {
	out := new(ServerConfig)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/GetServerConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) ListNodePools(ctx context.Context, in *ListNodePoolsRequest, opts ...grpc.CallOption) (*ListNodePoolsResponse, error) {
	out := new(ListNodePoolsResponse)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/ListNodePools", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) GetNodePool(ctx context.Context, in *GetNodePoolRequest, opts ...grpc.CallOption) (*NodePool, error) {
	out := new(NodePool)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/GetNodePool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) CreateNodePool(ctx context.Context, in *CreateNodePoolRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/CreateNodePool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) DeleteNodePool(ctx context.Context, in *DeleteNodePoolRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/DeleteNodePool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) RollbackNodePoolUpgrade(ctx context.Context, in *RollbackNodePoolUpgradeRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/RollbackNodePoolUpgrade", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) SetNodePoolManagement(ctx context.Context, in *SetNodePoolManagementRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/SetNodePoolManagement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) SetLabels(ctx context.Context, in *SetLabelsRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/SetLabels", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) SetLegacyAbac(ctx context.Context, in *SetLegacyAbacRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/SetLegacyAbac", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) StartIPRotation(ctx context.Context, in *StartIPRotationRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/StartIPRotation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) CompleteIPRotation(ctx context.Context, in *CompleteIPRotationRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/CompleteIPRotation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) SetNodePoolSize(ctx context.Context, in *SetNodePoolSizeRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/SetNodePoolSize", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) SetNetworkPolicy(ctx context.Context, in *SetNetworkPolicyRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/SetNetworkPolicy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterManagerClient) SetMaintenancePolicy(ctx context.Context, in *SetMaintenancePolicyRequest, opts ...grpc.CallOption) (*Operation, error) {
	out := new(Operation)
	err := c.cc.Invoke(ctx, "/google.container.v1alpha1.ClusterManager/SetMaintenancePolicy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ClusterManagerServer is the server API for ClusterManager service.
type ClusterManagerServer interface {
	// Lists all clusters owned by a project in either the specified zone or all
	// zones.
	ListClusters(context.Context, *ListClustersRequest) (*ListClustersResponse, error)
	// Gets the details of a specific cluster.
	GetCluster(context.Context, *GetClusterRequest) (*Cluster, error)
	// Creates a cluster, consisting of the specified number and type of Google
	// Compute Engine instances.
	//
	// By default, the cluster is created in the project's
	// [default network](/compute/docs/networks-and-firewalls#networks).
	//
	// One firewall is added for the cluster. After cluster creation,
	// the cluster creates routes for each node to allow the containers
	// on that node to communicate with all other instances in the
	// cluster.
	//
	// Finally, an entry is added to the project's global metadata indicating
	// which CIDR range is being used by the cluster.
	CreateCluster(context.Context, *CreateClusterRequest) (*Operation, error)
	// Updates the settings of a specific cluster.
	UpdateCluster(context.Context, *UpdateClusterRequest) (*Operation, error)
	// Updates the version and/or iamge type of a specific node pool.
	UpdateNodePool(context.Context, *UpdateNodePoolRequest) (*Operation, error)
	// Sets the autoscaling settings of a specific node pool.
	SetNodePoolAutoscaling(context.Context, *SetNodePoolAutoscalingRequest) (*Operation, error)
	// Sets the logging service of a specific cluster.
	SetLoggingService(context.Context, *SetLoggingServiceRequest) (*Operation, error)
	// Sets the monitoring service of a specific cluster.
	SetMonitoringService(context.Context, *SetMonitoringServiceRequest) (*Operation, error)
	// Sets the addons of a specific cluster.
	SetAddonsConfig(context.Context, *SetAddonsConfigRequest) (*Operation, error)
	// Sets the locations of a specific cluster.
	SetLocations(context.Context, *SetLocationsRequest) (*Operation, error)
	// Updates the master of a specific cluster.
	UpdateMaster(context.Context, *UpdateMasterRequest) (*Operation, error)
	// Used to set master auth materials. Currently supports :-
	// Changing the admin password of a specific cluster.
	// This can be either via password generation or explicitly set.
	// Modify basic_auth.csv and reset the K8S API server.
	SetMasterAuth(context.Context, *SetMasterAuthRequest) (*Operation, error)
	// Deletes the cluster, including the Kubernetes endpoint and all worker
	// nodes.
	//
	// Firewalls and routes that were configured during cluster creation
	// are also deleted.
	//
	// Other Google Compute Engine resources that might be in use by the cluster
	// (e.g. load balancer resources) will not be deleted if they weren't present
	// at the initial create time.
	DeleteCluster(context.Context, *DeleteClusterRequest) (*Operation, error)
	// Lists all operations in a project in a specific zone or all zones.
	ListOperations(context.Context, *ListOperationsRequest) (*ListOperationsResponse, error)
	// Gets the specified operation.
	GetOperation(context.Context, *GetOperationRequest) (*Operation, error)
	// Cancels the specified operation.
	CancelOperation(context.Context, *CancelOperationRequest) (*emptypb.Empty, error)
	// Returns configuration info about the Container Engine service.
	GetServerConfig(context.Context, *GetServerConfigRequest) (*ServerConfig, error)
	// Lists the node pools for a cluster.
	ListNodePools(context.Context, *ListNodePoolsRequest) (*ListNodePoolsResponse, error)
	// Retrieves the node pool requested.
	GetNodePool(context.Context, *GetNodePoolRequest) (*NodePool, error)
	// Creates a node pool for a cluster.
	CreateNodePool(context.Context, *CreateNodePoolRequest) (*Operation, error)
	// Deletes a node pool from a cluster.
	DeleteNodePool(context.Context, *DeleteNodePoolRequest) (*Operation, error)
	// Roll back the previously Aborted or Failed NodePool upgrade.
	// This will be an no-op if the last upgrade successfully completed.
	RollbackNodePoolUpgrade(context.Context, *RollbackNodePoolUpgradeRequest) (*Operation, error)
	// Sets the NodeManagement options for a node pool.
	SetNodePoolManagement(context.Context, *SetNodePoolManagementRequest) (*Operation, error)
	// Sets labels on a cluster.
	SetLabels(context.Context, *SetLabelsRequest) (*Operation, error)
	// Enables or disables the ABAC authorization mechanism on a cluster.
	SetLegacyAbac(context.Context, *SetLegacyAbacRequest) (*Operation, error)
	// Start master IP rotation.
	StartIPRotation(context.Context, *StartIPRotationRequest) (*Operation, error)
	// Completes master IP rotation.
	CompleteIPRotation(context.Context, *CompleteIPRotationRequest) (*Operation, error)
	// Sets the size of a specific node pool.
	SetNodePoolSize(context.Context, *SetNodePoolSizeRequest) (*Operation, error)
	// Enables/Disables Network Policy for a cluster.
	SetNetworkPolicy(context.Context, *SetNetworkPolicyRequest) (*Operation, error)
	// Sets the maintenance policy for a cluster.
	SetMaintenancePolicy(context.Context, *SetMaintenancePolicyRequest) (*Operation, error)
}

// UnimplementedClusterManagerServer can be embedded to have forward compatible implementations.
type UnimplementedClusterManagerServer struct {
}

func (*UnimplementedClusterManagerServer) ListClusters(context.Context, *ListClustersRequest) (*ListClustersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListClusters not implemented")
}
func (*UnimplementedClusterManagerServer) GetCluster(context.Context, *GetClusterRequest) (*Cluster, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCluster not implemented")
}
func (*UnimplementedClusterManagerServer) CreateCluster(context.Context, *CreateClusterRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCluster not implemented")
}
func (*UnimplementedClusterManagerServer) UpdateCluster(context.Context, *UpdateClusterRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCluster not implemented")
}
func (*UnimplementedClusterManagerServer) UpdateNodePool(context.Context, *UpdateNodePoolRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNodePool not implemented")
}
func (*UnimplementedClusterManagerServer) SetNodePoolAutoscaling(context.Context, *SetNodePoolAutoscalingRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetNodePoolAutoscaling not implemented")
}
func (*UnimplementedClusterManagerServer) SetLoggingService(context.Context, *SetLoggingServiceRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetLoggingService not implemented")
}
func (*UnimplementedClusterManagerServer) SetMonitoringService(context.Context, *SetMonitoringServiceRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetMonitoringService not implemented")
}
func (*UnimplementedClusterManagerServer) SetAddonsConfig(context.Context, *SetAddonsConfigRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetAddonsConfig not implemented")
}
func (*UnimplementedClusterManagerServer) SetLocations(context.Context, *SetLocationsRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetLocations not implemented")
}
func (*UnimplementedClusterManagerServer) UpdateMaster(context.Context, *UpdateMasterRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMaster not implemented")
}
func (*UnimplementedClusterManagerServer) SetMasterAuth(context.Context, *SetMasterAuthRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetMasterAuth not implemented")
}
func (*UnimplementedClusterManagerServer) DeleteCluster(context.Context, *DeleteClusterRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCluster not implemented")
}
func (*UnimplementedClusterManagerServer) ListOperations(context.Context, *ListOperationsRequest) (*ListOperationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListOperations not implemented")
}
func (*UnimplementedClusterManagerServer) GetOperation(context.Context, *GetOperationRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOperation not implemented")
}
func (*UnimplementedClusterManagerServer) CancelOperation(context.Context, *CancelOperationRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelOperation not implemented")
}
func (*UnimplementedClusterManagerServer) GetServerConfig(context.Context, *GetServerConfigRequest) (*ServerConfig, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServerConfig not implemented")
}
func (*UnimplementedClusterManagerServer) ListNodePools(context.Context, *ListNodePoolsRequest) (*ListNodePoolsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNodePools not implemented")
}
func (*UnimplementedClusterManagerServer) GetNodePool(context.Context, *GetNodePoolRequest) (*NodePool, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNodePool not implemented")
}
func (*UnimplementedClusterManagerServer) CreateNodePool(context.Context, *CreateNodePoolRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNodePool not implemented")
}
func (*UnimplementedClusterManagerServer) DeleteNodePool(context.Context, *DeleteNodePoolRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNodePool not implemented")
}
func (*UnimplementedClusterManagerServer) RollbackNodePoolUpgrade(context.Context, *RollbackNodePoolUpgradeRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RollbackNodePoolUpgrade not implemented")
}
func (*UnimplementedClusterManagerServer) SetNodePoolManagement(context.Context, *SetNodePoolManagementRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetNodePoolManagement not implemented")
}
func (*UnimplementedClusterManagerServer) SetLabels(context.Context, *SetLabelsRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetLabels not implemented")
}
func (*UnimplementedClusterManagerServer) SetLegacyAbac(context.Context, *SetLegacyAbacRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetLegacyAbac not implemented")
}
func (*UnimplementedClusterManagerServer) StartIPRotation(context.Context, *StartIPRotationRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartIPRotation not implemented")
}
func (*UnimplementedClusterManagerServer) CompleteIPRotation(context.Context, *CompleteIPRotationRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CompleteIPRotation not implemented")
}
func (*UnimplementedClusterManagerServer) SetNodePoolSize(context.Context, *SetNodePoolSizeRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetNodePoolSize not implemented")
}
func (*UnimplementedClusterManagerServer) SetNetworkPolicy(context.Context, *SetNetworkPolicyRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetNetworkPolicy not implemented")
}
func (*UnimplementedClusterManagerServer) SetMaintenancePolicy(context.Context, *SetMaintenancePolicyRequest) (*Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetMaintenancePolicy not implemented")
}

func RegisterClusterManagerServer(s *grpc.Server, srv ClusterManagerServer) {
	s.RegisterService(&_ClusterManager_serviceDesc, srv)
}

func _ClusterManager_ListClusters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListClustersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).ListClusters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/ListClusters",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).ListClusters(ctx, req.(*ListClustersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_GetCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).GetCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/GetCluster",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).GetCluster(ctx, req.(*GetClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_CreateCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).CreateCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/CreateCluster",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).CreateCluster(ctx, req.(*CreateClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_UpdateCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).UpdateCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/UpdateCluster",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).UpdateCluster(ctx, req.(*UpdateClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_UpdateNodePool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodePoolRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).UpdateNodePool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/UpdateNodePool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).UpdateNodePool(ctx, req.(*UpdateNodePoolRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_SetNodePoolAutoscaling_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetNodePoolAutoscalingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).SetNodePoolAutoscaling(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/SetNodePoolAutoscaling",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).SetNodePoolAutoscaling(ctx, req.(*SetNodePoolAutoscalingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_SetLoggingService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetLoggingServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).SetLoggingService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/SetLoggingService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).SetLoggingService(ctx, req.(*SetLoggingServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_SetMonitoringService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMonitoringServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).SetMonitoringService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/SetMonitoringService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).SetMonitoringService(ctx, req.(*SetMonitoringServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_SetAddonsConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetAddonsConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).SetAddonsConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/SetAddonsConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).SetAddonsConfig(ctx, req.(*SetAddonsConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_SetLocations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetLocationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).SetLocations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/SetLocations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).SetLocations(ctx, req.(*SetLocationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_UpdateMaster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMasterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).UpdateMaster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/UpdateMaster",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).UpdateMaster(ctx, req.(*UpdateMasterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_SetMasterAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMasterAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).SetMasterAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/SetMasterAuth",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).SetMasterAuth(ctx, req.(*SetMasterAuthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_DeleteCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).DeleteCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/DeleteCluster",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).DeleteCluster(ctx, req.(*DeleteClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_ListOperations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOperationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).ListOperations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/ListOperations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).ListOperations(ctx, req.(*ListOperationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_GetOperation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOperationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).GetOperation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/GetOperation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).GetOperation(ctx, req.(*GetOperationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_CancelOperation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelOperationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).CancelOperation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/CancelOperation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).CancelOperation(ctx, req.(*CancelOperationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_GetServerConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServerConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).GetServerConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/GetServerConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).GetServerConfig(ctx, req.(*GetServerConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_ListNodePools_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNodePoolsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).ListNodePools(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/ListNodePools",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).ListNodePools(ctx, req.(*ListNodePoolsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_GetNodePool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNodePoolRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).GetNodePool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/GetNodePool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).GetNodePool(ctx, req.(*GetNodePoolRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_CreateNodePool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNodePoolRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).CreateNodePool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/CreateNodePool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).CreateNodePool(ctx, req.(*CreateNodePoolRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_DeleteNodePool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNodePoolRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).DeleteNodePool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/DeleteNodePool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).DeleteNodePool(ctx, req.(*DeleteNodePoolRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_RollbackNodePoolUpgrade_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RollbackNodePoolUpgradeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).RollbackNodePoolUpgrade(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/RollbackNodePoolUpgrade",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).RollbackNodePoolUpgrade(ctx, req.(*RollbackNodePoolUpgradeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_SetNodePoolManagement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetNodePoolManagementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).SetNodePoolManagement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/SetNodePoolManagement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).SetNodePoolManagement(ctx, req.(*SetNodePoolManagementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_SetLabels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetLabelsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).SetLabels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/SetLabels",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).SetLabels(ctx, req.(*SetLabelsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_SetLegacyAbac_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetLegacyAbacRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).SetLegacyAbac(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/SetLegacyAbac",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).SetLegacyAbac(ctx, req.(*SetLegacyAbacRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_StartIPRotation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartIPRotationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).StartIPRotation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/StartIPRotation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).StartIPRotation(ctx, req.(*StartIPRotationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_CompleteIPRotation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompleteIPRotationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).CompleteIPRotation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/CompleteIPRotation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).CompleteIPRotation(ctx, req.(*CompleteIPRotationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_SetNodePoolSize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetNodePoolSizeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).SetNodePoolSize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/SetNodePoolSize",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).SetNodePoolSize(ctx, req.(*SetNodePoolSizeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_SetNetworkPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetNetworkPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).SetNetworkPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/SetNetworkPolicy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).SetNetworkPolicy(ctx, req.(*SetNetworkPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterManager_SetMaintenancePolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMaintenancePolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterManagerServer).SetMaintenancePolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.container.v1alpha1.ClusterManager/SetMaintenancePolicy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterManagerServer).SetMaintenancePolicy(ctx, req.(*SetMaintenancePolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ClusterManager_serviceDesc = grpc.ServiceDesc{
	ServiceName: "google.container.v1alpha1.ClusterManager",
	HandlerType: (*ClusterManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListClusters",
			Handler:    _ClusterManager_ListClusters_Handler,
		},
		{
			MethodName: "GetCluster",
			Handler:    _ClusterManager_GetCluster_Handler,
		},
		{
			MethodName: "CreateCluster",
			Handler:    _ClusterManager_CreateCluster_Handler,
		},
		{
			MethodName: "UpdateCluster",
			Handler:    _ClusterManager_UpdateCluster_Handler,
		},
		{
			MethodName: "UpdateNodePool",
			Handler:    _ClusterManager_UpdateNodePool_Handler,
		},
		{
			MethodName: "SetNodePoolAutoscaling",
			Handler:    _ClusterManager_SetNodePoolAutoscaling_Handler,
		},
		{
			MethodName: "SetLoggingService",
			Handler:    _ClusterManager_SetLoggingService_Handler,
		},
		{
			MethodName: "SetMonitoringService",
			Handler:    _ClusterManager_SetMonitoringService_Handler,
		},
		{
			MethodName: "SetAddonsConfig",
			Handler:    _ClusterManager_SetAddonsConfig_Handler,
		},
		{
			MethodName: "SetLocations",
			Handler:    _ClusterManager_SetLocations_Handler,
		},
		{
			MethodName: "UpdateMaster",
			Handler:    _ClusterManager_UpdateMaster_Handler,
		},
		{
			MethodName: "SetMasterAuth",
			Handler:    _ClusterManager_SetMasterAuth_Handler,
		},
		{
			MethodName: "DeleteCluster",
			Handler:    _ClusterManager_DeleteCluster_Handler,
		},
		{
			MethodName: "ListOperations",
			Handler:    _ClusterManager_ListOperations_Handler,
		},
		{
			MethodName: "GetOperation",
			Handler:    _ClusterManager_GetOperation_Handler,
		},
		{
			MethodName: "CancelOperation",
			Handler:    _ClusterManager_CancelOperation_Handler,
		},
		{
			MethodName: "GetServerConfig",
			Handler:    _ClusterManager_GetServerConfig_Handler,
		},
		{
			MethodName: "ListNodePools",
			Handler:    _ClusterManager_ListNodePools_Handler,
		},
		{
			MethodName: "GetNodePool",
			Handler:    _ClusterManager_GetNodePool_Handler,
		},
		{
			MethodName: "CreateNodePool",
			Handler:    _ClusterManager_CreateNodePool_Handler,
		},
		{
			MethodName: "DeleteNodePool",
			Handler:    _ClusterManager_DeleteNodePool_Handler,
		},
		{
			MethodName: "RollbackNodePoolUpgrade",
			Handler:    _ClusterManager_RollbackNodePoolUpgrade_Handler,
		},
		{
			MethodName: "SetNodePoolManagement",
			Handler:    _ClusterManager_SetNodePoolManagement_Handler,
		},
		{
			MethodName: "SetLabels",
			Handler:    _ClusterManager_SetLabels_Handler,
		},
		{
			MethodName: "SetLegacyAbac",
			Handler:    _ClusterManager_SetLegacyAbac_Handler,
		},
		{
			MethodName: "StartIPRotation",
			Handler:    _ClusterManager_StartIPRotation_Handler,
		},
		{
			MethodName: "CompleteIPRotation",
			Handler:    _ClusterManager_CompleteIPRotation_Handler,
		},
		{
			MethodName: "SetNodePoolSize",
			Handler:    _ClusterManager_SetNodePoolSize_Handler,
		},
		{
			MethodName: "SetNetworkPolicy",
			Handler:    _ClusterManager_SetNetworkPolicy_Handler,
		},
		{
			MethodName: "SetMaintenancePolicy",
			Handler:    _ClusterManager_SetMaintenancePolicy_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "google/container/v1alpha1/cluster_service.proto",
}
