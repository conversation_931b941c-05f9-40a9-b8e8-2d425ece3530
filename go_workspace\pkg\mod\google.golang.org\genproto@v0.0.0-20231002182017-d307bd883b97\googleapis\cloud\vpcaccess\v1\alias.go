// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by aliasgen. DO NOT EDIT.

// Package vpcaccess aliases all exported identifiers in package
// "cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb".
//
// Deprecated: Please use types in: cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb.
// Please read https://github.com/googleapis/google-cloud-go/blob/main/migration.md
// for more details.
package vpcaccess

import (
	src "cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb"
	grpc "google.golang.org/grpc"
)

// Deprecated: Please use consts in: cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb
const (
	Connector_CREATING          = src.Connector_CREATING
	Connector_DELETING          = src.Connector_DELETING
	Connector_ERROR             = src.Connector_ERROR
	Connector_READY             = src.Connector_READY
	Connector_STATE_UNSPECIFIED = src.Connector_STATE_UNSPECIFIED
	Connector_UPDATING          = src.Connector_UPDATING
)

// Deprecated: Please use vars in: cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb
var (
	Connector_State_name                            = src.Connector_State_name
	Connector_State_value                           = src.Connector_State_value
	File_google_cloud_vpcaccess_v1_vpc_access_proto = src.File_google_cloud_vpcaccess_v1_vpc_access_proto
)

// Definition of a Serverless VPC Access connector.
//
// Deprecated: Please use types in: cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb
type Connector = src.Connector

// State of a connector.
//
// Deprecated: Please use types in: cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb
type Connector_State = src.Connector_State

// The subnet in which to house the connector
//
// Deprecated: Please use types in: cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb
type Connector_Subnet = src.Connector_Subnet

// Request for creating a Serverless VPC Access connector.
//
// Deprecated: Please use types in: cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb
type CreateConnectorRequest = src.CreateConnectorRequest

// Request for deleting a Serverless VPC Access connector.
//
// Deprecated: Please use types in: cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb
type DeleteConnectorRequest = src.DeleteConnectorRequest

// Request for getting a Serverless VPC Access connector.
//
// Deprecated: Please use types in: cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb
type GetConnectorRequest = src.GetConnectorRequest

// Request for listing Serverless VPC Access connectors in a location.
//
// Deprecated: Please use types in: cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb
type ListConnectorsRequest = src.ListConnectorsRequest

// Response for listing Serverless VPC Access connectors.
//
// Deprecated: Please use types in: cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb
type ListConnectorsResponse = src.ListConnectorsResponse

// Metadata for google.longrunning.Operation.
//
// Deprecated: Please use types in: cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb
type OperationMetadata = src.OperationMetadata

// UnimplementedVpcAccessServiceServer can be embedded to have forward
// compatible implementations.
//
// Deprecated: Please use types in: cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb
type UnimplementedVpcAccessServiceServer = src.UnimplementedVpcAccessServiceServer

// VpcAccessServiceClient is the client API for VpcAccessService service. For
// semantics around ctx use and closing/ending streaming RPCs, please refer to
// https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
//
// Deprecated: Please use types in: cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb
type VpcAccessServiceClient = src.VpcAccessServiceClient

// VpcAccessServiceServer is the server API for VpcAccessService service.
//
// Deprecated: Please use types in: cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb
type VpcAccessServiceServer = src.VpcAccessServiceServer

// Deprecated: Please use funcs in: cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb
func NewVpcAccessServiceClient(cc grpc.ClientConnInterface) VpcAccessServiceClient {
	return src.NewVpcAccessServiceClient(cc)
}

// Deprecated: Please use funcs in: cloud.google.com/go/vpcaccess/apiv1/vpcaccesspb
func RegisterVpcAccessServiceServer(s *grpc.Server, srv VpcAccessServiceServer) {
	src.RegisterVpcAccessServiceServer(s, srv)
}
