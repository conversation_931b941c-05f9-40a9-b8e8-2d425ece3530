{"src\\main\\java\\com\\fuck\\fuckinggooo\\util\\PermissionUtils.kt": ["CameraPermissionState:com.fuck.fuckinggooo.util", "hasPermission:com.fuck.fuckinggooo.util.CameraPermissionState", "requestPermission:com.fuck.fuckinggooo.util.CameraPermissionState", "hasCameraPermission:com.fuck.fuckinggooo.util", "rememberCameraPermissionState:com.fuck.fuckinggooo.util"], "src\\main\\java\\com\\fuck\\fuckinggooo\\core\\LocalSocksProxy.kt": ["port:com.fuck.fuckinggooo.core.LocalSocksProxy.TargetInfo", "<init>:com.fuck.fuckinggooo.core.LocalSocksProxy.Companion", "Companion:com.fuck.fuckinggooo.core.LocalSocksProxy", "start:com.fuck.fuckinggooo.core.LocalSocksProxy", "stop:com.fuck.fuckinggooo.core.LocalSocksProxy", "host:com.fuck.fuckinggooo.core.LocalSocksProxy.TargetInfo", "LocalSocksProxy:com.fuck.fuckinggooo.core"], "src\\main\\java\\com\\fuck\\fuckinggooo\\ui\\screen\\DashboardScreen.kt": ["ModeButton:com.fuck.fuckinggooo.ui.screen", "ConfigurationItem:com.fuck.fuckinggooo.ui.screen", "NodesGridView:com.fuck.fuckinggooo.ui.screen", "ConfigurationCard:com.fuck.fuckinggooo.ui.screen", "SpeedCard:com.fuck.fuckinggooo.ui.screen", "NodeGridItem:com.fuck.fuckinggooo.ui.screen", "StatusCard:com.fuck.fuckinggooo.ui.screen", "ConnectionCard:com.fuck.fuckinggooo.ui.screen", "NodesCard:com.fuck.fuckinggooo.ui.screen", "NodeItem:com.fuck.fuckinggooo.ui.screen", "ModeCard:com.fuck.fuckinggooo.ui.screen", "DashboardScreen:com.fuck.fuckinggooo.ui.screen", "ModeSelector:com.fuck.fuckinggooo.ui.screen", "TrafficCard:com.fuck.fuckinggooo.ui.screen"], "src\\main\\java\\com\\fuck\\fuckinggooo\\model\\ProxyNode.kt": ["host:com.fuck.fuckinggooo.model.ProxyNode", "ProxyNode:com.fuck.fuckinggooo.model", "network:com.fuck.fuckinggooo.model.ProxyNode", "path:com.fuck.fuckinggooo.model.ProxyNode", "username:com.fuck.fuckinggooo.model.ProxyNode", "serviceName:com.fuck.fuckinggooo.model.ProxyNode", "latency:com.fuck.fuckinggooo.model.ProxyNode", "port:com.fuck.fuckinggooo.model.ProxyNode", "security:com.fuck.fuckinggooo.model.ProxyNode", "tls:com.fuck.fuckinggooo.model.ProxyNode", "alterId:com.fuck.fuckinggooo.model.ProxyNode", "uuid:com.fuck.fuckinggooo.model.ProxyNode", "headerType:com.fuck.fuckinggooo.model.ProxyNode", "password:com.fuck.fuckinggooo.model.ProxyNode", "allowInsecure:com.fuck.fuckinggooo.model.ProxyNode", "name:com.fuck.fuckinggooo.model.ProxyNode", "server:com.fuck.fuckinggooo.model.ProxyNode", "protocol:com.fuck.fuckinggooo.model.ProxyNode", "id:com.fuck.fuckinggooo.model.ProxyNode", "flow:com.fuck.fuckinggooo.model.ProxyNode", "sni:com.fuck.fuckinggooo.model.ProxyNode"], "src\\main\\java\\com\\fuck\\fuckinggooo\\service\\ConfigManager.kt": ["net:com.fuck.fuckinggooo.service.ConfigManager.VmessInfo", "host:com.fuck.fuckinggooo.service.ConfigManager.VmessInfo", "type:com.fuck.fuckinggooo.service.ConfigManager.VmessInfo", "aid:com.fuck.fuckinggooo.service.ConfigManager.VmessInfo", "downloadSubscription:com.fuck.fuckinggooo.service.ConfigManager", "id:com.fuck.fuckinggooo.service.ConfigManager.VmessInfo", "parseTrojanLink:com.fuck.fuckinggooo.service.ConfigManager", "v:com.fuck.fuckinggooo.service.ConfigManager.VmessInfo", "parseSubscriptionContent:com.fuck.fuckinggooo.service.ConfigManager", "add:com.fuck.fuckinggooo.service.ConfigManager.VmessInfo", "scy:com.fuck.fuckinggooo.service.ConfigManager.VmessInfo", "generateSingBoxConfig:com.fuck.fuckinggooo.service.ConfigManager", "<init>:com.fuck.fuckinggooo.service.ConfigManager", "parseVlessLink:com.fuck.fuckinggooo.service.ConfigManager", "parseVmessLink:com.fuck.fuckinggooo.service.ConfigManager", "ps:com.fuck.fuckinggooo.service.ConfigManager.VmessInfo", "path:com.fuck.fuckinggooo.service.ConfigManager.VmessInfo", "ConfigManager:com.fuck.fuckinggooo.service", "parseShadowsocksLink:com.fuck.fuckinggooo.service.ConfigManager", "validateConfig:com.fuck.fuckinggooo.service.ConfigManager", "port:com.fuck.fuckinggooo.service.ConfigManager.VmessInfo", "tls:com.fuck.fuckinggooo.service.ConfigManager.VmessInfo"], "src\\main\\java\\com\\fuck\\fuckinggooo\\service\\LatencyTester.kt": ["testMultipleLatenciesWithCallback:com.fuck.fuckinggooo.service.LatencyTester", "<init>:com.fuck.fuckinggooo.service.LatencyTester", "testMultipleLatencies:com.fuck.fuckinggooo.service.LatencyTester", "Companion:com.fuck.fuckinggooo.service.LatencyTester", "testLatency:com.fuck.fuckinggooo.service.LatencyTester", "<init>:com.fuck.fuckinggooo.service.LatencyTester.Companion", "LatencyTester:com.fuck.fuckinggooo.service"], "src\\main\\java\\com\\fuck\\fuckinggooo\\service\\NodeManager.kt": ["NodeManager:com.fuck.fuckinggooo.service", "testNodeLatency:com.fuck.fuckinggooo.service.NodeManager", "getAllNodes:com.fuck.fuckinggooo.service.NodeManager", "selectNode:com.fuck.fuckinggooo.service.NodeManager"], "src\\main\\java\\com\\fuck\\fuckinggooo\\core\\SingBoxConfigGenerator.kt": ["<init>:com.fuck.fuckinggooo.core.SingBoxConfigGenerator", "SingBoxConfigGenerator:com.fuck.fuckinggooo.core", "generateConfig:com.fuck.fuckinggooo.core.SingBoxConfigGenerator"], "src\\main\\java\\com\\fuck\\fuckinggooo\\model\\ConnectionState.kt": ["<init>:com.fuck.fuckinggooo.model.ConnectionState.DISCONNECTED", "<init>:com.fuck.fuckinggooo.model.ConnectionState.CONNECTED", "<init>:com.fuck.fuckinggooo.model.ConnectionState.DISCONNECTING", "DISCONNECTED:com.fuck.fuckinggooo.model.ConnectionState", "<init>:com.fuck.fuckinggooo.model.ConnectionState.ERROR", "<init>:com.fuck.fuckinggooo.model.ConnectionState.CONNECTING", "ERROR:com.fuck.fuckinggooo.model.ConnectionState", "DISCONNECTING:com.fuck.fuckinggooo.model.ConnectionState", "CONNECTING:com.fuck.fuckinggooo.model.ConnectionState", "CONNECTED:com.fuck.fuckinggooo.model.ConnectionState", "ConnectionState:com.fuck.fuckinggooo.model"], "src\\main\\java\\com\\fuck\\fuckinggooo\\core\\FallbackProxy.kt": ["FallbackProxy:com.fuck.fuckinggooo.core", "start:com.fuck.fuckinggooo.core.FallbackProxy"], "src\\main\\java\\com\\fuck\\fuckinggooo\\service\\VpnManager.kt": ["<init>:com.fuck.fuckinggooo.service.VpnManager.VpnState.CONNECTED", "totalDownload:com.fuck.fuckinggooo.service.VpnManager.ConnectionStats", "CONNECTING:com.fuck.fuckinggooo.service.VpnManager.VpnState", "Companion:com.fuck.fuckinggooo.service.VpnManager", "cleanup:com.fuck.fuckinggooo.service.VpnManager", "ERROR:com.fuck.fuckinggooo.service.VpnManager.VpnState", "totalUpload:com.fuck.fuckinggooo.service.VpnManager.ConnectionStats", "startVpn:com.fuck.fuckinggooo.service.VpnManager", "stopVpn:com.fuck.fuckinggooo.service.VpnManager", "DISCONNECTING:com.fuck.fuckinggooo.service.VpnManager.VpnState", "uploadSpeed:com.fuck.fuckinggooo.service.VpnManager.ConnectionStats", "VpnState:com.fuck.fuckinggooo.service.VpnManager", "switchVpn:com.fuck.fuckinggooo.service.VpnManager", "ConnectionStats:com.fuck.fuckinggooo.service.VpnManager", "<init>:com.fuck.fuckinggooo.service.VpnManager.VpnState.CONNECTING", "CONNECTED:com.fuck.fuckinggooo.service.VpnManager.VpnState", "downloadSpeed:com.fuck.fuckinggooo.service.VpnManager.ConnectionStats", "activeConnections:com.fuck.fuckinggooo.service.VpnManager.ConnectionStats", "getCurrentState:com.fuck.fuckinggooo.service.VpnManager", "currentNode:com.fuck.fuckinggooo.service.VpnManager", "<init>:com.fuck.fuckinggooo.service.VpnManager.VpnState.DISCONNECTING", "<init>:com.fuck.fuckinggooo.service.VpnManager.VpnState.ERROR", "<init>:com.fuck.fuckinggooo.service.VpnManager.VpnState.DISCONNECTED", "connectionStats:com.fuck.fuckinggooo.service.VpnManager", "checkVpnPermission:com.fuck.fuckinggooo.service.VpnManager", "vpnState:com.fuck.fuckinggooo.service.VpnManager", "VpnManager:com.fuck.fuckinggooo.service", "<init>:com.fuck.fuckinggooo.service.VpnManager.Companion", "isConnected:com.fuck.fuckinggooo.service.VpnManager", "getInstance:com.fuck.fuckinggooo.service.VpnManager.Companion", "DISCONNECTED:com.fuck.fuckinggooo.service.VpnManager.VpnState", "connectedTime:com.fuck.fuckinggooo.service.VpnManager.ConnectionStats"], "src\\main\\java\\com\\fuck\\fuckinggooo\\model\\ImportResult.kt": ["success:com.fuck.fuckinggooo.model.ImportResult", "errorMessage:com.fuck.fuckinggooo.model.ImportResult", "nodeCount:com.fuck.fuckinggooo.model.ImportResult", "ImportResult:com.fuck.fuckinggooo.model"], "src\\main\\java\\com\\fuck\\fuckinggooo\\core\\PacketBuilder.kt": ["buildTcpHeader:com.fuck.fuckinggooo.core.PacketBuilder", "<init>:com.fuck.fuckinggooo.core.PacketBuilder", "buildTcpResponsePacket:com.fuck.fuckinggooo.core.PacketBuilder", "PacketBuilder:com.fuck.fuckinggooo.core", "buildUdpHeader:com.fuck.fuckinggooo.core.PacketBuilder", "buildUdpResponsePacket:com.fuck.fuckinggooo.core.PacketBuilder", "buildIpv4Header:com.fuck.fuckinggooo.core.PacketBuilder"], "src\\main\\java\\com\\fuck\\fuckinggooo\\ui\\dashboard\\DashboardViewModel.kt": ["DashboardViewModel:com.fuck.fuckinggooo.ui.dashboard", "statusMessage:com.fuck.fuckinggooo.ui.dashboard.DashboardViewModel", "proxyMode:com.fuck.fuckinggooo.ui.dashboard.DashboardViewModel", "selectedNode:com.fuck.fuckinggooo.ui.dashboard.DashboardViewModel", "isRunning:com.fuck.fuckinggooo.ui.dashboard.DashboardViewModel", "toggleIsRunning:com.fuck.fuckinggooo.ui.dashboard.DashboardViewModel"], "src\\main\\java\\com\\fuck\\fuckinggooo\\core\\SimpleTunToProxy.kt": ["proxySocket:com.fuck.fuckinggooo.core.SimpleTunToProxy.TcpConnection", "Companion:com.fuck.fuckinggooo.core.SimpleTunToProxy", "sourceIp:com.fuck.fuckinggooo.core.SimpleTunToProxy.IpHeader", "TcpConnection:com.fuck.fuckinggooo.core.SimpleTunToProxy", "acknowledgmentNumber:com.fuck.fuckinggooo.core.SimpleTunToProxy.TcpHeader", "TcpHeader:com.fuck.fuckinggooo.core.SimpleTunToProxy", "destPort:com.fuck.fuckinggooo.core.SimpleTunToProxy.TcpConnection", "destIp:com.fuck.fuckinggooo.core.SimpleTunToProxy.IpHeader", "sourcePort:com.fuck.fuckinggooo.core.SimpleTunToProxy.TcpHeader", "SimpleTunToProxy:com.fuck.fuckinggooo.core", "headerLength:com.fuck.fuckinggooo.core.SimpleTunToProxy.TcpHeader", "destPort:com.fuck.fuckinggooo.core.SimpleTunToProxy.TcpHeader", "stop:com.fuck.fuckinggooo.core.SimpleTunToProxy", "sourcePort:com.fuck.fuckinggooo.core.SimpleTunToProxy.TcpConnection", "sourceIp:com.fuck.fuckinggooo.core.SimpleTunToProxy.TcpConnection", "version:com.fuck.fuckinggooo.core.SimpleTunToProxy.IpHeader", "destIp:com.fuck.fuckinggooo.core.SimpleTunToProxy.TcpConnection", "protocol:com.fuck.fuckinggooo.core.SimpleTunToProxy.IpHeader", "<init>:com.fuck.fuckinggooo.core.SimpleTunToProxy.Companion", "IpHeader:com.fuck.fuckinggooo.core.SimpleTunToProxy", "headerLength:com.fuck.fuckinggooo.core.SimpleTunToProxy.IpHeader", "sequenceNumber:com.fuck.fuckinggooo.core.SimpleTunToProxy.TcpHeader", "lastActivity:com.fuck.fuckinggooo.core.SimpleTunToProxy.TcpConnection", "flags:com.fuck.fuckinggooo.core.SimpleTunToProxy.TcpHeader", "start:com.fuck.fuckinggooo.core.SimpleTunToProxy"], "src\\main\\java\\com\\fuck\\fuckinggooo\\core\\SimpleHttpProxy.kt": ["<init>:com.fuck.fuckinggooo.core.SimpleHttpProxy.Companion", "start:com.fuck.fuckinggooo.core.SimpleHttpProxy", "SimpleHttpProxy:com.fuck.fuckinggooo.core", "Companion:com.fuck.fuckinggooo.core.SimpleHttpProxy", "stop:com.fuck.fuckinggooo.core.SimpleHttpProxy"], "src\\main\\java\\com\\fuck\\fuckinggooo\\ui\\activity\\QrScanActivity.kt": ["QrScanActivity:com.fuck.fuckinggooo.ui.activity", "Companion:com.fuck.fuckinggooo.ui.activity.QrScanActivity", "<init>:com.fuck.fuckinggooo.ui.activity.QrScanActivity", "onCreate:com.fuck.fuckinggooo.ui.activity.QrScanActivity", "onPause:com.fuck.fuckinggooo.ui.activity.QrScanActivity", "RESULT_QR_CONTENT:com.fuck.fuckinggooo.ui.activity.QrScanActivity.Companion", "<init>:com.fuck.fuckinggooo.ui.activity.QrScanActivity.Companion", "onResume:com.fuck.fuckinggooo.ui.activity.QrScanActivity", "QrScanScreen:com.fuck.fuckinggooo.ui.activity"], "src\\main\\java\\com\\fuck\\fuckinggooo\\core\\SingBoxCore.kt": ["Companion:com.fuck.fuckinggooo.core.SingBoxCore", "start:com.fuck.fuckinggooo.core.SingBoxCore", "isRunning:com.fuck.fuckinggooo.core.SingBoxCore", "<init>:com.fuck.fuckinggooo.core.SingBoxCore.Companion", "stop:com.fuck.fuckinggooo.core.SingBoxCore", "getHttpPort:com.fuck.fuckinggooo.core.SingBoxCore", "getSocksPort:com.fuck.fuckinggooo.core.SingBoxCore", "getVersion:com.fuck.fuckinggooo.core.SingBoxCore", "cleanup:com.fuck.fuckinggooo.core.SingBoxCore", "SingBoxCore:com.fuck.fuckinggooo.core", "testConnection:com.fuck.fuckinggooo.core.SingBoxCore"], "src\\main\\java\\com\\fuck\\fuckinggooo\\ui\\theme\\Type.kt": ["Typography:com.fuck.fuckinggooo.ui.theme"], "src\\main\\java\\com\\fuck\\fuckinggooo\\ui\\screen\\SettingsScreen.kt": ["SettingsInfo:com.fuck.fuckinggooo.ui.screen", "SettingsDropdown:com.fuck.fuckinggooo.ui.screen", "SettingsScreen:com.fuck.fuckinggooo.ui.screen", "SettingsCard:com.fuck.fuckinggooo.ui.screen", "SettingsAction:com.fuck.fuckinggooo.ui.screen"], "src\\main\\java\\com\\fuck\\fuckinggooo\\viewmodel\\MainViewModel.kt": ["nodes:com.fuck.fuckinggooo.viewmodel.MainViewModel", "isConnected:com.fuck.fuckinggooo.viewmodel.MainViewModel", "refreshNodes:com.fuck.fuckinggooo.viewmodel.MainViewModel", "clearError:com.fuck.fuckinggooo.viewmodel.MainViewModel", "connectionStats:com.fuck.fuckinggooo.viewmodel.MainViewModel", "startVpn:com.fuck.fuckinggooo.viewmodel.MainViewModel", "stopVpn:com.fuck.fuckinggooo.viewmodel.MainViewModel", "MainViewModel:com.fuck.fuckinggooo.viewmodel", "switchVpn:com.fuck.fuckinggooo.viewmodel.MainViewModel", "clearVpnPermissionIntent:com.fuck.fuckinggooo.viewmodel.MainViewModel", "onCleared:com.fuck.fuckinggooo.viewmodel.MainViewModel", "errorMessage:com.fuck.fuckinggooo.viewmodel.MainViewModel", "selectedNode:com.fuck.fuckinggooo.viewmodel.MainViewModel", "connectionState:com.fuck.fuckinggooo.viewmodel.MainViewModel", "vpnPermissionIntent:com.fuck.fuckinggooo.viewmodel.MainViewModel", "checkVpnPermission:com.fuck.fuckinggooo.viewmodel.MainViewModel", "toggleConnection:com.fuck.fuckinggooo.viewmodel.MainViewModel"], "src\\main\\java\\com\\fuck\\fuckinggooo\\core\\EnhancedFallbackProxy.kt": ["EnhancedFallbackProxy:com.fuck.fuckinggooo.core", "stop:com.fuck.fuckinggooo.core.EnhancedFallbackProxy", "start:com.fuck.fuckinggooo.core.EnhancedFallbackProxy", "<init>:com.fuck.fuckinggooo.core.EnhancedFallbackProxy", "isRunning:com.fuck.fuckinggooo.core.EnhancedFallbackProxy"], "src\\main\\java\\com\\fuck\\fuckinggooo\\model\\AppDatabase.kt": ["AppDatabase:com.fuck.fuckinggooo.model", "Companion:com.fuck.fuckinggooo.model.AppDatabase", "proxyNodeDao:com.fuck.fuckinggooo.model.AppDatabase", "<init>:com.fuck.fuckinggooo.model.AppDatabase", "getDatabase:com.fuck.fuckinggooo.model.AppDatabase.Companion", "<init>:com.fuck.fuckinggooo.model.AppDatabase.Companion", "proxyConfigDao:com.fuck.fuckinggooo.model.AppDatabase", "MIGRATION_1_2:com.fuck.fuckinggooo.model.AppDatabase.Companion", "MIGRATION_2_3:com.fuck.fuckinggooo.model.AppDatabase.Companion"], "src\\main\\java\\com\\fuck\\fuckinggooo\\viewmodel\\NodeViewModel.kt": ["nodes:com.fuck.fuckinggooo.viewmodel.NodeViewModel", "NodeViewModel:com.fuck.fuckinggooo.viewmodel", "testNodeLatency:com.fuck.fuckinggooo.viewmodel.NodeViewModel", "loadNodes:com.fuck.fuckinggooo.viewmodel.NodeViewModel"], "src\\main\\java\\com\\fuck\\fuckinggooo\\viewmodel\\ConfigViewModel.kt": ["importResult:com.fuck.fuckinggooo.viewmodel.ConfigViewModel", "addConfig:com.fuck.fuckinggooo.viewmodel.ConfigViewModel", "updateConfig:com.fuck.fuckinggooo.viewmodel.ConfigViewModel", "updateConfigNameAndUrl:com.fuck.fuckinggooo.viewmodel.ConfigViewModel", "validateAndImportBatch:com.fuck.fuckinggooo.viewmodel.ConfigViewModel", "deleteConfig:com.fuck.fuckinggooo.viewmodel.ConfigViewModel", "ConfigViewModel:com.fuck.fuckinggooo.viewmodel", "parseConfig:com.fuck.fuckinggooo.viewmodel.ConfigViewModel", "saveConfig:com.fuck.fuckinggooo.viewmodel.ConfigViewModel", "importSubscription:com.fuck.fuckinggooo.viewmodel.ConfigViewModel", "importFromQrCode:com.fuck.fuckinggooo.viewmodel.ConfigViewModel", "importFromFile:com.fuck.fuckinggooo.viewmodel.ConfigViewModel", "importFromClipboard:com.fuck.fuckinggooo.viewmodel.ConfigViewModel", "allConfigs:com.fuck.fuckinggooo.viewmodel.ConfigViewModel", "testLatency:com.fuck.fuckinggooo.viewmodel.ConfigViewModel", "updateConfigName:com.fuck.fuckinggooo.viewmodel.ConfigViewModel", "createManualConfig:com.fuck.fuckinggooo.viewmodel.ConfigViewModel", "testNodeLatency:com.fuck.fuckinggooo.viewmodel.ConfigViewModel", "saveConfigAsync:com.fuck.fuckinggooo.viewmodel.ConfigViewModel", "selectNode:com.fuck.fuckinggooo.viewmodel.ConfigViewModel"], "src\\main\\java\\com\\fuck\\fuckinggooo\\core\\LibcoreManager.kt": ["isBoxRunning:com.fuck.fuckinggooo.core.LibcoreManager", "getVersion:com.fuck.fuckinggooo.core.LibcoreManager", "getSocksPort:com.fuck.fuckinggooo.core.LibcoreManager", "getHttpPort:com.fuck.fuckinggooo.core.LibcoreManager", "isRunning:com.fuck.fuckinggooo.core.LibcoreManager", "stopSingBox:com.fuck.fuckinggooo.core.LibcoreManager", "cleanup:com.fuck.fuckinggooo.core.LibcoreManager", "LibcoreManager:com.fuck.fuckinggooo.core", "testConnection:com.fuck.fuckinggooo.core.LibcoreManager", "startSingBox:com.fuck.fuckinggooo.core.LibcoreManager", "<init>:com.fuck.fuckinggooo.core.LibcoreManager"], "src\\main\\java\\com\\fuck\\fuckinggooo\\viewmodel\\ViewModelFactory.kt": ["ViewModelFactory:com.fuck.fuckinggooo.viewmodel", "create:com.fuck.fuckinggooo.viewmodel.ViewModelFactory"], "src\\main\\java\\com\\fuck\\fuckinggooo\\viewmodel\\DashboardViewModel.kt": ["selectConfig:com.fuck.fuckinggooo.viewmodel.DashboardViewModel", "currentNode:com.fuck.fuckinggooo.viewmodel.ConnectionStats", "<init>:com.fuck.fuckinggooo.viewmodel.ProxyMode.RULE", "totalUpload:com.fuck.fuckinggooo.viewmodel.ConnectionStats", "<init>:com.fuck.fuckinggooo.viewmodel.ProxyMode.GLOBAL", "inboundConnections:com.fuck.fuckinggooo.viewmodel.ConnectionStats", "setMode:com.fuck.fuckinggooo.viewmodel.DashboardViewModel", "goroutines:com.fuck.fuckinggooo.viewmodel.ConnectionStats", "startVpnAfterPermission:com.fuck.fuckinggooo.viewmodel.DashboardViewModel", "ProxyMode:com.fuck.fuckinggooo.viewmodel", "DashboardViewModel:com.fuck.fuckinggooo.viewmodel", "clearSelectedConfig:com.fuck.fuckinggooo.viewmodel.DashboardViewModel", "selectNode:com.fuck.fuckinggooo.viewmodel.DashboardViewModel", "uploadSpeed:com.fuck.fuckinggooo.viewmodel.ConnectionStats", "isRunning:com.fuck.fuckinggooo.viewmodel.DashboardViewModel", "serverIP:com.fuck.fuckinggooo.viewmodel.ConnectionStats", "ConnectionStats:com.fuck.fuckinggooo.viewmodel", "currentMode:com.fuck.fuckinggooo.viewmodel.DashboardViewModel", "memoryUsage:com.fuck.fuckinggooo.viewmodel.ConnectionStats", "testCurrentConfigLatency:com.fuck.fuckinggooo.viewmodel.DashboardViewModel", "testSingleNodeLatency:com.fuck.fuckinggooo.viewmodel.DashboardViewModel", "connectionStats:com.fuck.fuckinggooo.viewmodel.DashboardViewModel", "DIRECT:com.fuck.fuckinggooo.viewmodel.ProxyMode", "isTestingLatency:com.fuck.fuckinggooo.viewmodel.DashboardViewModel", "RULE:com.fuck.fuckinggooo.viewmodel.ProxyMode", "<init>:com.fuck.fuckinggooo.viewmodel.ProxyMode.DIRECT", "downloadSpeed:com.fuck.fuckinggooo.viewmodel.ConnectionStats", "toggleIsRunning:com.fuck.fuckinggooo.viewmodel.DashboardViewModel", "selectedConfig:com.fuck.fuckinggooo.viewmodel.DashboardViewModel", "allConfigs:com.fuck.fuckinggooo.viewmodel.DashboardViewModel", "GLOBAL:com.fuck.fuckinggooo.viewmodel.ProxyMode", "outboundConnections:com.fuck.fuckinggooo.viewmodel.ConnectionStats", "totalDownload:com.fuck.fuckinggooo.viewmodel.ConnectionStats"], "src\\main\\java\\com\\fuck\\fuckinggooo\\model\\ProxyConfigDao.kt": ["getAll:com.fuck.fuckinggooo.model.ProxyConfigDao", "deleteByUrl:com.fuck.fuckinggooo.model.ProxyConfigDao", "insert:com.fuck.fuckinggooo.model.ProxyConfigDao", "update:com.fuck.fuckinggooo.model.ProxyConfigDao", "ProxyConfigDao:com.fuck.fuckinggooo.model", "getByUrl:com.fuck.fuckinggooo.model.ProxyConfigDao", "delete:com.fuck.fuckinggooo.model.ProxyConfigDao"], "src\\main\\java\\com\\fuck\\fuckinggooo\\service\\ProxyVpnService.kt": ["Companion:com.fuck.fuckinggooo.service.ProxyVpnService", "EXTRA_STATUS:com.fuck.fuckinggooo.service.ProxyVpnService.Companion", "STATUS_DISCONNECTED:com.fuck.fuckinggooo.service.ProxyVpnService.Companion", "STATUS_CONNECTING:com.fuck.fuckinggooo.service.ProxyVpnService.Companion", "STATUS_ERROR:com.fuck.fuckinggooo.service.ProxyVpnService.Companion", "EXTRA_PROXY_MODE:com.fuck.fuckinggooo.service.ProxyVpnService.Companion", "VPN_STATUS_ACTION:com.fuck.fuckinggooo.service.ProxyVpnService.Companion", "STATUS_DISCONNECTING:com.fuck.fuckinggooo.service.ProxyVpnService.Companion", "onDestroy:com.fuck.fuckinggooo.service.ProxyVpnService", "ACTION_START:com.fuck.fuckinggooo.service.ProxyVpnService.Companion", "<init>:com.fuck.fuckinggooo.service.ProxyVpnService.Companion", "onStartCommand:com.fuck.fuckinggooo.service.ProxyVpnService", "STATUS_CONNECTED:com.fuck.fuckinggooo.service.ProxyVpnService.Companion", "EXTRA_ERROR_MESSAGE:com.fuck.fuckinggooo.service.ProxyVpnService.Companion", "onRevoke:com.fuck.fuckinggooo.service.ProxyVpnService", "ProxyVpnService:com.fuck.fuckinggooo.service", "EXTRA_PROXY_NODE_JSON:com.fuck.fuckinggooo.service.ProxyVpnService.Companion", "<init>:com.fuck.fuckinggooo.service.ProxyVpnService", "ACTION_STOP:com.fuck.fuckinggooo.service.ProxyVpnService.Companion"], "src\\main\\java\\com\\fuck\\fuckinggooo\\model\\ProxyConfig.kt": ["ProxyConfig:com.fuck.fuckinggooo.model", "name:com.fuck.fuckinggooo.model.ProxyConfig", "selectedNodeId:com.fuck.fuckinggooo.model.ProxyConfig", "lastUpdate:com.fuck.fuckinggooo.model.ProxyConfig", "nodes:com.fuck.fuckinggooo.model.ProxyConfig", "subscriptionUrl:com.fuck.fuckinggooo.model.ProxyConfig"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\fuck\\fuckinggooo\\model\\AppDatabase_Impl.java": ["AppDatabase_Impl:com.fuck.fuckinggooo.model", "proxyConfigDao:com.fuck.fuckinggooo.model.AppDatabase_Impl", "createOpenHelper:com.fuck.fuckinggooo.model.AppDatabase_Impl", "clearAllTables:com.fuck.fuckinggooo.model.AppDatabase_Impl", "getRequiredAutoMigrationSpecs:com.fuck.fuckinggooo.model.AppDatabase_Impl", "getRequiredTypeConverters:com.fuck.fuckinggooo.model.AppDatabase_Impl", "createInvalidationTracker:com.fuck.fuckinggooo.model.AppDatabase_Impl", "<init>:com.fuck.fuckinggooo.model.AppDatabase_Impl", "getAutoMigrations:com.fuck.fuckinggooo.model.AppDatabase_Impl", "proxyNodeDao:com.fuck.fuckinggooo.model.AppDatabase_Impl"], "src\\main\\java\\com\\fuck\\fuckinggooo\\MainActivity.kt": ["<init>:com.fuck.fuckinggooo.Screen.Settings", "onCreate:com.fuck.fuckinggooo.MainActivity", "<init>:com.fuck.fuckinggooo.Screen.Logs", "route:com.fuck.fuckinggooo.Screen", "Logs:com.fuck.fuckinggooo.Screen", "<init>:com.fuck.fuckinggooo.Screen.Dashboard", "MainActivity:com.fuck.fuckinggooo", "<init>:com.fuck.fuckinggooo.Screen.Config", "MainScreenWithNav:com.fuck.fuckinggooo", "Dashboard:com.fuck.fuckinggooo.Screen", "icon:com.fuck.fuckinggooo.Screen", "<init>:com.fuck.fuckinggooo.MainActivity", "Config:com.fuck.fuckinggooo.Screen", "Screen:com.fuck.fuckinggooo", "Settings:com.fuck.fuckinggooo.Screen"], "src\\main\\java\\com\\fuck\\fuckinggooo\\ui\\screen\\ConfigScreen.kt": ["ConfigOptionItem:com.fuck.fuckinggooo.ui.screen", "NodeItem:com.fuck.fuckinggooo.ui.screen", "ConfigListItem:com.fuck.fuckinggooo.ui.screen", "EmptyConfigState:com.fuck.fuckinggooo.ui.screen", "ConfigScreen:com.fuck.fuckinggooo.ui.screen", "AddConfigBottomSheet:com.fuck.fuckinggooo.ui.screen", "EditConfigDialog:com.fuck.fuckinggooo.ui.screen", "ConfigCard:com.fuck.fuckinggooo.ui.screen"], "src\\main\\java\\com\\fuck\\fuckinggooo\\ui\\theme\\Theme.kt": ["FuckingGoooTheme:com.fuck.fuckinggooo.ui.theme"], "src\\main\\java\\com\\fuck\\fuckinggooo\\model\\Converters.kt": ["<init>:com.fuck.fuckinggooo.model.Converters", "Converters:com.fuck.fuckinggooo.model", "toNodeList:com.fuck.fuckinggooo.model.Converters", "fromNodeList:com.fuck.fuckinggooo.model.Converters"], "src\\main\\java\\com\\fuck\\fuckinggooo\\ui\\theme\\Color.kt": ["Purple80:com.fuck.fuckinggooo.ui.theme", "PurpleGrey40:com.fuck.fuckinggooo.ui.theme", "Pink80:com.fuck.fuckinggooo.ui.theme", "PurpleGrey80:com.fuck.fuckinggooo.ui.theme", "Pink40:com.fuck.fuckinggooo.ui.theme", "Purple40:com.fuck.fuckinggooo.ui.theme"], "src\\main\\java\\com\\fuck\\fuckinggooo\\ui\\screen\\LogsScreen.kt": ["LogsScreen:com.fuck.fuckinggooo.ui.screen", "LogCard:com.fuck.fuckinggooo.ui.screen", "content:com.fuck.fuckinggooo.ui.screen.LogEntry", "LogEntry:com.fuck.fuckinggooo.ui.screen", "level:com.fuck.fuckinggooo.ui.screen.LogEntry"], "src\\main\\java\\com\\fuck\\fuckinggooo\\jni\\SingBoxJNI.kt": ["getVersion:com.fuck.fuckinggooo.jni.SingBoxJNI", "getConnectionStats:com.fuck.fuckinggooo.jni.SingBoxJNI", "<init>:com.fuck.fuckinggooo.jni.SingBoxJNI", "SingBoxJNI:com.fuck.fuckinggooo.jni", "startCore:com.fuck.fuckinggooo.jni.SingBoxJNI", "isRunning:com.fuck.fuckinggooo.jni.SingBoxJNI", "stopCore:com.fuck.fuckinggooo.jni.SingBoxJNI"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\fuck\\fuckinggooo\\model\\ProxyNodeDao_Impl.java": ["deleteAll:com.fuck.fuckinggooo.model.ProxyNodeDao_Impl", "ProxyNodeDao_Impl:com.fuck.fuckinggooo.model", "getRequiredConverters:com.fuck.fuckinggooo.model.ProxyNodeDao_Impl", "getById:com.fuck.fuckinggooo.model.ProxyNodeDao_Impl", "<init>:com.fuck.fuckinggooo.model.ProxyNodeDao_Impl", "getAll:com.fuck.fuckinggooo.model.ProxyNodeDao_Impl", "insertAll:com.fuck.fuckinggooo.model.ProxyNodeDao_Impl"], "src\\main\\java\\com\\fuck\\fuckinggooo\\service\\ProxyCore.kt": ["type:com.fuck.fuckinggooo.service.ProxyCore.ProxyConfig", "stop:com.fuck.fuckinggooo.service.ProxyCore", "network:com.fuck.fuckinggooo.service.ProxyCore.ProxyConfig", "security:com.fuck.fuckinggooo.service.ProxyCore.ProxyConfig", "port:com.fuck.fuckinggooo.service.ProxyCore.ProxyConfig", "ProxyCore:com.fuck.fuckinggooo.service", "server:com.fuck.fuckinggooo.service.ProxyCore.ProxyConfig", "uuid:com.fuck.fuckinggooo.service.ProxyCore.ProxyConfig", "<init>:com.fuck.fuckinggooo.service.ProxyCore", "ProxyConfig:com.fuck.fuckinggooo.service.ProxyCore", "password:com.fuck.fuckinggooo.service.ProxyCore.ProxyConfig", "method:com.fuck.fuckinggooo.service.ProxyCore.ProxyConfig", "start:com.fuck.fuckinggooo.service.ProxyCore"], "src\\main\\java\\com\\fuck\\fuckinggooo\\core\\TunToSocks.kt": ["destPort:com.fuck.fuckinggooo.core.TunToSocks.UdpHeader", "UdpHeader:com.fuck.fuckinggooo.core.TunToSocks", "<init>:com.fuck.fuckinggooo.core.TunToSocks.Companion", "destHost:com.fuck.fuckinggooo.core.TunToSocks.UdpSession", "stop:com.fuck.fuckinggooo.core.TunToSocks", "destPort:com.fuck.fuckinggooo.core.TunToSocks.TcpHeader", "lastActivity:com.fuck.fuckinggooo.core.TunToSocks.TcpConnection", "TcpHeader:com.fuck.fuckinggooo.core.TunToSocks", "proxySocket:com.fuck.fuckinggooo.core.TunToSocks.TcpConnection", "sourcePort:com.fuck.fuckinggooo.core.TunToSocks.UdpHeader", "UdpSession:com.fuck.fuckinggooo.core.TunToSocks", "protocol:com.fuck.fuckinggooo.core.TunToSocks.IpHeader", "totalLength:com.fuck.fuckinggooo.core.TunToSocks.IpHeader", "acknowledgmentNumber:com.fuck.fuckinggooo.core.TunToSocks.TcpHeader", "flags:com.fuck.fuckinggooo.core.TunToSocks.TcpHeader", "Companion:com.fuck.fuckinggooo.core.TunToSocks", "sequenceNumber:com.fuck.fuckinggooo.core.TunToSocks.TcpHeader", "lastActivity:com.fuck.fuckinggooo.core.TunToSocks.UdpSession", "start:com.fuck.fuckinggooo.core.TunToSocks", "headerLength:com.fuck.fuckinggooo.core.TunToSocks.TcpHeader", "destIp:com.fuck.fuckinggooo.core.TunToSocks.IpHeader", "sourcePort:com.fuck.fuckinggooo.core.TunToSocks.UdpSession", "headerLength:com.fuck.fuckinggooo.core.TunToSocks.IpHeader", "destHost:com.fuck.fuckinggooo.core.TunToSocks.TcpConnection", "TcpConnection:com.fuck.fuckinggooo.core.TunToSocks", "sourcePort:com.fuck.fuckinggooo.core.TunToSocks.TcpHeader", "length:com.fuck.fuckinggooo.core.TunToSocks.UdpHeader", "TunToSocks:com.fuck.fuckinggooo.core", "sourcePort:com.fuck.fuckinggooo.core.TunToSocks.TcpConnection", "socket:com.fuck.fuckinggooo.core.TunToSocks.UdpSession", "destPort:com.fuck.fuckinggooo.core.TunToSocks.UdpSession", "sourceIp:com.fuck.fuckinggooo.core.TunToSocks.IpHeader", "clientSocket:com.fuck.fuckinggooo.core.TunToSocks.TcpConnection", "IpHeader:com.fuck.fuckinggooo.core.TunToSocks", "destPort:com.fuck.fuckinggooo.core.TunToSocks.TcpConnection", "version:com.fuck.fuckinggooo.core.TunToSocks.IpHeader"], "src\\main\\java\\com\\fuck\\fuckinggooo\\core\\DirectSocksProxy.kt": ["isRunning:com.fuck.fuckinggooo.core.DirectSocksProxy", "Companion:com.fuck.fuckinggooo.core.DirectSocksProxy", "<init>:com.fuck.fuckinggooo.core.DirectSocksProxy.Companion", "DirectSocksProxy:com.fuck.fuckinggooo.core", "port:com.fuck.fuckinggooo.core.DirectSocksProxy.TargetInfo", "stop:com.fuck.fuckinggooo.core.DirectSocksProxy", "setProxyNode:com.fuck.fuckinggooo.core.DirectSocksProxy", "start:com.fuck.fuckinggooo.core.DirectSocksProxy", "host:com.fuck.fuckinggooo.core.DirectSocksProxy.TargetInfo"], "src\\main\\java\\com\\fuck\\fuckinggooo\\ui\\screen\\ConfigAddScreen.kt": ["SUBSCRIPTION:com.fuck.fuckinggooo.ui.screen.ConfigType", "ConfigTypeSelector:com.fuck.fuckinggooo.ui.screen", "MANUAL:com.fuck.fuckinggooo.ui.screen.ConfigType", "<init>:com.fuck.fuckinggooo.ui.screen.ConfigType.MANUAL", "ConfigAddScreen:com.fuck.fuckinggooo.ui.screen", "<init>:com.fuck.fuckinggooo.ui.screen.ConfigType.SUBSCRIPTION", "SubscriptionConfigForm:com.fuck.fuckinggooo.ui.screen", "ConfigType:com.fuck.fuckinggooo.ui.screen", "ManualConfigForm:com.fuck.fuckinggooo.ui.screen"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\fuck\\fuckinggooo\\model\\ProxyConfigDao_Impl.java": ["delete:com.fuck.fuckinggooo.model.ProxyConfigDao_Impl", "getByUrl:com.fuck.fuckinggooo.model.ProxyConfigDao_Impl", "<init>:com.fuck.fuckinggooo.model.ProxyConfigDao_Impl", "deleteByUrl:com.fuck.fuckinggooo.model.ProxyConfigDao_Impl", "ProxyConfigDao_Impl:com.fuck.fuckinggooo.model", "getAll:com.fuck.fuckinggooo.model.ProxyConfigDao_Impl", "getRequiredConverters:com.fuck.fuckinggooo.model.ProxyConfigDao_Impl", "update:com.fuck.fuckinggooo.model.ProxyConfigDao_Impl", "insert:com.fuck.fuckinggooo.model.ProxyConfigDao_Impl"], "src\\main\\java\\com\\fuck\\fuckinggooo\\model\\ProxyNodeDao.kt": ["ProxyNodeDao:com.fuck.fuckinggooo.model", "getById:com.fuck.fuckinggooo.model.ProxyNodeDao", "getAll:com.fuck.fuckinggooo.model.ProxyNodeDao", "deleteAll:com.fuck.fuckinggooo.model.ProxyNodeDao", "insertAll:com.fuck.fuckinggooo.model.ProxyNodeDao"]}