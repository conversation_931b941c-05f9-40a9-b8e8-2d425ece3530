// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.12.2
// source: google/maps/routes/v1/polyline.proto

package routes

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// A set of values that specify the quality of the polyline.
type PolylineQuality int32

const (
	// No polyline quality preference specified. Defaults to `OVERVIEW`.
	PolylineQuality_POLYLINE_QUALITY_UNSPECIFIED PolylineQuality = 0
	// Specifies a high-quality polyline - which is composed using more points
	// than `OVERVIEW`, at the cost of increased response size. Use this value
	// when you need more precision.
	PolylineQuality_HIGH_QUALITY PolylineQuality = 1
	// Specifies an overview polyline - which is composed using a small number of
	// points. Use this value when displaying an overview of the route. Using this
	// option has a lower request latency compared to using the
	// `HIGH_QUALITY` option.
	PolylineQuality_OVERVIEW PolylineQuality = 2
)

// Enum value maps for PolylineQuality.
var (
	PolylineQuality_name = map[int32]string{
		0: "POLYLINE_QUALITY_UNSPECIFIED",
		1: "HIGH_QUALITY",
		2: "OVERVIEW",
	}
	PolylineQuality_value = map[string]int32{
		"POLYLINE_QUALITY_UNSPECIFIED": 0,
		"HIGH_QUALITY":                 1,
		"OVERVIEW":                     2,
	}
)

func (x PolylineQuality) Enum() *PolylineQuality {
	p := new(PolylineQuality)
	*p = x
	return p
}

func (x PolylineQuality) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PolylineQuality) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_routes_v1_polyline_proto_enumTypes[0].Descriptor()
}

func (PolylineQuality) Type() protoreflect.EnumType {
	return &file_google_maps_routes_v1_polyline_proto_enumTypes[0]
}

func (x PolylineQuality) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PolylineQuality.Descriptor instead.
func (PolylineQuality) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_routes_v1_polyline_proto_rawDescGZIP(), []int{0}
}

// Specifies the preferred type of polyline to be returned.
type PolylineEncoding int32

const (
	// No polyline type preference specified. Defaults to `ENCODED_POLYLINE`.
	PolylineEncoding_POLYLINE_ENCODING_UNSPECIFIED PolylineEncoding = 0
	// Specifies a polyline encoded using the [polyline encoding
	// algorithm](https://developers.google.com/maps/documentation/utilities/polylinealgorithm).
	PolylineEncoding_ENCODED_POLYLINE PolylineEncoding = 1
	// Specifies a polyline using the [GeoJSON LineString
	// format](https://tools.ietf.org/html/rfc7946#section-3.1.4)
	PolylineEncoding_GEO_JSON_LINESTRING PolylineEncoding = 2
)

// Enum value maps for PolylineEncoding.
var (
	PolylineEncoding_name = map[int32]string{
		0: "POLYLINE_ENCODING_UNSPECIFIED",
		1: "ENCODED_POLYLINE",
		2: "GEO_JSON_LINESTRING",
	}
	PolylineEncoding_value = map[string]int32{
		"POLYLINE_ENCODING_UNSPECIFIED": 0,
		"ENCODED_POLYLINE":              1,
		"GEO_JSON_LINESTRING":           2,
	}
)

func (x PolylineEncoding) Enum() *PolylineEncoding {
	p := new(PolylineEncoding)
	*p = x
	return p
}

func (x PolylineEncoding) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PolylineEncoding) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_routes_v1_polyline_proto_enumTypes[1].Descriptor()
}

func (PolylineEncoding) Type() protoreflect.EnumType {
	return &file_google_maps_routes_v1_polyline_proto_enumTypes[1]
}

func (x PolylineEncoding) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PolylineEncoding.Descriptor instead.
func (PolylineEncoding) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_routes_v1_polyline_proto_rawDescGZIP(), []int{1}
}

// Encapsulates an encoded polyline.
type Polyline struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Encapsulates the type of polyline. Defaults to encoded_polyline.
	//
	// Types that are assignable to PolylineType:
	//	*Polyline_EncodedPolyline
	//	*Polyline_GeoJsonLinestring
	PolylineType isPolyline_PolylineType `protobuf_oneof:"polyline_type"`
}

func (x *Polyline) Reset() {
	*x = Polyline{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_routes_v1_polyline_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Polyline) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Polyline) ProtoMessage() {}

func (x *Polyline) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_routes_v1_polyline_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Polyline.ProtoReflect.Descriptor instead.
func (*Polyline) Descriptor() ([]byte, []int) {
	return file_google_maps_routes_v1_polyline_proto_rawDescGZIP(), []int{0}
}

func (m *Polyline) GetPolylineType() isPolyline_PolylineType {
	if m != nil {
		return m.PolylineType
	}
	return nil
}

func (x *Polyline) GetEncodedPolyline() string {
	if x, ok := x.GetPolylineType().(*Polyline_EncodedPolyline); ok {
		return x.EncodedPolyline
	}
	return ""
}

func (x *Polyline) GetGeoJsonLinestring() *structpb.Struct {
	if x, ok := x.GetPolylineType().(*Polyline_GeoJsonLinestring); ok {
		return x.GeoJsonLinestring
	}
	return nil
}

type isPolyline_PolylineType interface {
	isPolyline_PolylineType()
}

type Polyline_EncodedPolyline struct {
	// The string encoding of the polyline using the [polyline encoding
	// algorithm](https://developers.google.com/maps/documentation/utilities/polylinealgorithm)
	EncodedPolyline string `protobuf:"bytes,1,opt,name=encoded_polyline,json=encodedPolyline,proto3,oneof"`
}

type Polyline_GeoJsonLinestring struct {
	// Specifies a polyline using the [GeoJSON LineString
	// format](https://tools.ietf.org/html/rfc7946#section-3.1.4)
	GeoJsonLinestring *structpb.Struct `protobuf:"bytes,2,opt,name=geo_json_linestring,json=geoJsonLinestring,proto3,oneof"`
}

func (*Polyline_EncodedPolyline) isPolyline_PolylineType() {}

func (*Polyline_GeoJsonLinestring) isPolyline_PolylineType() {}

var File_google_maps_routes_v1_polyline_proto protoreflect.FileDescriptor

var file_google_maps_routes_v1_polyline_proto_rawDesc = []byte{
	0x0a, 0x24, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x72, 0x6f,
	0x75, 0x74, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d,
	0x61, 0x70, 0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x93, 0x01, 0x0a, 0x08,
	0x50, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x2b, 0x0a, 0x10, 0x65, 0x6e, 0x63, 0x6f,
	0x64, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x0f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x64, 0x50, 0x6f, 0x6c,
	0x79, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x49, 0x0a, 0x13, 0x67, 0x65, 0x6f, 0x5f, 0x6a, 0x73, 0x6f,
	0x6e, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x48, 0x00, 0x52, 0x11, 0x67,
	0x65, 0x6f, 0x4a, 0x73, 0x6f, 0x6e, 0x4c, 0x69, 0x6e, 0x65, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x42, 0x0f, 0x0a, 0x0d, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x2a, 0x53, 0x0a, 0x0f, 0x50, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x51, 0x75, 0x61,
	0x6c, 0x69, 0x74, 0x79, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x4f, 0x4c, 0x59, 0x4c, 0x49, 0x4e, 0x45,
	0x5f, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x48, 0x49, 0x47, 0x48, 0x5f, 0x51,
	0x55, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x4f, 0x56, 0x45, 0x52,
	0x56, 0x49, 0x45, 0x57, 0x10, 0x02, 0x2a, 0x64, 0x0a, 0x10, 0x50, 0x6f, 0x6c, 0x79, 0x6c, 0x69,
	0x6e, 0x65, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x21, 0x0a, 0x1d, 0x50, 0x4f,
	0x4c, 0x59, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a,
	0x10, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x44, 0x5f, 0x50, 0x4f, 0x4c, 0x59, 0x4c, 0x49, 0x4e,
	0x45, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x47, 0x45, 0x4f, 0x5f, 0x4a, 0x53, 0x4f, 0x4e, 0x5f,
	0x4c, 0x49, 0x4e, 0x45, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x42, 0xa3, 0x01, 0x0a,
	0x19, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73,
	0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x42, 0x0d, 0x50, 0x6f, 0x6c, 0x79,
	0x6c, 0x69, 0x6e, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x3b, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67,
	0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2f, 0x76,
	0x31, 0x3b, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0xf8, 0x01, 0x01, 0xa2, 0x02, 0x04, 0x47, 0x4d,
	0x52, 0x53, 0xaa, 0x02, 0x15, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x4d, 0x61, 0x70, 0x73,
	0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x15, 0x47, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x5c, 0x4d, 0x61, 0x70, 0x73, 0x5c, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x5c,
	0x56, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_maps_routes_v1_polyline_proto_rawDescOnce sync.Once
	file_google_maps_routes_v1_polyline_proto_rawDescData = file_google_maps_routes_v1_polyline_proto_rawDesc
)

func file_google_maps_routes_v1_polyline_proto_rawDescGZIP() []byte {
	file_google_maps_routes_v1_polyline_proto_rawDescOnce.Do(func() {
		file_google_maps_routes_v1_polyline_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_maps_routes_v1_polyline_proto_rawDescData)
	})
	return file_google_maps_routes_v1_polyline_proto_rawDescData
}

var file_google_maps_routes_v1_polyline_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_google_maps_routes_v1_polyline_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_google_maps_routes_v1_polyline_proto_goTypes = []interface{}{
	(PolylineQuality)(0),    // 0: google.maps.routes.v1.PolylineQuality
	(PolylineEncoding)(0),   // 1: google.maps.routes.v1.PolylineEncoding
	(*Polyline)(nil),        // 2: google.maps.routes.v1.Polyline
	(*structpb.Struct)(nil), // 3: google.protobuf.Struct
}
var file_google_maps_routes_v1_polyline_proto_depIdxs = []int32{
	3, // 0: google.maps.routes.v1.Polyline.geo_json_linestring:type_name -> google.protobuf.Struct
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_google_maps_routes_v1_polyline_proto_init() }
func file_google_maps_routes_v1_polyline_proto_init() {
	if File_google_maps_routes_v1_polyline_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_maps_routes_v1_polyline_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Polyline); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_maps_routes_v1_polyline_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Polyline_EncodedPolyline)(nil),
		(*Polyline_GeoJsonLinestring)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_maps_routes_v1_polyline_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_maps_routes_v1_polyline_proto_goTypes,
		DependencyIndexes: file_google_maps_routes_v1_polyline_proto_depIdxs,
		EnumInfos:         file_google_maps_routes_v1_polyline_proto_enumTypes,
		MessageInfos:      file_google_maps_routes_v1_polyline_proto_msgTypes,
	}.Build()
	File_google_maps_routes_v1_polyline_proto = out.File
	file_google_maps_routes_v1_polyline_proto_rawDesc = nil
	file_google_maps_routes_v1_polyline_proto_goTypes = nil
	file_google_maps_routes_v1_polyline_proto_depIdxs = nil
}
