package com.fuck.fuckinggooo.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.net.VpnService
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.fuck.fuckinggooo.model.ProxyNode
import com.fuck.fuckinggooo.viewmodel.ProxyMode
import com.fuck.fuckinggooo.jni.SingBoxJNI
import com.google.gson.Gson
import kotlinx.coroutines.*

/**
 * VPN连接管理器
 * 负责管理VPN连接状态、启动停止VPN服务等
 */
class VpnManager(private val context: Context) {
    
    companion object {
        private const val TAG = "VpnManager"
        
        @Volatile
        private var INSTANCE: VpnManager? = null
        
        fun getInstance(context: Context): VpnManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: VpnManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    // VPN连接状态
    enum class VpnState {
        DISCONNECTED,
        CONNECTING,
        CONNECTED,
        DISCONNECTING,
        ERROR
    }
    
    // VPN状态LiveData
    private val _vpnState = MutableLiveData(VpnState.DISCONNECTED)
    val vpnState: LiveData<VpnState> = _vpnState
    
    // 当前连接的节点
    private val _currentNode = MutableLiveData<ProxyNode?>()
    val currentNode: LiveData<ProxyNode?> = _currentNode
    
    // 连接统计信息
    private val _connectionStats = MutableLiveData<ConnectionStats>()
    val connectionStats: LiveData<ConnectionStats> = _connectionStats
    
    // 协程作用域
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private var statsJob: Job? = null
    private var statusMonitoringJob: Job? = null
    private var currentState = VpnState.DISCONNECTED
    private val stateChangeListeners = mutableListOf<(VpnState) -> Unit>()
    
    // 广播接收器，监听VPN状态变化
    private val vpnStatusReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == ProxyVpnService.VPN_STATUS_ACTION) {
                val status = intent.getStringExtra(ProxyVpnService.EXTRA_STATUS)
                val errorMessage = intent.getStringExtra(ProxyVpnService.EXTRA_ERROR_MESSAGE)
                
                Log.d(TAG, "Received VPN status broadcast: $status")
                
                when (status) {
                    ProxyVpnService.STATUS_CONNECTING -> {
                        _vpnState.value = VpnState.CONNECTING
                    }
                    ProxyVpnService.STATUS_CONNECTED -> {
                        _vpnState.value = VpnState.CONNECTED
                        // 不再重复调用startStatusMonitoring，因为在startVpn中已经调用了
                        Log.d(TAG, "VPN connected via broadcast")
                    }
                    ProxyVpnService.STATUS_DISCONNECTING -> {
                        _vpnState.value = VpnState.DISCONNECTING
                    }
                    ProxyVpnService.STATUS_DISCONNECTED -> {
                        _vpnState.value = VpnState.DISCONNECTED
                        _currentNode.value = null
                        stopStatusMonitoring()
                    }
                    ProxyVpnService.STATUS_ERROR -> {
                        _vpnState.value = VpnState.ERROR
                        Log.e(TAG, "VPN error: $errorMessage")
                        stopStatusMonitoring()
                    }
                }
            }
        }
    }
    
    init {
        // 注册广播接收器
        val filter = IntentFilter(ProxyVpnService.VPN_STATUS_ACTION)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 需要明确指定导出标志
            context.registerReceiver(vpnStatusReceiver, filter, Context.RECEIVER_NOT_EXPORTED)
        } else {
            context.registerReceiver(vpnStatusReceiver, filter)
        }
    }
    
    data class ConnectionStats(
        val uploadSpeed: String = "0 B/s",
        val downloadSpeed: String = "0 B/s",
        val totalUpload: String = "0 B",
        val totalDownload: String = "0 B",
        val connectedTime: Long = 0L,
        val activeConnections: Int = 0
    )
    
    /**
     * 检查VPN权限
     */
    fun checkVpnPermission(): Intent? {
        return VpnService.prepare(context)
    }
    
    /**
     * 启动VPN连接
     */
    fun startVpn(node: ProxyNode, mode: ProxyMode = ProxyMode.RULE) {
        if (_vpnState.value == VpnState.CONNECTED || _vpnState.value == VpnState.CONNECTING) {
            Log.w(TAG, "VPN is already connected or connecting")
            return
        }
        
        Log.d(TAG, "Starting VPN connection to: ${node.name} with mode: $mode")
        _vpnState.value = VpnState.CONNECTING
        _currentNode.value = node
        
        try {
            val intent = Intent(context, ProxyVpnService::class.java).apply {
                action = ProxyVpnService.ACTION_START
                putExtra(ProxyVpnService.EXTRA_PROXY_NODE_JSON, Gson().toJson(node))
                putExtra(ProxyVpnService.EXTRA_PROXY_MODE, mode.name)
            }
            context.startForegroundService(intent)
            
            // 启动状态监控
            startStatusMonitoring()
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start VPN", e)
            _vpnState.value = VpnState.ERROR
            _currentNode.value = null
        }
    }
    
    /**
     * 停止VPN连接
     */
    fun stopVpn() {
        if (_vpnState.value == VpnState.DISCONNECTED || _vpnState.value == VpnState.DISCONNECTING) {
            Log.w(TAG, "VPN is already disconnected or disconnecting")
            return
        }
        
        Log.d(TAG, "Stopping VPN connection")
        _vpnState.value = VpnState.DISCONNECTING
        
        try {
            val intent = Intent(context, ProxyVpnService::class.java).apply {
                action = ProxyVpnService.ACTION_STOP
            }
            context.startService(intent)
            
            // 停止状态监控
            stopStatusMonitoring()
            
            _vpnState.value = VpnState.DISCONNECTED
            _currentNode.value = null
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop VPN", e)
            _vpnState.value = VpnState.ERROR
        }
    }
    
    /**
     * 切换VPN连接
     */
    fun switchVpn(node: ProxyNode) {
        Log.d(TAG, "Switching VPN to: ${node.name}")
        
        if (_vpnState.value == VpnState.CONNECTED) {
            // 先停止当前连接，然后启动新连接
            scope.launch {
                stopVpn()
                delay(1000) // 等待停止完成
                startVpn(node, ProxyMode.RULE)
            }
        } else {
            startVpn(node, ProxyMode.RULE)
        }
    }
    
    /**
     * 获取当前VPN状态
     */
    fun getCurrentState(): VpnState {
        return _vpnState.value ?: VpnState.DISCONNECTED
    }
    
    /**
     * 检查是否已连接
     */
    fun isConnected(): Boolean {
        return _vpnState.value == VpnState.CONNECTED
    }
    
    /**
     * 启动状态监控
     */
    private fun startStatusMonitoring() {
        // 先停止现有的监控
        stopStatusMonitoring()

        Log.d(TAG, "Starting VPN status monitoring")
        statusMonitoringJob = scope.launch {
            var consecutiveDisconnects = 0

            try {
                while (isActive) {
                    try {
                        val currentVpnState = _vpnState.value ?: VpnState.DISCONNECTED

                        // 只在连接状态下进行监控
                        if (currentVpnState == VpnState.CONNECTED || currentVpnState == VpnState.CONNECTING) {
                            val isConnected = checkVpnConnection()

                            when (currentVpnState) {
                                VpnState.CONNECTING -> {
                                    if (isConnected) {
                                        consecutiveDisconnects = 0
                                        Log.d(TAG, "VPN connection established")
                                    }
                                }
                                VpnState.CONNECTED -> {
                                    if (!isConnected) {
                                        consecutiveDisconnects++
                                        Log.w(TAG, "VPN disconnection detected ($consecutiveDisconnects/3)")
                                        // 需要连续3次检测到断开才认为真的断开
                                        if (consecutiveDisconnects >= 3) {
                                            Log.w(TAG, "VPN connection lost, updating state")
                                            _vpnState.value = VpnState.DISCONNECTED
                                            _currentNode.value = null
                                            break
                                        }
                                    } else {
                                        consecutiveDisconnects = 0
                                    }
                                }
                                else -> {
                                    // 其他状态不需要监控
                                    break
                                }
                            }
                        } else {
                            // 非连接状态，停止监控
                            break
                        }

                        delay(3000) // 增加检测间隔到3秒
                    } catch (e: CancellationException) {
                        Log.d(TAG, "Status monitoring cancelled")
                        break
                    } catch (e: Exception) {
                        Log.e(TAG, "Error in status monitoring", e)
                        delay(5000) // 出错时等待更长时间再重试
                    }
                }
            } catch (e: CancellationException) {
                Log.d(TAG, "Status monitoring job cancelled")
            } catch (e: Exception) {
                Log.e(TAG, "Status monitoring failed", e)
            }

            Log.d(TAG, "Status monitoring ended")
        }
    }
    
    /**
     * 停止状态监控
     */
    private fun stopStatusMonitoring() {
        try {
            statusMonitoringJob?.cancel()
            statusMonitoringJob = null
            Log.d(TAG, "Status monitoring stopped")
        } catch (e: Exception) {
            Log.w(TAG, "Error stopping status monitoring", e)
        }
    }
    
    /**
     * 更新连接统计信息
     */
    private fun updateConnectionStats() {
        try {
            var connections = 0
            try {
                connections = SingBoxJNI.getConnectionStats()
            } catch (e: UnsatisfiedLinkError) {
                Log.w(TAG, "Native library not loaded, using fallback connection stats")
                connections = 1 // 假设有一个连接
            } catch (e: Exception) {
                Log.w(TAG, "Failed to get connection stats from SingBox: ${e.message}")
                connections = 1 // 假设有一个连接
            }
            
            // 这里应该从sing-box获取真实的统计数据
            // 目前使用模拟数据
            val stats = ConnectionStats(
                uploadSpeed = "1.2 MB/s",
                downloadSpeed = "5.8 MB/s", 
                totalUpload = "125 MB",
                totalDownload = "892 MB",
                connectedTime = System.currentTimeMillis(),
                activeConnections = connections
            )
            
            _connectionStats.value = stats
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update connection stats", e)
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        stopStatusMonitoring()
        LocalBroadcastManager.getInstance(context).unregisterReceiver(vpnStatusReceiver)
        scope.cancel()
        Log.d(TAG, "VpnManager cleaned up")
    }
    private fun updateVpnState(newState: VpnState) {
        if (currentState != newState) {
            currentState = newState
            Log.d(TAG, "VPN state changed: $newState")
            
            // 通知所有监听器
            stateChangeListeners.forEach { it(newState) }
        }
    }

    private fun checkVpnConnection(): Boolean {
        return try {
            // 简单的连接检查 - 检查VPN状态
            _vpnState.value == VpnState.CONNECTED
        } catch (e: Exception) {
            Log.e(TAG, "Error checking VPN connection", e)
            false
        }
    }
}

