// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.12.2
// source: google/maps/routes/v1/custom_route.proto

package routes

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Encapsulates a custom route computed based on the route objective specified
// by the customer. CustomRoute contains a route and a route token, which can be
// passed to NavSDK to reconstruct the custom route for turn by turn navigation.
type CustomRoute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The route considered 'best' for the input route objective.
	Route *Route `protobuf:"bytes,11,opt,name=route,proto3" json:"route,omitempty"`
	// Web-safe base64 encoded route token that can be passed to NavSDK, which
	// allows NavSDK to reconstruct the route during navigation, and in the event
	// of rerouting honor the original intention when RoutesPreferred
	// ComputeCustomRoutes is called. Customers should treat this token as an
	// opaque blob.
	Token string `protobuf:"bytes,12,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *CustomRoute) Reset() {
	*x = CustomRoute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_routes_v1_custom_route_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomRoute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomRoute) ProtoMessage() {}

func (x *CustomRoute) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_routes_v1_custom_route_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomRoute.ProtoReflect.Descriptor instead.
func (*CustomRoute) Descriptor() ([]byte, []int) {
	return file_google_maps_routes_v1_custom_route_proto_rawDescGZIP(), []int{0}
}

func (x *CustomRoute) GetRoute() *Route {
	if x != nil {
		return x.Route
	}
	return nil
}

func (x *CustomRoute) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

var File_google_maps_routes_v1_custom_route_proto protoreflect.FileDescriptor

var file_google_maps_routes_v1_custom_route_proto_rawDesc = []byte{
	0x0a, 0x28, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x72, 0x6f,
	0x75, 0x74, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x72,
	0x6f, 0x75, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x1a, 0x21, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x72,
	0x6f, 0x75, 0x74, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x57, 0x0a, 0x0b, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x6f,
	0x75, 0x74, 0x65, 0x12, 0x32, 0x0a, 0x05, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73,
	0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65,
	0x52, 0x05, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0xa6, 0x01,
	0x0a, 0x19, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70,
	0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x42, 0x10, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a,
	0x3b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f,
	0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x72, 0x6f, 0x75, 0x74,
	0x65, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0xf8, 0x01, 0x01, 0xa2,
	0x02, 0x04, 0x47, 0x4d, 0x52, 0x53, 0xaa, 0x02, 0x15, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x4d, 0x61, 0x70, 0x73, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x56, 0x31, 0xca, 0x02,
	0x15, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x4d, 0x61, 0x70, 0x73, 0x5c, 0x52, 0x6f, 0x75,
	0x74, 0x65, 0x73, 0x5c, 0x56, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_maps_routes_v1_custom_route_proto_rawDescOnce sync.Once
	file_google_maps_routes_v1_custom_route_proto_rawDescData = file_google_maps_routes_v1_custom_route_proto_rawDesc
)

func file_google_maps_routes_v1_custom_route_proto_rawDescGZIP() []byte {
	file_google_maps_routes_v1_custom_route_proto_rawDescOnce.Do(func() {
		file_google_maps_routes_v1_custom_route_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_maps_routes_v1_custom_route_proto_rawDescData)
	})
	return file_google_maps_routes_v1_custom_route_proto_rawDescData
}

var file_google_maps_routes_v1_custom_route_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_google_maps_routes_v1_custom_route_proto_goTypes = []interface{}{
	(*CustomRoute)(nil), // 0: google.maps.routes.v1.CustomRoute
	(*Route)(nil),       // 1: google.maps.routes.v1.Route
}
var file_google_maps_routes_v1_custom_route_proto_depIdxs = []int32{
	1, // 0: google.maps.routes.v1.CustomRoute.route:type_name -> google.maps.routes.v1.Route
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_google_maps_routes_v1_custom_route_proto_init() }
func file_google_maps_routes_v1_custom_route_proto_init() {
	if File_google_maps_routes_v1_custom_route_proto != nil {
		return
	}
	file_google_maps_routes_v1_route_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_maps_routes_v1_custom_route_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomRoute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_maps_routes_v1_custom_route_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_maps_routes_v1_custom_route_proto_goTypes,
		DependencyIndexes: file_google_maps_routes_v1_custom_route_proto_depIdxs,
		MessageInfos:      file_google_maps_routes_v1_custom_route_proto_msgTypes,
	}.Build()
	File_google_maps_routes_v1_custom_route_proto = out.File
	file_google_maps_routes_v1_custom_route_proto_rawDesc = nil
	file_google_maps_routes_v1_custom_route_proto_goTypes = nil
	file_google_maps_routes_v1_custom_route_proto_depIdxs = nil
}
