package com.fuck.fuckinggooo.core

import android.content.Context
import android.util.Log
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream

import com.fuck.fuckinggooo.model.ProxyNode

/**
 * SingBox核心管理类
 * 使用libcore模块提供的sing-box功能
 */
class SingBoxCore(private val context: Context) {
    private var isRunning = false
    private var isInFallbackMode = false
    private var libcoreManager: LibcoreManager? = null
    private var directSocksProxy: DirectSocksProxy? = null
    
    companion object {
        private const val TAG = "SingBoxCore"
        private const val CONFIG_FILE = "config.json"
    }

    /**
     * 安全初始化LibcoreManager
     */
    private fun initLibcoreManager(): LibcoreManager? {
        if (libcoreManager == null) {
            try {
                // 检查native库是否可用，如果不可用直接返回null
                val libFile = java.io.File(context.applicationInfo.nativeLibraryDir, "liblibcore.so")
                if (!libFile.exists()) {
                    Log.w(TAG, "Native library liblibcore.so not found, using fallback mode")
                    return null
                }

                libcoreManager = LibcoreManager
                Log.d(TAG, "LibcoreManager initialized successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to initialize LibcoreManager, will use fallback mode", e)
                libcoreManager = null
            }
        }
        return libcoreManager
    }
    
    /**
     * 启动sing-box核心
     */
    suspend fun start(configJson: String, proxyNode: ProxyNode? = null): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                if (isRunning) {
                    Log.w(TAG, "SingBox core is already running")
                    return@withContext true
                }
                
                // 保存配置文件
                val configFile = File(context.filesDir, CONFIG_FILE)
                FileOutputStream(configFile).use { fos ->
                    fos.write(configJson.toByteArray())
                }
                
                Log.i(TAG, "Starting SingBox core with libcore")
                Log.d(TAG, "Config: $configJson")
                
                // 尝试使用libcore启动sing-box核心，带重试机制
                var lastException: Exception? = null
                // 暂时跳过native库，直接使用fallback模式
                // TODO: 修复native库问题后重新启用
                Log.w(TAG, "Skipping native library due to compatibility issues, using fallback mode")
                val manager: LibcoreManager? = null

                if (false && manager != null) {
                    for (attempt in 1..3) {
                        try {
                            Log.i(TAG, "Attempting to start SingBox core (attempt $attempt/3)")
                            val success = manager?.startSingBox(context, configJson) ?: false

                        if (success) {
                            // 验证端口是否可用
                            delay(500) // 等待核心完全启动
                            val socksPort = getSocksPort()
                            val httpPort = getHttpPort()

                            Log.i(TAG, "SingBox core started successfully with libcore")
                            Log.i(TAG, "SOCKS port: $socksPort, HTTP port: $httpPort")

                            isRunning = true
                            isInFallbackMode = false
                            return@withContext true
                        } else {
                            lastException = Exception("Libcore start failed")
                            Log.w(TAG, "Libcore failed to start (attempt $attempt)")
                            if (attempt < 3) {
                                delay(1000) // 等待1秒后重试
                            }
                        }
                    } catch (e: Exception) {
                        lastException = e
                        Log.w(TAG, "Native library failed (attempt $attempt): ${e.message}")
                        if (attempt < 3) {
                            delay(1000) // 等待1秒后重试
                        }
                    }
                }

                // 所有重试都失败，启动fallback模式
                Log.w(TAG, "All attempts to start native core failed, starting fallback mode")
                Log.w(TAG, "Last error: ${lastException?.message}")
                } else {
                    // LibcoreManager初始化失败，直接使用fallback模式
                    Log.w(TAG, "LibcoreManager initialization failed, using fallback mode")
                }

                Log.i(TAG, "=== STARTING FALLBACK MODE ===")

                return@withContext startFallbackMode(proxyNode)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to start SingBox core", e)
                return@withContext false
            }
        }
    }

    /**
     * 启动fallback模式
     */
    private suspend fun startFallbackMode(proxyNode: ProxyNode? = null): Boolean = withContext(Dispatchers.IO) {
        try {
            // Fallback模式：启动本地SOCKS代理
            isInFallbackMode = true
            Log.i(TAG, "Creating DirectSocksProxy on port 1081")
            directSocksProxy = DirectSocksProxy(port = 1081, proxyNode = proxyNode)
            Log.i(TAG, "Starting DirectSocksProxy...")
            directSocksProxy?.start()

            // 等待DirectSocksProxy启动
            delay(1000)

            // 验证DirectSocksProxy是否启动成功
            val isProxyRunning = directSocksProxy?.isRunning() ?: false
            if (!isProxyRunning) {
                Log.e(TAG, "DirectSocksProxy failed to start")
                return@withContext false
            }

            Log.i(TAG, "DirectSocksProxy start command completed")

            isRunning = true
            Log.i(TAG, "SingBox core started successfully with fallback mode (DirectSocksProxy)")
            Log.i(TAG, "SOCKS port: ${getSocksPort()}, HTTP port: ${getHttpPort()}")
            Log.i(TAG, "=== FALLBACK MODE READY ===")
            return@withContext true

        } catch (e: Exception) {
            Log.e(TAG, "Failed to start fallback mode", e)
            return@withContext false
        }
    }

    /**
     * 停止sing-box核心
     */
    suspend fun stop() {
        withContext(Dispatchers.IO) {
            try {
                if (!isRunning) {
                    Log.w(TAG, "SingBox core is not running")
                    return@withContext
                }
                
                Log.i(TAG, "Stopping SingBox core")
                
                if (isInFallbackMode) {
                    // 停止本地SOCKS代理
                    directSocksProxy?.stop()
                    directSocksProxy = null
                    Log.i(TAG, "DirectSocksProxy stopped")
                } else {
                    // 使用libcore停止sing-box核心
                    val manager = libcoreManager
                    val success = manager?.stopSingBox() ?: true

                    if (success) {
                        Log.i(TAG, "SingBox core stopped successfully")
                    } else {
                        Log.e(TAG, "Failed to stop SingBox core properly")
                    }
                }
                
                isRunning = false
                isInFallbackMode = false
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to stop SingBox core", e)
                isRunning = false
                isInFallbackMode = false
            }
        }
    }
    
    /**
     * 检查核心是否运行中
     */
    fun isRunning(): Boolean {
        return try {
            if (isInFallbackMode) {
                isRunning // 在fallback模式下直接返回状态
            } else {
                val manager = libcoreManager
                isRunning && (manager?.isBoxRunning() ?: false)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking core status", e)
            isRunning
        }
    }
    
    /**
     * 获取SOCKS代理端口
     */
    fun getSocksPort(): Int {
        return try {
            val manager = libcoreManager
            manager?.getSocksPort() ?: 1081
        } catch (e: Exception) {
            Log.w(TAG, "Native library not available, using fallback SOCKS port")
            1081 // 与配置中的端口保持一致
        }
    }
    
    /**
     * 获取HTTP代理端口
     */
    fun getHttpPort(): Int {
        return try {
            val manager = libcoreManager
            manager?.getHttpPort() ?: 1080
        } catch (e: Exception) {
            Log.e(TAG, "Error getting HTTP port", e)
            1080 // 默认端口
        }
    }
    
    /**
     * 测试连接
     */
    suspend fun testConnection(serverAddr: String, serverPort: Int): Boolean {
        return try {
            val manager = libcoreManager
            manager?.testConnection(serverAddr, serverPort, 5000) ?: false
        } catch (e: Exception) {
            Log.e(TAG, "Error testing connection", e)
            false
        }
    }
    
    /**
     * 获取版本信息
     */
    fun getVersion(): String {
        return try {
            val manager = libcoreManager
            manager?.getVersion() ?: "Fallback-1.0.0"
        } catch (e: Exception) {
            Log.e(TAG, "Error getting version", e)
            "Fallback-1.0.0"
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            val manager = libcoreManager
            manager?.cleanup()
        } catch (e: Exception) {
            Log.e(TAG, "Error during cleanup", e)
        }
    }
}