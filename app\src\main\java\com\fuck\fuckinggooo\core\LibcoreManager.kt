package com.fuck.fuckinggooo.core

import android.content.Context
import android.util.Log
import com.google.gson.Gson
import java.io.File

/**
 * 基于NekoBox架构的Libcore管理器
 * 参考: https://github.com/MatsuriDayo/NekoBoxForAndroid
 */
object LibcoreManager {
    private const val TAG = "LibcoreManager"
    private var isNativeLoaded = false
    private var boxInstance: Long = 0

    init {
        try {
            // 尝试加载libcore库
            System.loadLibrary("libcore")
            isNativeLoaded = true
            Log.d(TAG, "Native libcore library loaded successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to load native libcore library", e)
            isNativeLoaded = false
        }
    }

    /**
     * 启动sing-box核心
     */
    fun startSingBox(context: Context, config: String): Boolean {
        return if (isNativeLoaded) {
            try {
                Log.d(TAG, "Starting sing-box with native libcore")

                // 创建工作目录
                val workDir = File(context.filesDir, "sing-box")
                if (!workDir.exists()) {
                    workDir.mkdirs()
                }

                // 启动sing-box实例
                boxInstance = nativeNewBox(config, workDir.absolutePath)
                if (boxInstance != 0L) {
                    nativeStartBox(boxInstance)
                    Log.d(TAG, "Sing-box started successfully with instance: $boxInstance")
                    true
                } else {
                    Log.e(TAG, "Failed to create sing-box instance")
                    false
                }
            } catch (e: Exception) {
                Log.e(TAG, "Native sing-box start failed", e)
                false
            }
        } else {
            Log.w(TAG, "Native library not loaded, using enhanced fallback")
            startEnhancedFallback(config)
        }
    }

    /**
     * 停止sing-box核心
     */
    fun stopSingBox(): Boolean {
        return if (isNativeLoaded && boxInstance != 0L) {
            try {
                nativeStopBox(boxInstance)
                nativeCloseBox(boxInstance)
                boxInstance = 0
                Log.d(TAG, "Sing-box stopped successfully")
                true
            } catch (e: Exception) {
                Log.e(TAG, "Failed to stop sing-box", e)
                false
            }
        } else {
            EnhancedFallbackProxy.stop()
            true
        }
    }

    /**
     * 检查sing-box是否运行
     */
    fun isRunning(): Boolean {
        return if (isNativeLoaded) {
            boxInstance != 0L
        } else {
            EnhancedFallbackProxy.isRunning()
        }
    }

    /**
     * 检查box实例是否运行
     */
    fun isBoxRunning(): Boolean {
        return isNativeLoaded && boxInstance != 0L
    }

    /**
     * 获取SOCKS端口
     */
    fun getSocksPort(): Int {
        return 1081 // 默认SOCKS端口
    }

    /**
     * 获取HTTP端口
     */
    fun getHttpPort(): Int {
        return 1080 // 默认HTTP端口
    }

    /**
     * 测试连接
     */
    fun testConnection(serverAddr: String, serverPort: Int, timeout: Int): Boolean {
        return try {
            val socket = java.net.Socket()
            socket.connect(java.net.InetSocketAddress(serverAddr, serverPort), timeout)
            socket.close()
            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 获取版本信息
     */
    fun getVersion(): String {
        return "1.0.0-fallback"
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        if (boxInstance != 0L) {
            try {
                nativeStopBox(boxInstance)
                nativeCloseBox(boxInstance)
                boxInstance = 0
            } catch (e: Exception) {
                Log.e(TAG, "Error during cleanup", e)
            }
        }
    }

    private fun startEnhancedFallback(config: String): Boolean {
        return try {
            // 解析配置获取代理信息
            val gson = Gson()
            val configMap = gson.fromJson(config, Map::class.java) as Map<String, Any>
            val outbounds = configMap["outbounds"] as List<Map<String, Any>>
            val proxyOutbound = outbounds.find { it["tag"] == "proxy" } as? Map<String, Any>

            if (proxyOutbound != null) {
                // 启动增强的fallback代理
                EnhancedFallbackProxy.start(proxyOutbound)
            } else {
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Enhanced fallback failed", e)
            false
        }
    }

    // Native方法声明 - 基于NekoBox的libcore接口
    private external fun nativeNewBox(config: String, workDir: String): Long
    private external fun nativeStartBox(instance: Long): Boolean
    private external fun nativeStopBox(instance: Long): Boolean
    private external fun nativeCloseBox(instance: Long): Boolean
}