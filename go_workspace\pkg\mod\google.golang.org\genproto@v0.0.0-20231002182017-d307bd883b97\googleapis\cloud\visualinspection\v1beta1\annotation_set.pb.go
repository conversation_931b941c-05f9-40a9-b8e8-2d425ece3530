// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0
// 	protoc        v3.13.0
// source: google/cloud/visualinspection/v1beta1/annotation_set.proto

package visualinspection

import (
	reflect "reflect"
	sync "sync"

	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	color "google.golang.org/genproto/googleapis/type/color"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

// Resource of an AnnotationSet.
type AnnotationSet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Type of the Annotations in this AnnotationSet. Annotations in this
	// AnnotationSet must be of the same type.
	//
	// Types that are assignable to AnnotationSetType:
	//	*AnnotationSet_ClassificationLabel
	//	*AnnotationSet_Box
	//	*AnnotationSet_Polygon
	//	*AnnotationSet_Mask
	//	*AnnotationSet_Polyline
	AnnotationSetType isAnnotationSet_AnnotationSetType `protobuf_oneof:"annotation_set_type"`
	// Output only. Resource name of the AnnotationSet generated by the system.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Required. A user friendly display name for the annotationset.
	// It can be up to 128 characters long and can be consist of any UTF-8
	// characters.
	DisplayName string `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// Output only. Timestamp when this AnnotationSet was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// Output only. Timestamp when AnnotationSet was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// Output only. The list of the AnnotationSpecs in the context of the
	// AnnotationSet. All of the AnnotationSpecs that are imported or created into
	// the AnnotationSet are automatically added to this list. User can manually
	// add item to this list even though no annotation is associated with the
	// corresponding AnnotationSpec.
	AnnotationSpecs []*AnnotationSpec `protobuf:"bytes,9,rep,name=annotation_specs,json=annotationSpecs,proto3" json:"annotation_specs,omitempty"`
}

func (x *AnnotationSet) Reset() {
	*x = AnnotationSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnotationSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnotationSet) ProtoMessage() {}

func (x *AnnotationSet) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnotationSet.ProtoReflect.Descriptor instead.
func (*AnnotationSet) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_annotation_set_proto_rawDescGZIP(), []int{0}
}

func (m *AnnotationSet) GetAnnotationSetType() isAnnotationSet_AnnotationSetType {
	if m != nil {
		return m.AnnotationSetType
	}
	return nil
}

func (x *AnnotationSet) GetClassificationLabel() *ClassificationLabelAnnotationSet {
	if x, ok := x.GetAnnotationSetType().(*AnnotationSet_ClassificationLabel); ok {
		return x.ClassificationLabel
	}
	return nil
}

func (x *AnnotationSet) GetBox() *BoundingBoxAnnotationSet {
	if x, ok := x.GetAnnotationSetType().(*AnnotationSet_Box); ok {
		return x.Box
	}
	return nil
}

func (x *AnnotationSet) GetPolygon() *PolygonAnnotationSet {
	if x, ok := x.GetAnnotationSetType().(*AnnotationSet_Polygon); ok {
		return x.Polygon
	}
	return nil
}

func (x *AnnotationSet) GetMask() *MaskAnnotationSet {
	if x, ok := x.GetAnnotationSetType().(*AnnotationSet_Mask); ok {
		return x.Mask
	}
	return nil
}

func (x *AnnotationSet) GetPolyline() *PolylineAnnotationSet {
	if x, ok := x.GetAnnotationSetType().(*AnnotationSet_Polyline); ok {
		return x.Polyline
	}
	return nil
}

func (x *AnnotationSet) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AnnotationSet) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *AnnotationSet) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *AnnotationSet) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *AnnotationSet) GetAnnotationSpecs() []*AnnotationSpec {
	if x != nil {
		return x.AnnotationSpecs
	}
	return nil
}

type isAnnotationSet_AnnotationSetType interface {
	isAnnotationSet_AnnotationSetType()
}

type AnnotationSet_ClassificationLabel struct {
	// Classification label.
	ClassificationLabel *ClassificationLabelAnnotationSet `protobuf:"bytes,3,opt,name=classification_label,json=classificationLabel,proto3,oneof"`
}

type AnnotationSet_Box struct {
	// Bounding box.
	Box *BoundingBoxAnnotationSet `protobuf:"bytes,4,opt,name=box,proto3,oneof"`
}

type AnnotationSet_Polygon struct {
	// Polygon.
	Polygon *PolygonAnnotationSet `protobuf:"bytes,5,opt,name=polygon,proto3,oneof"`
}

type AnnotationSet_Mask struct {
	// Mask.
	Mask *MaskAnnotationSet `protobuf:"bytes,6,opt,name=mask,proto3,oneof"`
}

type AnnotationSet_Polyline struct {
	// Polyline
	Polyline *PolylineAnnotationSet `protobuf:"bytes,11,opt,name=polyline,proto3,oneof"`
}

func (*AnnotationSet_ClassificationLabel) isAnnotationSet_AnnotationSetType() {}

func (*AnnotationSet_Box) isAnnotationSet_AnnotationSetType() {}

func (*AnnotationSet_Polygon) isAnnotationSet_AnnotationSetType() {}

func (*AnnotationSet_Mask) isAnnotationSet_AnnotationSetType() {}

func (*AnnotationSet_Polyline) isAnnotationSet_AnnotationSetType() {}

// ClassificationLabel Annotation type.
type ClassificationLabelAnnotationSet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether or not the classification annotation is a multi-label
	// classification.
	MultiLabel bool `protobuf:"varint,1,opt,name=multi_label,json=multiLabel,proto3" json:"multi_label,omitempty"`
}

func (x *ClassificationLabelAnnotationSet) Reset() {
	*x = ClassificationLabelAnnotationSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClassificationLabelAnnotationSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClassificationLabelAnnotationSet) ProtoMessage() {}

func (x *ClassificationLabelAnnotationSet) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClassificationLabelAnnotationSet.ProtoReflect.Descriptor instead.
func (*ClassificationLabelAnnotationSet) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_annotation_set_proto_rawDescGZIP(), []int{1}
}

func (x *ClassificationLabelAnnotationSet) GetMultiLabel() bool {
	if x != nil {
		return x.MultiLabel
	}
	return false
}

// BoundingBox Annotation type.
type BoundingBoxAnnotationSet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BoundingBoxAnnotationSet) Reset() {
	*x = BoundingBoxAnnotationSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoundingBoxAnnotationSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoundingBoxAnnotationSet) ProtoMessage() {}

func (x *BoundingBoxAnnotationSet) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoundingBoxAnnotationSet.ProtoReflect.Descriptor instead.
func (*BoundingBoxAnnotationSet) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_annotation_set_proto_rawDescGZIP(), []int{2}
}

// Polygon Annotation type.
type PolygonAnnotationSet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PolygonAnnotationSet) Reset() {
	*x = PolygonAnnotationSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PolygonAnnotationSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolygonAnnotationSet) ProtoMessage() {}

func (x *PolygonAnnotationSet) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolygonAnnotationSet.ProtoReflect.Descriptor instead.
func (*PolygonAnnotationSet) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_annotation_set_proto_rawDescGZIP(), []int{3}
}

// Mask Annotation type.
type MaskAnnotationSet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Mapping of color to annotationspec.
	// For example,
	// annotation_spec_colors {
	//  annotation_spec_id: 1
	//  red: 0
	//  green: 1
	//  blue:0
	// }
	// it means green color maps to annotation_spec_id==1.
	AnnotationSpecColors []*AnnotationSpecColor `protobuf:"bytes,1,rep,name=annotation_spec_colors,json=annotationSpecColors,proto3" json:"annotation_spec_colors,omitempty"`
}

func (x *MaskAnnotationSet) Reset() {
	*x = MaskAnnotationSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaskAnnotationSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaskAnnotationSet) ProtoMessage() {}

func (x *MaskAnnotationSet) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaskAnnotationSet.ProtoReflect.Descriptor instead.
func (*MaskAnnotationSet) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_annotation_set_proto_rawDescGZIP(), []int{4}
}

func (x *MaskAnnotationSet) GetAnnotationSpecColors() []*AnnotationSpecColor {
	if x != nil {
		return x.AnnotationSpecColors
	}
	return nil
}

// Mapping of color to AnnotationSpec
type AnnotationSpecColor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// AnnotationSpec Id.
	AnnotationSpecId string `protobuf:"bytes,1,opt,name=annotation_spec_id,json=annotationSpecId,proto3" json:"annotation_spec_id,omitempty"`
	// RGB value represents color in the category_mask image.
	// Alpha value will always be empty.
	Color *color.Color `protobuf:"bytes,2,opt,name=color,proto3" json:"color,omitempty"`
}

func (x *AnnotationSpecColor) Reset() {
	*x = AnnotationSpecColor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnotationSpecColor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnotationSpecColor) ProtoMessage() {}

func (x *AnnotationSpecColor) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnotationSpecColor.ProtoReflect.Descriptor instead.
func (*AnnotationSpecColor) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_annotation_set_proto_rawDescGZIP(), []int{5}
}

func (x *AnnotationSpecColor) GetAnnotationSpecId() string {
	if x != nil {
		return x.AnnotationSpecId
	}
	return ""
}

func (x *AnnotationSpecColor) GetColor() *color.Color {
	if x != nil {
		return x.Color
	}
	return nil
}

// Polyline Annotation type.
type PolylineAnnotationSet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PolylineAnnotationSet) Reset() {
	*x = PolylineAnnotationSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PolylineAnnotationSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolylineAnnotationSet) ProtoMessage() {}

func (x *PolylineAnnotationSet) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolylineAnnotationSet.ProtoReflect.Descriptor instead.
func (*PolylineAnnotationSet) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_annotation_set_proto_rawDescGZIP(), []int{6}
}

var File_google_cloud_visualinspection_v1beta1_annotation_set_proto protoreflect.FileDescriptor

var file_google_cloud_visualinspection_v1beta1_annotation_set_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x76,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x25, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65,
	0x74, 0x61, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x76, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x62,
	0x65, 0x74, 0x61, 0x31, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x70, 0x65, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbc, 0x07, 0x0a, 0x0d, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x12, 0x7c, 0x0a, 0x14, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x48, 0x00, 0x52,
	0x13, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x12, 0x53, 0x0a, 0x03, 0x62, 0x6f, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x42, 0x6f, 0x78, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x74, 0x48, 0x00, 0x52, 0x03, 0x62, 0x6f, 0x78, 0x12, 0x57, 0x0a, 0x07, 0x70, 0x6f, 0x6c,
	0x79, 0x67, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74,
	0x61, 0x31, 0x2e, 0x50, 0x6f, 0x6c, 0x79, 0x67, 0x6f, 0x6e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x48, 0x00, 0x52, 0x07, 0x70, 0x6f, 0x6c, 0x79, 0x67,
	0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x04, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x38, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e,
	0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x4d, 0x61, 0x73, 0x6b, 0x41, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x48, 0x00, 0x52, 0x04, 0x6d, 0x61,
	0x73, 0x6b, 0x12, 0x5a, 0x0a, 0x08, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x50, 0x6f, 0x6c,
	0x79, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x74, 0x48, 0x00, 0x52, 0x08, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x17,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41,
	0x03, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x40, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x40, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x65, 0x0a, 0x10, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x70, 0x65, 0x63, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x70, 0x65, 0x63, 0x73, 0x3a, 0x8f, 0x01, 0xea, 0x41, 0x8b,
	0x01, 0x0a, 0x2d, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74,
	0x12, 0x5a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x7d, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65,
	0x74, 0x73, 0x2f, 0x7b, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x7d, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x73, 0x2f, 0x7b, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x7d, 0x42, 0x15, 0x0a, 0x13,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x43, 0x0a, 0x20, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x22, 0x1a, 0x0a, 0x18, 0x42, 0x6f, 0x75, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x42, 0x6f, 0x78, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x65, 0x74, 0x22, 0x16, 0x0a, 0x14, 0x50, 0x6f, 0x6c, 0x79, 0x67, 0x6f, 0x6e, 0x41,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x22, 0x85, 0x01, 0x0a,
	0x11, 0x4d, 0x61, 0x73, 0x6b, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x74, 0x12, 0x70, 0x0a, 0x16, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x73, 0x70, 0x65, 0x63, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x70, 0x65, 0x63, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x52, 0x14,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x70, 0x65, 0x63, 0x43, 0x6f,
	0x6c, 0x6f, 0x72, 0x73, 0x22, 0x6d, 0x0a, 0x13, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x70, 0x65, 0x63, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x2c, 0x0a, 0x12, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x70, 0x65, 0x63, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x05, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x52, 0x05, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x22, 0x17, 0x0a, 0x15, 0x50, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x41,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x42, 0xff, 0x01, 0x0a,
	0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x50, 0x01, 0x5a, 0x55, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74,
	0x61, 0x31, 0x3b, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0xaa, 0x02, 0x25, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x43, 0x6c, 0x6f,
	0x75, 0x64, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x56, 0x31, 0x42, 0x65, 0x74, 0x61, 0x31, 0xca, 0x02, 0x25, 0x47, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x5c, 0x56, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5c, 0x56, 0x31, 0x62, 0x65,
	0x74, 0x61, 0x31, 0xea, 0x02, 0x28, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x43, 0x6c,
	0x6f, 0x75, 0x64, 0x3a, 0x3a, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x70, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x3a, 0x3a, 0x56, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_cloud_visualinspection_v1beta1_annotation_set_proto_rawDescOnce sync.Once
	file_google_cloud_visualinspection_v1beta1_annotation_set_proto_rawDescData = file_google_cloud_visualinspection_v1beta1_annotation_set_proto_rawDesc
)

func file_google_cloud_visualinspection_v1beta1_annotation_set_proto_rawDescGZIP() []byte {
	file_google_cloud_visualinspection_v1beta1_annotation_set_proto_rawDescOnce.Do(func() {
		file_google_cloud_visualinspection_v1beta1_annotation_set_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_cloud_visualinspection_v1beta1_annotation_set_proto_rawDescData)
	})
	return file_google_cloud_visualinspection_v1beta1_annotation_set_proto_rawDescData
}

var file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_google_cloud_visualinspection_v1beta1_annotation_set_proto_goTypes = []interface{}{
	(*AnnotationSet)(nil),                    // 0: google.cloud.visualinspection.v1beta1.AnnotationSet
	(*ClassificationLabelAnnotationSet)(nil), // 1: google.cloud.visualinspection.v1beta1.ClassificationLabelAnnotationSet
	(*BoundingBoxAnnotationSet)(nil),         // 2: google.cloud.visualinspection.v1beta1.BoundingBoxAnnotationSet
	(*PolygonAnnotationSet)(nil),             // 3: google.cloud.visualinspection.v1beta1.PolygonAnnotationSet
	(*MaskAnnotationSet)(nil),                // 4: google.cloud.visualinspection.v1beta1.MaskAnnotationSet
	(*AnnotationSpecColor)(nil),              // 5: google.cloud.visualinspection.v1beta1.AnnotationSpecColor
	(*PolylineAnnotationSet)(nil),            // 6: google.cloud.visualinspection.v1beta1.PolylineAnnotationSet
	(*timestamppb.Timestamp)(nil),            // 7: google.protobuf.Timestamp
	(*AnnotationSpec)(nil),                   // 8: google.cloud.visualinspection.v1beta1.AnnotationSpec
	(*color.Color)(nil),                      // 9: google.type.Color
}
var file_google_cloud_visualinspection_v1beta1_annotation_set_proto_depIdxs = []int32{
	1,  // 0: google.cloud.visualinspection.v1beta1.AnnotationSet.classification_label:type_name -> google.cloud.visualinspection.v1beta1.ClassificationLabelAnnotationSet
	2,  // 1: google.cloud.visualinspection.v1beta1.AnnotationSet.box:type_name -> google.cloud.visualinspection.v1beta1.BoundingBoxAnnotationSet
	3,  // 2: google.cloud.visualinspection.v1beta1.AnnotationSet.polygon:type_name -> google.cloud.visualinspection.v1beta1.PolygonAnnotationSet
	4,  // 3: google.cloud.visualinspection.v1beta1.AnnotationSet.mask:type_name -> google.cloud.visualinspection.v1beta1.MaskAnnotationSet
	6,  // 4: google.cloud.visualinspection.v1beta1.AnnotationSet.polyline:type_name -> google.cloud.visualinspection.v1beta1.PolylineAnnotationSet
	7,  // 5: google.cloud.visualinspection.v1beta1.AnnotationSet.create_time:type_name -> google.protobuf.Timestamp
	7,  // 6: google.cloud.visualinspection.v1beta1.AnnotationSet.update_time:type_name -> google.protobuf.Timestamp
	8,  // 7: google.cloud.visualinspection.v1beta1.AnnotationSet.annotation_specs:type_name -> google.cloud.visualinspection.v1beta1.AnnotationSpec
	5,  // 8: google.cloud.visualinspection.v1beta1.MaskAnnotationSet.annotation_spec_colors:type_name -> google.cloud.visualinspection.v1beta1.AnnotationSpecColor
	9,  // 9: google.cloud.visualinspection.v1beta1.AnnotationSpecColor.color:type_name -> google.type.Color
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_google_cloud_visualinspection_v1beta1_annotation_set_proto_init() }
func file_google_cloud_visualinspection_v1beta1_annotation_set_proto_init() {
	if File_google_cloud_visualinspection_v1beta1_annotation_set_proto != nil {
		return
	}
	file_google_cloud_visualinspection_v1beta1_annotation_spec_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnotationSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClassificationLabelAnnotationSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoundingBoxAnnotationSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PolygonAnnotationSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaskAnnotationSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnotationSpecColor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PolylineAnnotationSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*AnnotationSet_ClassificationLabel)(nil),
		(*AnnotationSet_Box)(nil),
		(*AnnotationSet_Polygon)(nil),
		(*AnnotationSet_Mask)(nil),
		(*AnnotationSet_Polyline)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_cloud_visualinspection_v1beta1_annotation_set_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_cloud_visualinspection_v1beta1_annotation_set_proto_goTypes,
		DependencyIndexes: file_google_cloud_visualinspection_v1beta1_annotation_set_proto_depIdxs,
		MessageInfos:      file_google_cloud_visualinspection_v1beta1_annotation_set_proto_msgTypes,
	}.Build()
	File_google_cloud_visualinspection_v1beta1_annotation_set_proto = out.File
	file_google_cloud_visualinspection_v1beta1_annotation_set_proto_rawDesc = nil
	file_google_cloud_visualinspection_v1beta1_annotation_set_proto_goTypes = nil
	file_google_cloud_visualinspection_v1beta1_annotation_set_proto_depIdxs = nil
}
