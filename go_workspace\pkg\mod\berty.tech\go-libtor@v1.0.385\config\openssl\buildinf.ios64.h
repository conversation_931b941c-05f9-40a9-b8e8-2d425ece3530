/*
 * WARNING: do not edit!
 * Generated by util/mkbuildinf.pl
 *
 * Copyright 2014-2017 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the OpenSSL license (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#define PLATFORM "platform: ios64-cross-arm64"
#define DATE "built on: Fri Oct  2 13:20:56 2020 UTC"

/*
 * Generate compiler_flags as an array of individual characters. This is a
 * workaround for the situation where CFLAGS gets too long for a C90 string
 * literal
 */
static const char compiler_flags[] = {
    'c','o','m','p','i','l','e','r',':',' ','/','A','p','p','l','i',
    'c','a','t','i','o','n','s','/','X','c','o','d','e','.','a','p',
    'p','/','C','o','n','t','e','n','t','s','/','D','e','v','e','l',
    'o','p','e','r','/','T','o','o','l','c','h','a','i','n','s','/',
    'X','c','o','d','e','D','e','f','a','u','l','t','.','x','c','t',
    'o','o','l','c','h','a','i','n','/','u','s','r','/','b','i','n',
    '/','c','c',' ','-','f','P','I','C',' ','-','i','s','y','s','r',
    'o','o','t',' ','/','A','p','p','l','i','c','a','t','i','o','n',
    's','/','X','c','o','d','e','.','a','p','p','/','C','o','n','t',
    'e','n','t','s','/','D','e','v','e','l','o','p','e','r','/','P',
    'l','a','t','f','o','r','m','s','/','i','P','h','o','n','e','O',
    'S','.','p','l','a','t','f','o','r','m','/','D','e','v','e','l',
    'o','p','e','r','/','S','D','K','s','/','i','P','h','o','n','e',
    'O','S','1','3','.','2','.','s','d','k',' ','-','f','n','o','-',
    'c','o','m','m','o','n',' ','-','f','e','m','b','e','d','-','b',
    'i','t','c','o','d','e',' ','-','m','i','o','s','-','v','e','r',
    's','i','o','n','-','m','i','n','=','1','2','.','0',' ','-','a',
    'r','c','h',' ','a','r','m','6','4',' ','-','O','3',' ','-','D',
    'O','P','E','N','S','S','L','_','P','I','C',' ','-','D','_','R',
    'E','E','N','T','R','A','N','T',' ','-','D','N','D','E','B','U',
    'G',' ','-','D','O','P','E','N','S','S','L','_','A','P','I','_',
    'C','O','M','P','A','T','=','0','x','1','0','1','0','0','0','0',
    '0','L','\0'
};
