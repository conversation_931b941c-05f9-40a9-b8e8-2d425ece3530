// Copyright 2019 Google LLC.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.12.2
// source: google/cloud/websecurityscanner/v1beta/scan_config.proto

package websecurityscanner

import (
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Type of user agents used for scanning.
type ScanConfig_UserAgent int32

const (
	// The user agent is unknown. Service will default to CHROME_LINUX.
	ScanConfig_USER_AGENT_UNSPECIFIED ScanConfig_UserAgent = 0
	// Chrome on Linux. This is the service default if unspecified.
	ScanConfig_CHROME_LINUX ScanConfig_UserAgent = 1
	// Chrome on Android.
	ScanConfig_CHROME_ANDROID ScanConfig_UserAgent = 2
	// Safari on IPhone.
	ScanConfig_SAFARI_IPHONE ScanConfig_UserAgent = 3
)

// Enum value maps for ScanConfig_UserAgent.
var (
	ScanConfig_UserAgent_name = map[int32]string{
		0: "USER_AGENT_UNSPECIFIED",
		1: "CHROME_LINUX",
		2: "CHROME_ANDROID",
		3: "SAFARI_IPHONE",
	}
	ScanConfig_UserAgent_value = map[string]int32{
		"USER_AGENT_UNSPECIFIED": 0,
		"CHROME_LINUX":           1,
		"CHROME_ANDROID":         2,
		"SAFARI_IPHONE":          3,
	}
)

func (x ScanConfig_UserAgent) Enum() *ScanConfig_UserAgent {
	p := new(ScanConfig_UserAgent)
	*p = x
	return p
}

func (x ScanConfig_UserAgent) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScanConfig_UserAgent) Descriptor() protoreflect.EnumDescriptor {
	return file_google_cloud_websecurityscanner_v1beta_scan_config_proto_enumTypes[0].Descriptor()
}

func (ScanConfig_UserAgent) Type() protoreflect.EnumType {
	return &file_google_cloud_websecurityscanner_v1beta_scan_config_proto_enumTypes[0]
}

func (x ScanConfig_UserAgent) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScanConfig_UserAgent.Descriptor instead.
func (ScanConfig_UserAgent) EnumDescriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDescGZIP(), []int{0, 0}
}

// Cloud platforms supported by Cloud Web Security Scanner.
type ScanConfig_TargetPlatform int32

const (
	// The target platform is unknown. Requests with this enum value will be
	// rejected with INVALID_ARGUMENT error.
	ScanConfig_TARGET_PLATFORM_UNSPECIFIED ScanConfig_TargetPlatform = 0
	// Google App Engine service.
	ScanConfig_APP_ENGINE ScanConfig_TargetPlatform = 1
	// Google Compute Engine service.
	ScanConfig_COMPUTE ScanConfig_TargetPlatform = 2
)

// Enum value maps for ScanConfig_TargetPlatform.
var (
	ScanConfig_TargetPlatform_name = map[int32]string{
		0: "TARGET_PLATFORM_UNSPECIFIED",
		1: "APP_ENGINE",
		2: "COMPUTE",
	}
	ScanConfig_TargetPlatform_value = map[string]int32{
		"TARGET_PLATFORM_UNSPECIFIED": 0,
		"APP_ENGINE":                  1,
		"COMPUTE":                     2,
	}
)

func (x ScanConfig_TargetPlatform) Enum() *ScanConfig_TargetPlatform {
	p := new(ScanConfig_TargetPlatform)
	*p = x
	return p
}

func (x ScanConfig_TargetPlatform) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScanConfig_TargetPlatform) Descriptor() protoreflect.EnumDescriptor {
	return file_google_cloud_websecurityscanner_v1beta_scan_config_proto_enumTypes[1].Descriptor()
}

func (ScanConfig_TargetPlatform) Type() protoreflect.EnumType {
	return &file_google_cloud_websecurityscanner_v1beta_scan_config_proto_enumTypes[1]
}

func (x ScanConfig_TargetPlatform) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScanConfig_TargetPlatform.Descriptor instead.
func (ScanConfig_TargetPlatform) EnumDescriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDescGZIP(), []int{0, 1}
}

// Scan risk levels supported by Cloud Web Security Scanner. LOW impact
// scanning will minimize requests with the potential to modify data. To
// achieve the maximum scan coverage, NORMAL risk level is recommended.
type ScanConfig_RiskLevel int32

const (
	// Use default, which is NORMAL.
	ScanConfig_RISK_LEVEL_UNSPECIFIED ScanConfig_RiskLevel = 0
	// Normal scanning (Recommended)
	ScanConfig_NORMAL ScanConfig_RiskLevel = 1
	// Lower impact scanning
	ScanConfig_LOW ScanConfig_RiskLevel = 2
)

// Enum value maps for ScanConfig_RiskLevel.
var (
	ScanConfig_RiskLevel_name = map[int32]string{
		0: "RISK_LEVEL_UNSPECIFIED",
		1: "NORMAL",
		2: "LOW",
	}
	ScanConfig_RiskLevel_value = map[string]int32{
		"RISK_LEVEL_UNSPECIFIED": 0,
		"NORMAL":                 1,
		"LOW":                    2,
	}
)

func (x ScanConfig_RiskLevel) Enum() *ScanConfig_RiskLevel {
	p := new(ScanConfig_RiskLevel)
	*p = x
	return p
}

func (x ScanConfig_RiskLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScanConfig_RiskLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_google_cloud_websecurityscanner_v1beta_scan_config_proto_enumTypes[2].Descriptor()
}

func (ScanConfig_RiskLevel) Type() protoreflect.EnumType {
	return &file_google_cloud_websecurityscanner_v1beta_scan_config_proto_enumTypes[2]
}

func (x ScanConfig_RiskLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScanConfig_RiskLevel.Descriptor instead.
func (ScanConfig_RiskLevel) EnumDescriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDescGZIP(), []int{0, 2}
}

// Controls export of scan configurations and results to Cloud Security
// Command Center.
type ScanConfig_ExportToSecurityCommandCenter int32

const (
	// Use default, which is ENABLED.
	ScanConfig_EXPORT_TO_SECURITY_COMMAND_CENTER_UNSPECIFIED ScanConfig_ExportToSecurityCommandCenter = 0
	// Export results of this scan to Cloud Security Command Center.
	ScanConfig_ENABLED ScanConfig_ExportToSecurityCommandCenter = 1
	// Do not export results of this scan to Cloud Security Command Center.
	ScanConfig_DISABLED ScanConfig_ExportToSecurityCommandCenter = 2
)

// Enum value maps for ScanConfig_ExportToSecurityCommandCenter.
var (
	ScanConfig_ExportToSecurityCommandCenter_name = map[int32]string{
		0: "EXPORT_TO_SECURITY_COMMAND_CENTER_UNSPECIFIED",
		1: "ENABLED",
		2: "DISABLED",
	}
	ScanConfig_ExportToSecurityCommandCenter_value = map[string]int32{
		"EXPORT_TO_SECURITY_COMMAND_CENTER_UNSPECIFIED": 0,
		"ENABLED":  1,
		"DISABLED": 2,
	}
)

func (x ScanConfig_ExportToSecurityCommandCenter) Enum() *ScanConfig_ExportToSecurityCommandCenter {
	p := new(ScanConfig_ExportToSecurityCommandCenter)
	*p = x
	return p
}

func (x ScanConfig_ExportToSecurityCommandCenter) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScanConfig_ExportToSecurityCommandCenter) Descriptor() protoreflect.EnumDescriptor {
	return file_google_cloud_websecurityscanner_v1beta_scan_config_proto_enumTypes[3].Descriptor()
}

func (ScanConfig_ExportToSecurityCommandCenter) Type() protoreflect.EnumType {
	return &file_google_cloud_websecurityscanner_v1beta_scan_config_proto_enumTypes[3]
}

func (x ScanConfig_ExportToSecurityCommandCenter) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScanConfig_ExportToSecurityCommandCenter.Descriptor instead.
func (ScanConfig_ExportToSecurityCommandCenter) EnumDescriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDescGZIP(), []int{0, 3}
}

// A ScanConfig resource contains the configurations to launch a scan.
type ScanConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The resource name of the ScanConfig. The name follows the format of
	// 'projects/{projectId}/scanConfigs/{scanConfigId}'. The ScanConfig IDs are
	// generated by the system.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Required. The user provided display name of the ScanConfig.
	DisplayName string `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// The maximum QPS during scanning. A valid value ranges from 5 to 20
	// inclusively. If the field is unspecified or its value is set 0, server will
	// default to 15. Other values outside of [5, 20] range will be rejected with
	// INVALID_ARGUMENT error.
	MaxQps int32 `protobuf:"varint,3,opt,name=max_qps,json=maxQps,proto3" json:"max_qps,omitempty"`
	// Required. The starting URLs from which the scanner finds site pages.
	StartingUrls []string `protobuf:"bytes,4,rep,name=starting_urls,json=startingUrls,proto3" json:"starting_urls,omitempty"`
	// The authentication configuration. If specified, service will use the
	// authentication configuration during scanning.
	Authentication *ScanConfig_Authentication `protobuf:"bytes,5,opt,name=authentication,proto3" json:"authentication,omitempty"`
	// The user agent used during scanning.
	UserAgent ScanConfig_UserAgent `protobuf:"varint,6,opt,name=user_agent,json=userAgent,proto3,enum=google.cloud.websecurityscanner.v1beta.ScanConfig_UserAgent" json:"user_agent,omitempty"`
	// The blacklist URL patterns as described in
	// https://cloud.google.com/security-scanner/docs/excluded-urls
	BlacklistPatterns []string `protobuf:"bytes,7,rep,name=blacklist_patterns,json=blacklistPatterns,proto3" json:"blacklist_patterns,omitempty"`
	// The schedule of the ScanConfig.
	Schedule *ScanConfig_Schedule `protobuf:"bytes,8,opt,name=schedule,proto3" json:"schedule,omitempty"`
	// Set of Cloud Platforms targeted by the scan. If empty, APP_ENGINE will be
	// used as a default.
	TargetPlatforms []ScanConfig_TargetPlatform `protobuf:"varint,9,rep,packed,name=target_platforms,json=targetPlatforms,proto3,enum=google.cloud.websecurityscanner.v1beta.ScanConfig_TargetPlatform" json:"target_platforms,omitempty"`
	// Controls export of scan configurations and results to Cloud Security
	// Command Center.
	ExportToSecurityCommandCenter ScanConfig_ExportToSecurityCommandCenter `protobuf:"varint,10,opt,name=export_to_security_command_center,json=exportToSecurityCommandCenter,proto3,enum=google.cloud.websecurityscanner.v1beta.ScanConfig_ExportToSecurityCommandCenter" json:"export_to_security_command_center,omitempty"`
	// Latest ScanRun if available.
	LatestRun *ScanRun `protobuf:"bytes,11,opt,name=latest_run,json=latestRun,proto3" json:"latest_run,omitempty"`
	// The risk level selected for the scan
	RiskLevel ScanConfig_RiskLevel `protobuf:"varint,12,opt,name=risk_level,json=riskLevel,proto3,enum=google.cloud.websecurityscanner.v1beta.ScanConfig_RiskLevel" json:"risk_level,omitempty"`
}

func (x *ScanConfig) Reset() {
	*x = ScanConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_scan_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScanConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanConfig) ProtoMessage() {}

func (x *ScanConfig) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_scan_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanConfig.ProtoReflect.Descriptor instead.
func (*ScanConfig) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDescGZIP(), []int{0}
}

func (x *ScanConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ScanConfig) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *ScanConfig) GetMaxQps() int32 {
	if x != nil {
		return x.MaxQps
	}
	return 0
}

func (x *ScanConfig) GetStartingUrls() []string {
	if x != nil {
		return x.StartingUrls
	}
	return nil
}

func (x *ScanConfig) GetAuthentication() *ScanConfig_Authentication {
	if x != nil {
		return x.Authentication
	}
	return nil
}

func (x *ScanConfig) GetUserAgent() ScanConfig_UserAgent {
	if x != nil {
		return x.UserAgent
	}
	return ScanConfig_USER_AGENT_UNSPECIFIED
}

func (x *ScanConfig) GetBlacklistPatterns() []string {
	if x != nil {
		return x.BlacklistPatterns
	}
	return nil
}

func (x *ScanConfig) GetSchedule() *ScanConfig_Schedule {
	if x != nil {
		return x.Schedule
	}
	return nil
}

func (x *ScanConfig) GetTargetPlatforms() []ScanConfig_TargetPlatform {
	if x != nil {
		return x.TargetPlatforms
	}
	return nil
}

func (x *ScanConfig) GetExportToSecurityCommandCenter() ScanConfig_ExportToSecurityCommandCenter {
	if x != nil {
		return x.ExportToSecurityCommandCenter
	}
	return ScanConfig_EXPORT_TO_SECURITY_COMMAND_CENTER_UNSPECIFIED
}

func (x *ScanConfig) GetLatestRun() *ScanRun {
	if x != nil {
		return x.LatestRun
	}
	return nil
}

func (x *ScanConfig) GetRiskLevel() ScanConfig_RiskLevel {
	if x != nil {
		return x.RiskLevel
	}
	return ScanConfig_RISK_LEVEL_UNSPECIFIED
}

// Scan authentication configuration.
type ScanConfig_Authentication struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required.
	// Authentication configuration
	//
	// Types that are assignable to Authentication:
	//	*ScanConfig_Authentication_GoogleAccount_
	//	*ScanConfig_Authentication_CustomAccount_
	Authentication isScanConfig_Authentication_Authentication `protobuf_oneof:"authentication"`
}

func (x *ScanConfig_Authentication) Reset() {
	*x = ScanConfig_Authentication{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_scan_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScanConfig_Authentication) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanConfig_Authentication) ProtoMessage() {}

func (x *ScanConfig_Authentication) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_scan_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanConfig_Authentication.ProtoReflect.Descriptor instead.
func (*ScanConfig_Authentication) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDescGZIP(), []int{0, 0}
}

func (m *ScanConfig_Authentication) GetAuthentication() isScanConfig_Authentication_Authentication {
	if m != nil {
		return m.Authentication
	}
	return nil
}

func (x *ScanConfig_Authentication) GetGoogleAccount() *ScanConfig_Authentication_GoogleAccount {
	if x, ok := x.GetAuthentication().(*ScanConfig_Authentication_GoogleAccount_); ok {
		return x.GoogleAccount
	}
	return nil
}

func (x *ScanConfig_Authentication) GetCustomAccount() *ScanConfig_Authentication_CustomAccount {
	if x, ok := x.GetAuthentication().(*ScanConfig_Authentication_CustomAccount_); ok {
		return x.CustomAccount
	}
	return nil
}

type isScanConfig_Authentication_Authentication interface {
	isScanConfig_Authentication_Authentication()
}

type ScanConfig_Authentication_GoogleAccount_ struct {
	// Authentication using a Google account.
	GoogleAccount *ScanConfig_Authentication_GoogleAccount `protobuf:"bytes,1,opt,name=google_account,json=googleAccount,proto3,oneof"`
}

type ScanConfig_Authentication_CustomAccount_ struct {
	// Authentication using a custom account.
	CustomAccount *ScanConfig_Authentication_CustomAccount `protobuf:"bytes,2,opt,name=custom_account,json=customAccount,proto3,oneof"`
}

func (*ScanConfig_Authentication_GoogleAccount_) isScanConfig_Authentication_Authentication() {}

func (*ScanConfig_Authentication_CustomAccount_) isScanConfig_Authentication_Authentication() {}

// Scan schedule configuration.
type ScanConfig_Schedule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A timestamp indicates when the next run will be scheduled. The value is
	// refreshed by the server after each run. If unspecified, it will default
	// to current server time, which means the scan will be scheduled to start
	// immediately.
	ScheduleTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=schedule_time,json=scheduleTime,proto3" json:"schedule_time,omitempty"`
	// Required. The duration of time between executions in days.
	IntervalDurationDays int32 `protobuf:"varint,2,opt,name=interval_duration_days,json=intervalDurationDays,proto3" json:"interval_duration_days,omitempty"`
}

func (x *ScanConfig_Schedule) Reset() {
	*x = ScanConfig_Schedule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_scan_config_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScanConfig_Schedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanConfig_Schedule) ProtoMessage() {}

func (x *ScanConfig_Schedule) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_scan_config_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanConfig_Schedule.ProtoReflect.Descriptor instead.
func (*ScanConfig_Schedule) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDescGZIP(), []int{0, 1}
}

func (x *ScanConfig_Schedule) GetScheduleTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduleTime
	}
	return nil
}

func (x *ScanConfig_Schedule) GetIntervalDurationDays() int32 {
	if x != nil {
		return x.IntervalDurationDays
	}
	return 0
}

// Describes authentication configuration that uses a Google account.
type ScanConfig_Authentication_GoogleAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The user name of the Google account.
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	// Required. Input only. The password of the Google account. The credential is stored encrypted
	// and not returned in any response nor included in audit logs.
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *ScanConfig_Authentication_GoogleAccount) Reset() {
	*x = ScanConfig_Authentication_GoogleAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_scan_config_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScanConfig_Authentication_GoogleAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanConfig_Authentication_GoogleAccount) ProtoMessage() {}

func (x *ScanConfig_Authentication_GoogleAccount) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_scan_config_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanConfig_Authentication_GoogleAccount.ProtoReflect.Descriptor instead.
func (*ScanConfig_Authentication_GoogleAccount) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *ScanConfig_Authentication_GoogleAccount) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ScanConfig_Authentication_GoogleAccount) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

// Describes authentication configuration that uses a custom account.
type ScanConfig_Authentication_CustomAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The user name of the custom account.
	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	// Required. Input only. The password of the custom account. The credential is stored encrypted
	// and not returned in any response nor included in audit logs.
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	// Required. The login form URL of the website.
	LoginUrl string `protobuf:"bytes,3,opt,name=login_url,json=loginUrl,proto3" json:"login_url,omitempty"`
}

func (x *ScanConfig_Authentication_CustomAccount) Reset() {
	*x = ScanConfig_Authentication_CustomAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_scan_config_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScanConfig_Authentication_CustomAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanConfig_Authentication_CustomAccount) ProtoMessage() {}

func (x *ScanConfig_Authentication_CustomAccount) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_scan_config_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanConfig_Authentication_CustomAccount.ProtoReflect.Descriptor instead.
func (*ScanConfig_Authentication_CustomAccount) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDescGZIP(), []int{0, 0, 1}
}

func (x *ScanConfig_Authentication_CustomAccount) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ScanConfig_Authentication_CustomAccount) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *ScanConfig_Authentication_CustomAccount) GetLoginUrl() string {
	if x != nil {
		return x.LoginUrl
	}
	return ""
}

var File_google_cloud_websecurityscanner_v1beta_scan_config_proto protoreflect.FileDescriptor

var file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDesc = []byte{
	0x0a, 0x38, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77,
	0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x26, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62, 0x65,
	0x74, 0x61, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77, 0x65, 0x62,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2f,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x72, 0x75, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc3, 0x0f, 0x0a, 0x0a, 0x53, 0x63, 0x61, 0x6e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0c, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x03, 0xe0, 0x41, 0x02, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f, 0x71, 0x70, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x51, 0x70, 0x73, 0x12, 0x28, 0x0a, 0x0d, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x55, 0x72, 0x6c, 0x73, 0x12, 0x69, 0x0a, 0x0e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x5b, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x3c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x53, 0x63, 0x61,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x12,
	0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x74, 0x65, 0x72,
	0x6e, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c,
	0x69, 0x73, 0x74, 0x50, 0x61, 0x74, 0x74, 0x65, 0x72, 0x6e, 0x73, 0x12, 0x57, 0x0a, 0x08, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x08, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x12, 0x6c, 0x0a, 0x10, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x41,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65,
	0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x73, 0x12, 0x9a, 0x01, 0x0a, 0x21, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x6f,
	0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e,
	0x64, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x50,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65,
	0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x6f, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x52, 0x1d, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x6f, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x12,
	0x4e, 0x0a, 0x0a, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x75, 0x6e, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x53, 0x63, 0x61,
	0x6e, 0x52, 0x75, 0x6e, 0x52, 0x09, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x52, 0x75, 0x6e, 0x12,
	0x5b, 0x0a, 0x0a, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x3c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x53, 0x63, 0x61,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x52, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x1a, 0xe4, 0x03, 0x0a,
	0x0e, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x78, 0x0a, 0x0e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61,
	0x2e, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x41, 0x75, 0x74, 0x68,
	0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x0d, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x78, 0x0a, 0x0e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x4f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x48, 0x00, 0x52, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x1a, 0x54, 0x0a, 0x0d, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x06, 0xe0, 0x41, 0x02, 0xe0, 0x41, 0x04, 0x52,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x1a, 0x76, 0x0a, 0x0d, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x06, 0xe0,
	0x41, 0x02, 0xe0, 0x41, 0x04, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12,
	0x20, 0x0a, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x08, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x55, 0x72,
	0x6c, 0x42, 0x10, 0x0a, 0x0e, 0x61, 0x75, 0x74, 0x68, 0x65, 0x6e, 0x74, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x1a, 0x86, 0x01, 0x0a, 0x08, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x12, 0x3f, 0x0a, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0c, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x39, 0x0a, 0x16, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x5f, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x14, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x79, 0x73, 0x22, 0x60, 0x0a, 0x09,
	0x55, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x53, 0x45,
	0x52, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x48, 0x52, 0x4f, 0x4d, 0x45, 0x5f,
	0x4c, 0x49, 0x4e, 0x55, 0x58, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x48, 0x52, 0x4f, 0x4d,
	0x45, 0x5f, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x53,
	0x41, 0x46, 0x41, 0x52, 0x49, 0x5f, 0x49, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x03, 0x22, 0x4e,
	0x0a, 0x0e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x46,
	0x4f, 0x52, 0x4d, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x50, 0x50, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x10,
	0x01, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x45, 0x10, 0x02, 0x22, 0x3c,
	0x0a, 0x09, 0x52, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x16, 0x52,
	0x49, 0x53, 0x4b, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x4f, 0x52, 0x4d, 0x41,
	0x4c, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x4c, 0x4f, 0x57, 0x10, 0x02, 0x22, 0x6d, 0x0a, 0x1d,
	0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x6f, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x31, 0x0a,
	0x2d, 0x45, 0x58, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x54, 0x4f, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52,
	0x49, 0x54, 0x59, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x41, 0x4e, 0x44, 0x5f, 0x43, 0x45, 0x4e, 0x54,
	0x45, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0b, 0x0a, 0x07, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0c, 0x0a,
	0x08, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x3a, 0x5f, 0xea, 0x41, 0x5c,
	0x0a, 0x2c, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2c,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x7d, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2f, 0x7b,
	0x73, 0x63, 0x61, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x7d, 0x42, 0x97, 0x02, 0x0a,
	0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x42, 0x0f, 0x53, 0x63, 0x61,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x58,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72,
	0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77, 0x65, 0x62, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x62, 0x65, 0x74, 0x61, 0x3b, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0xaa, 0x02, 0x26, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x57, 0x65, 0x62, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x56, 0x31, 0x42, 0x65, 0x74,
	0x61, 0xca, 0x02, 0x26, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x43, 0x6c, 0x6f, 0x75, 0x64,
	0x5c, 0x57, 0x65, 0x62, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x53, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x5c, 0x56, 0x31, 0x62, 0x65, 0x74, 0x61, 0xea, 0x02, 0x29, 0x47, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x3a, 0x3a, 0x57, 0x65, 0x62, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x3a, 0x3a,
	0x56, 0x31, 0x62, 0x65, 0x74, 0x61, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDescOnce sync.Once
	file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDescData = file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDesc
)

func file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDescGZIP() []byte {
	file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDescOnce.Do(func() {
		file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDescData)
	})
	return file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDescData
}

var file_google_cloud_websecurityscanner_v1beta_scan_config_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_google_cloud_websecurityscanner_v1beta_scan_config_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_google_cloud_websecurityscanner_v1beta_scan_config_proto_goTypes = []interface{}{
	(ScanConfig_UserAgent)(0),                       // 0: google.cloud.websecurityscanner.v1beta.ScanConfig.UserAgent
	(ScanConfig_TargetPlatform)(0),                  // 1: google.cloud.websecurityscanner.v1beta.ScanConfig.TargetPlatform
	(ScanConfig_RiskLevel)(0),                       // 2: google.cloud.websecurityscanner.v1beta.ScanConfig.RiskLevel
	(ScanConfig_ExportToSecurityCommandCenter)(0),   // 3: google.cloud.websecurityscanner.v1beta.ScanConfig.ExportToSecurityCommandCenter
	(*ScanConfig)(nil),                              // 4: google.cloud.websecurityscanner.v1beta.ScanConfig
	(*ScanConfig_Authentication)(nil),               // 5: google.cloud.websecurityscanner.v1beta.ScanConfig.Authentication
	(*ScanConfig_Schedule)(nil),                     // 6: google.cloud.websecurityscanner.v1beta.ScanConfig.Schedule
	(*ScanConfig_Authentication_GoogleAccount)(nil), // 7: google.cloud.websecurityscanner.v1beta.ScanConfig.Authentication.GoogleAccount
	(*ScanConfig_Authentication_CustomAccount)(nil), // 8: google.cloud.websecurityscanner.v1beta.ScanConfig.Authentication.CustomAccount
	(*ScanRun)(nil),                                 // 9: google.cloud.websecurityscanner.v1beta.ScanRun
	(*timestamppb.Timestamp)(nil),                   // 10: google.protobuf.Timestamp
}
var file_google_cloud_websecurityscanner_v1beta_scan_config_proto_depIdxs = []int32{
	5,  // 0: google.cloud.websecurityscanner.v1beta.ScanConfig.authentication:type_name -> google.cloud.websecurityscanner.v1beta.ScanConfig.Authentication
	0,  // 1: google.cloud.websecurityscanner.v1beta.ScanConfig.user_agent:type_name -> google.cloud.websecurityscanner.v1beta.ScanConfig.UserAgent
	6,  // 2: google.cloud.websecurityscanner.v1beta.ScanConfig.schedule:type_name -> google.cloud.websecurityscanner.v1beta.ScanConfig.Schedule
	1,  // 3: google.cloud.websecurityscanner.v1beta.ScanConfig.target_platforms:type_name -> google.cloud.websecurityscanner.v1beta.ScanConfig.TargetPlatform
	3,  // 4: google.cloud.websecurityscanner.v1beta.ScanConfig.export_to_security_command_center:type_name -> google.cloud.websecurityscanner.v1beta.ScanConfig.ExportToSecurityCommandCenter
	9,  // 5: google.cloud.websecurityscanner.v1beta.ScanConfig.latest_run:type_name -> google.cloud.websecurityscanner.v1beta.ScanRun
	2,  // 6: google.cloud.websecurityscanner.v1beta.ScanConfig.risk_level:type_name -> google.cloud.websecurityscanner.v1beta.ScanConfig.RiskLevel
	7,  // 7: google.cloud.websecurityscanner.v1beta.ScanConfig.Authentication.google_account:type_name -> google.cloud.websecurityscanner.v1beta.ScanConfig.Authentication.GoogleAccount
	8,  // 8: google.cloud.websecurityscanner.v1beta.ScanConfig.Authentication.custom_account:type_name -> google.cloud.websecurityscanner.v1beta.ScanConfig.Authentication.CustomAccount
	10, // 9: google.cloud.websecurityscanner.v1beta.ScanConfig.Schedule.schedule_time:type_name -> google.protobuf.Timestamp
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_google_cloud_websecurityscanner_v1beta_scan_config_proto_init() }
func file_google_cloud_websecurityscanner_v1beta_scan_config_proto_init() {
	if File_google_cloud_websecurityscanner_v1beta_scan_config_proto != nil {
		return
	}
	file_google_cloud_websecurityscanner_v1beta_scan_run_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_cloud_websecurityscanner_v1beta_scan_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScanConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_scan_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScanConfig_Authentication); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_scan_config_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScanConfig_Schedule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_scan_config_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScanConfig_Authentication_GoogleAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_scan_config_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScanConfig_Authentication_CustomAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_cloud_websecurityscanner_v1beta_scan_config_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*ScanConfig_Authentication_GoogleAccount_)(nil),
		(*ScanConfig_Authentication_CustomAccount_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_cloud_websecurityscanner_v1beta_scan_config_proto_goTypes,
		DependencyIndexes: file_google_cloud_websecurityscanner_v1beta_scan_config_proto_depIdxs,
		EnumInfos:         file_google_cloud_websecurityscanner_v1beta_scan_config_proto_enumTypes,
		MessageInfos:      file_google_cloud_websecurityscanner_v1beta_scan_config_proto_msgTypes,
	}.Build()
	File_google_cloud_websecurityscanner_v1beta_scan_config_proto = out.File
	file_google_cloud_websecurityscanner_v1beta_scan_config_proto_rawDesc = nil
	file_google_cloud_websecurityscanner_v1beta_scan_config_proto_goTypes = nil
	file_google_cloud_websecurityscanner_v1beta_scan_config_proto_depIdxs = nil
}
