// Copyright 2019 Google LLC.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.12.2
// source: google/cloud/websecurityscanner/v1beta/web_security_scanner.proto

package websecurityscanner

import (
	context "context"
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request for the `CreateScanConfig` method.
type CreateScanConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The parent resource name where the scan is created, which should be a
	// project resource name in the format 'projects/{projectId}'.
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// Required. The ScanConfig to be created.
	ScanConfig *ScanConfig `protobuf:"bytes,2,opt,name=scan_config,json=scanConfig,proto3" json:"scan_config,omitempty"`
}

func (x *CreateScanConfigRequest) Reset() {
	*x = CreateScanConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateScanConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateScanConfigRequest) ProtoMessage() {}

func (x *CreateScanConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateScanConfigRequest.ProtoReflect.Descriptor instead.
func (*CreateScanConfigRequest) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescGZIP(), []int{0}
}

func (x *CreateScanConfigRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *CreateScanConfigRequest) GetScanConfig() *ScanConfig {
	if x != nil {
		return x.ScanConfig
	}
	return nil
}

// Request for the `DeleteScanConfig` method.
type DeleteScanConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource name of the ScanConfig to be deleted. The name follows the
	// format of 'projects/{projectId}/scanConfigs/{scanConfigId}'.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteScanConfigRequest) Reset() {
	*x = DeleteScanConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteScanConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteScanConfigRequest) ProtoMessage() {}

func (x *DeleteScanConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteScanConfigRequest.ProtoReflect.Descriptor instead.
func (*DeleteScanConfigRequest) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescGZIP(), []int{1}
}

func (x *DeleteScanConfigRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Request for the `GetScanConfig` method.
type GetScanConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource name of the ScanConfig to be returned. The name follows the
	// format of 'projects/{projectId}/scanConfigs/{scanConfigId}'.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetScanConfigRequest) Reset() {
	*x = GetScanConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScanConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScanConfigRequest) ProtoMessage() {}

func (x *GetScanConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScanConfigRequest.ProtoReflect.Descriptor instead.
func (*GetScanConfigRequest) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescGZIP(), []int{2}
}

func (x *GetScanConfigRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Request for the `ListScanConfigs` method.
type ListScanConfigsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The parent resource name, which should be a project resource name in the
	// format 'projects/{projectId}'.
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// A token identifying a page of results to be returned. This should be a
	// `next_page_token` value returned from a previous List request.
	// If unspecified, the first page of results is returned.
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// The maximum number of ScanConfigs to return, can be limited by server.
	// If not specified or not positive, the implementation will select a
	// reasonable value.
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *ListScanConfigsRequest) Reset() {
	*x = ListScanConfigsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListScanConfigsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListScanConfigsRequest) ProtoMessage() {}

func (x *ListScanConfigsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListScanConfigsRequest.ProtoReflect.Descriptor instead.
func (*ListScanConfigsRequest) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescGZIP(), []int{3}
}

func (x *ListScanConfigsRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *ListScanConfigsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListScanConfigsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Request for the `UpdateScanConfigRequest` method.
type UpdateScanConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The ScanConfig to be updated. The name field must be set to identify the
	// resource to be updated. The values of fields not covered by the mask
	// will be ignored.
	ScanConfig *ScanConfig `protobuf:"bytes,2,opt,name=scan_config,json=scanConfig,proto3" json:"scan_config,omitempty"`
	// Required. The update mask applies to the resource. For the `FieldMask` definition,
	// see
	// https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,3,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *UpdateScanConfigRequest) Reset() {
	*x = UpdateScanConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateScanConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateScanConfigRequest) ProtoMessage() {}

func (x *UpdateScanConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateScanConfigRequest.ProtoReflect.Descriptor instead.
func (*UpdateScanConfigRequest) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateScanConfigRequest) GetScanConfig() *ScanConfig {
	if x != nil {
		return x.ScanConfig
	}
	return nil
}

func (x *UpdateScanConfigRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// Response for the `ListScanConfigs` method.
type ListScanConfigsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of ScanConfigs returned.
	ScanConfigs []*ScanConfig `protobuf:"bytes,1,rep,name=scan_configs,json=scanConfigs,proto3" json:"scan_configs,omitempty"`
	// Token to retrieve the next page of results, or empty if there are no
	// more results in the list.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListScanConfigsResponse) Reset() {
	*x = ListScanConfigsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListScanConfigsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListScanConfigsResponse) ProtoMessage() {}

func (x *ListScanConfigsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListScanConfigsResponse.ProtoReflect.Descriptor instead.
func (*ListScanConfigsResponse) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescGZIP(), []int{5}
}

func (x *ListScanConfigsResponse) GetScanConfigs() []*ScanConfig {
	if x != nil {
		return x.ScanConfigs
	}
	return nil
}

func (x *ListScanConfigsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// Request for the `StartScanRun` method.
type StartScanRunRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource name of the ScanConfig to be used. The name follows the
	// format of 'projects/{projectId}/scanConfigs/{scanConfigId}'.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *StartScanRunRequest) Reset() {
	*x = StartScanRunRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartScanRunRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartScanRunRequest) ProtoMessage() {}

func (x *StartScanRunRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartScanRunRequest.ProtoReflect.Descriptor instead.
func (*StartScanRunRequest) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescGZIP(), []int{6}
}

func (x *StartScanRunRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Request for the `GetScanRun` method.
type GetScanRunRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource name of the ScanRun to be returned. The name follows the
	// format of
	// 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetScanRunRequest) Reset() {
	*x = GetScanRunRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScanRunRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScanRunRequest) ProtoMessage() {}

func (x *GetScanRunRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScanRunRequest.ProtoReflect.Descriptor instead.
func (*GetScanRunRequest) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescGZIP(), []int{7}
}

func (x *GetScanRunRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Request for the `ListScanRuns` method.
type ListScanRunsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The parent resource name, which should be a scan resource name in the
	// format 'projects/{projectId}/scanConfigs/{scanConfigId}'.
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// A token identifying a page of results to be returned. This should be a
	// `next_page_token` value returned from a previous List request.
	// If unspecified, the first page of results is returned.
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// The maximum number of ScanRuns to return, can be limited by server.
	// If not specified or not positive, the implementation will select a
	// reasonable value.
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *ListScanRunsRequest) Reset() {
	*x = ListScanRunsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListScanRunsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListScanRunsRequest) ProtoMessage() {}

func (x *ListScanRunsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListScanRunsRequest.ProtoReflect.Descriptor instead.
func (*ListScanRunsRequest) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescGZIP(), []int{8}
}

func (x *ListScanRunsRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *ListScanRunsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListScanRunsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Response for the `ListScanRuns` method.
type ListScanRunsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of ScanRuns returned.
	ScanRuns []*ScanRun `protobuf:"bytes,1,rep,name=scan_runs,json=scanRuns,proto3" json:"scan_runs,omitempty"`
	// Token to retrieve the next page of results, or empty if there are no
	// more results in the list.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListScanRunsResponse) Reset() {
	*x = ListScanRunsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListScanRunsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListScanRunsResponse) ProtoMessage() {}

func (x *ListScanRunsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListScanRunsResponse.ProtoReflect.Descriptor instead.
func (*ListScanRunsResponse) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescGZIP(), []int{9}
}

func (x *ListScanRunsResponse) GetScanRuns() []*ScanRun {
	if x != nil {
		return x.ScanRuns
	}
	return nil
}

func (x *ListScanRunsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// Request for the `StopScanRun` method.
type StopScanRunRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource name of the ScanRun to be stopped. The name follows the
	// format of
	// 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *StopScanRunRequest) Reset() {
	*x = StopScanRunRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopScanRunRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopScanRunRequest) ProtoMessage() {}

func (x *StopScanRunRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopScanRunRequest.ProtoReflect.Descriptor instead.
func (*StopScanRunRequest) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescGZIP(), []int{10}
}

func (x *StopScanRunRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Request for the `ListCrawledUrls` method.
type ListCrawledUrlsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The parent resource name, which should be a scan run resource name in the
	// format
	// 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// A token identifying a page of results to be returned. This should be a
	// `next_page_token` value returned from a previous List request.
	// If unspecified, the first page of results is returned.
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// The maximum number of CrawledUrls to return, can be limited by server.
	// If not specified or not positive, the implementation will select a
	// reasonable value.
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *ListCrawledUrlsRequest) Reset() {
	*x = ListCrawledUrlsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCrawledUrlsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCrawledUrlsRequest) ProtoMessage() {}

func (x *ListCrawledUrlsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCrawledUrlsRequest.ProtoReflect.Descriptor instead.
func (*ListCrawledUrlsRequest) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescGZIP(), []int{11}
}

func (x *ListCrawledUrlsRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *ListCrawledUrlsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListCrawledUrlsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Response for the `ListCrawledUrls` method.
type ListCrawledUrlsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of CrawledUrls returned.
	CrawledUrls []*CrawledUrl `protobuf:"bytes,1,rep,name=crawled_urls,json=crawledUrls,proto3" json:"crawled_urls,omitempty"`
	// Token to retrieve the next page of results, or empty if there are no
	// more results in the list.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListCrawledUrlsResponse) Reset() {
	*x = ListCrawledUrlsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCrawledUrlsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCrawledUrlsResponse) ProtoMessage() {}

func (x *ListCrawledUrlsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCrawledUrlsResponse.ProtoReflect.Descriptor instead.
func (*ListCrawledUrlsResponse) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescGZIP(), []int{12}
}

func (x *ListCrawledUrlsResponse) GetCrawledUrls() []*CrawledUrl {
	if x != nil {
		return x.CrawledUrls
	}
	return nil
}

func (x *ListCrawledUrlsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// Request for the `GetFinding` method.
type GetFindingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource name of the Finding to be returned. The name follows the
	// format of
	// 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}/findings/{findingId}'.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetFindingRequest) Reset() {
	*x = GetFindingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFindingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFindingRequest) ProtoMessage() {}

func (x *GetFindingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFindingRequest.ProtoReflect.Descriptor instead.
func (*GetFindingRequest) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescGZIP(), []int{13}
}

func (x *GetFindingRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Request for the `ListFindings` method.
type ListFindingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The parent resource name, which should be a scan run resource name in the
	// format
	// 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// Required. The filter expression. The expression must be in the format: <field>
	// <operator> <value>.
	// Supported field: 'finding_type'.
	// Supported operator: '='.
	Filter string `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	// A token identifying a page of results to be returned. This should be a
	// `next_page_token` value returned from a previous List request.
	// If unspecified, the first page of results is returned.
	PageToken string `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// The maximum number of Findings to return, can be limited by server.
	// If not specified or not positive, the implementation will select a
	// reasonable value.
	PageSize int32 `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *ListFindingsRequest) Reset() {
	*x = ListFindingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListFindingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFindingsRequest) ProtoMessage() {}

func (x *ListFindingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFindingsRequest.ProtoReflect.Descriptor instead.
func (*ListFindingsRequest) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescGZIP(), []int{14}
}

func (x *ListFindingsRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *ListFindingsRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListFindingsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListFindingsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Response for the `ListFindings` method.
type ListFindingsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of Findings returned.
	Findings []*Finding `protobuf:"bytes,1,rep,name=findings,proto3" json:"findings,omitempty"`
	// Token to retrieve the next page of results, or empty if there are no
	// more results in the list.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListFindingsResponse) Reset() {
	*x = ListFindingsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListFindingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFindingsResponse) ProtoMessage() {}

func (x *ListFindingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFindingsResponse.ProtoReflect.Descriptor instead.
func (*ListFindingsResponse) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescGZIP(), []int{15}
}

func (x *ListFindingsResponse) GetFindings() []*Finding {
	if x != nil {
		return x.Findings
	}
	return nil
}

func (x *ListFindingsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// Request for the `ListFindingTypeStats` method.
type ListFindingTypeStatsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The parent resource name, which should be a scan run resource name in the
	// format
	// 'projects/{projectId}/scanConfigs/{scanConfigId}/scanRuns/{scanRunId}'.
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
}

func (x *ListFindingTypeStatsRequest) Reset() {
	*x = ListFindingTypeStatsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListFindingTypeStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFindingTypeStatsRequest) ProtoMessage() {}

func (x *ListFindingTypeStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFindingTypeStatsRequest.ProtoReflect.Descriptor instead.
func (*ListFindingTypeStatsRequest) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescGZIP(), []int{16}
}

func (x *ListFindingTypeStatsRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

// Response for the `ListFindingTypeStats` method.
type ListFindingTypeStatsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of FindingTypeStats returned.
	FindingTypeStats []*FindingTypeStats `protobuf:"bytes,1,rep,name=finding_type_stats,json=findingTypeStats,proto3" json:"finding_type_stats,omitempty"`
}

func (x *ListFindingTypeStatsResponse) Reset() {
	*x = ListFindingTypeStatsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListFindingTypeStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFindingTypeStatsResponse) ProtoMessage() {}

func (x *ListFindingTypeStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFindingTypeStatsResponse.ProtoReflect.Descriptor instead.
func (*ListFindingTypeStatsResponse) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescGZIP(), []int{17}
}

func (x *ListFindingTypeStatsResponse) GetFindingTypeStats() []*FindingTypeStats {
	if x != nil {
		return x.FindingTypeStats
	}
	return nil
}

var File_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto protoreflect.FileDescriptor

var file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDesc = []byte{
	0x0a, 0x41, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77,
	0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2f, 0x77, 0x65, 0x62, 0x5f, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x26, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77, 0x65, 0x62,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2f,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2f, 0x63, 0x72, 0x61, 0x77, 0x6c, 0x65, 0x64, 0x5f, 0x75,
	0x72, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61,
	0x2f, 0x66, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77, 0x65, 0x62,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2f,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2f, 0x66, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x38, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77, 0x65,
	0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74,
	0x61, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x72, 0x75, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xc0, 0x01, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x06, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x33, 0xe0, 0x41, 0x02,
	0xfa, 0x41, 0x2d, 0x0a, 0x2b, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x58, 0x0a, 0x0b, 0x73, 0x63, 0x61, 0x6e,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0a, 0x73, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x22, 0x63, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x63, 0x61, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x34, 0xe0, 0x41, 0x02,
	0xfa, 0x41, 0x2e, 0x0a, 0x2c, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x60, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x63,
	0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x48, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x34, 0xe0,
	0x41, 0x02, 0xfa, 0x41, 0x2e, 0x0a, 0x2c, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xa1, 0x01, 0x0a, 0x16, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x33, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x2d, 0x0a, 0x2b, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xb5, 0x01,
	0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x58, 0x0a, 0x0b, 0x73, 0x63, 0x61,
	0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65,
	0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0a, 0x73, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x40, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61,
	0x73, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x4d, 0x61, 0x73, 0x6b, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4d, 0x61, 0x73, 0x6b, 0x22, 0x98, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63,
	0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x55, 0x0a, 0x0c, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61,
	0x2e, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0b, 0x73, 0x63, 0x61,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74,
	0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x22, 0x5f, 0x0a, 0x13, 0x53, 0x74, 0x61, 0x72, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x34, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x2e, 0x0a, 0x2c, 0x77,
	0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x22, 0x5a, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x45, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x31, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x2b, 0x0a, 0x29, 0x77, 0x65,
	0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x53, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x9f, 0x01,
	0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4c, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x34, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x2e, 0x0a, 0x2c, 0x77,
	0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22,
	0x8c, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x09, 0x73, 0x63, 0x61, 0x6e,
	0x5f, 0x72, 0x75, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x62, 0x65, 0x74, 0x61, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e, 0x52, 0x08, 0x73, 0x63,
	0x61, 0x6e, 0x52, 0x75, 0x6e, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x5b,
	0x0a, 0x12, 0x53, 0x74, 0x6f, 0x70, 0x53, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x45, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x31, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x2b, 0x0a, 0x29, 0x77, 0x65, 0x62, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x53, 0x63,
	0x61, 0x6e, 0x52, 0x75, 0x6e, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x9f, 0x01, 0x0a, 0x16,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x72, 0x61, 0x77, 0x6c, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x49, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x31, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x2b, 0x0a, 0x29,
	0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x53, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x98, 0x01,
	0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x72, 0x61, 0x77, 0x6c, 0x65, 0x64, 0x55, 0x72, 0x6c,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x55, 0x0a, 0x0c, 0x63, 0x72, 0x61,
	0x77, 0x6c, 0x65, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x32, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77,
	0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x43, 0x72, 0x61, 0x77, 0x6c, 0x65, 0x64,
	0x55, 0x72, 0x6c, 0x52, 0x0b, 0x63, 0x72, 0x61, 0x77, 0x6c, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x73,
	0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50,
	0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x5a, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x46,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x45, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x31, 0xe0, 0x41, 0x02,
	0xfa, 0x41, 0x2b, 0x0a, 0x29, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x46, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0xb9, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x49, 0x0a, 0x06,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x31, 0xe0, 0x41,
	0x02, 0xfa, 0x41, 0x2b, 0x0a, 0x29, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x53, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e, 0x52,
	0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x22, 0x8b, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b, 0x0a, 0x08, 0x66, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x62, 0x65, 0x74, 0x61, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x08, 0x66, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x68,
	0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x49, 0x0a,
	0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x31, 0xe0,
	0x41, 0x02, 0xfa, 0x41, 0x2b, 0x0a, 0x29, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x53, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e,
	0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x22, 0x86, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73,
	0x74, 0x46, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x12, 0x66, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x46,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52,
	0x10, 0x66, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x73, 0x32, 0x8e, 0x16, 0x0a, 0x12, 0x57, 0x65, 0x62, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0xda, 0x01, 0x0a, 0x10, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3f, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x63, 0x61,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65,
	0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x22, 0x51, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x36, 0x22, 0x27, 0x2f, 0x76, 0x31, 0x62,
	0x65, 0x74, 0x61, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x73, 0x3a, 0x0b, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0xda, 0x41, 0x12, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2c, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0xa3, 0x01, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3f, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62,
	0x65, 0x74, 0x61, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x36, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x2a, 0x27, 0x2f, 0x76, 0x31,
	0x62, 0x65, 0x74, 0x61, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x73, 0x2f, 0x2a, 0x7d, 0xda, 0x41, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0xb9, 0x01, 0x0a, 0x0d,
	0x47, 0x65, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3c, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x62, 0x65, 0x74, 0x61, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22,
	0x36, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x12, 0x27, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f,
	0x2a, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2f, 0x2a, 0x7d,
	0xda, 0x41, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0xcc, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74,
	0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x3e, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x62, 0x65, 0x74, 0x61, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x62, 0x65, 0x74, 0x61, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x38, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x29, 0x12, 0x27, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2f, 0x7b, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a,
	0x7d, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0xda, 0x41, 0x06,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0xeb, 0x01, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3f, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x62, 0x65, 0x74, 0x61, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x61, 0x6e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x22, 0x62, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x42, 0x32, 0x33, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74,
	0x61, 0x2f, 0x7b, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x6e,
	0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x73,
	0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x0b, 0x73,
	0x63, 0x61, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0xda, 0x41, 0x17, 0x73, 0x63, 0x61,
	0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x6d, 0x61, 0x73, 0x6b, 0x12, 0xbd, 0x01, 0x0a, 0x0c, 0x53, 0x74, 0x61, 0x72, 0x74, 0x53, 0x63,
	0x61, 0x6e, 0x52, 0x75, 0x6e, 0x12, 0x3b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x53, 0x63, 0x61, 0x6e,
	0x52, 0x75, 0x6e, 0x22, 0x3f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x32, 0x22, 0x2d, 0x2f, 0x76, 0x31,
	0x62, 0x65, 0x74, 0x61, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x3a, 0x01, 0x2a, 0xda, 0x41, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0xbb, 0x01, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x53, 0x63, 0x61, 0x6e,
	0x52, 0x75, 0x6e, 0x12, 0x39, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65,
	0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e, 0x22,
	0x41, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x34, 0x12, 0x32, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f,
	0x2a, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2f, 0x2a, 0x2f,
	0x73, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0xda, 0x41, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0xce, 0x01, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x52,
	0x75, 0x6e, 0x73, 0x12, 0x3b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e,
	0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63,
	0x61, 0x6e, 0x52, 0x75, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x43,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x34, 0x12, 0x32, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2f,
	0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73,
	0x2f, 0x2a, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2f, 0x2a,
	0x7d, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e, 0x73, 0xda, 0x41, 0x06, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x12, 0xc5, 0x01, 0x0a, 0x0b, 0x53, 0x74, 0x6f, 0x70, 0x53, 0x63, 0x61, 0x6e,
	0x52, 0x75, 0x6e, 0x12, 0x3a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x53, 0x74, 0x6f,
	0x70, 0x53, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77,
	0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x53, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e,
	0x22, 0x49, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3c, 0x22, 0x37, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74,
	0x61, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73,
	0x2f, 0x2a, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2f, 0x2a,
	0x2f, 0x73, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x73, 0x74, 0x6f,
	0x70, 0x3a, 0x01, 0x2a, 0xda, 0x41, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0xe5, 0x01, 0x0a, 0x0f,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x72, 0x61, 0x77, 0x6c, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x73, 0x12,
	0x3e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77,
	0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x72, 0x61,
	0x77, 0x6c, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77,
	0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x72, 0x61,
	0x77, 0x6c, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x51, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x42, 0x12, 0x40, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74,
	0x61, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73,
	0x2f, 0x2a, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x63,
	0x72, 0x61, 0x77, 0x6c, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x73, 0xda, 0x41, 0x06, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x12, 0xc6, 0x01, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x12, 0x39, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x47, 0x65, 0x74, 0x46,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x22, 0x4c,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3f, 0x12, 0x3d, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2f,
	0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a,
	0x2f, 0x73, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2f, 0x2a, 0x2f, 0x73,
	0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x66, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x73, 0x2f, 0x2a, 0x7d, 0xda, 0x41, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0xe0, 0x01, 0x0a,
	0x0c, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x3b, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62,
	0x65, 0x74, 0x61, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x55, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3f,
	0x12, 0x3d, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x63, 0x61,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x52,
	0x75, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x66, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0xda,
	0x41, 0x0d, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2c, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0xf9, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x43, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74,
	0x61, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x56, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x47, 0x12, 0x45, 0x2f, 0x76, 0x31,
	0x62, 0x65, 0x74, 0x61, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x52, 0x75, 0x6e, 0x73, 0x2f, 0x2a,
	0x7d, 0x2f, 0x66, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x73, 0xda, 0x41, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x1a, 0x55, 0xca, 0x41, 0x21,
	0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f,
	0x6d, 0xd2, 0x41, 0x2e, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x77, 0x77, 0x77, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61,
	0x75, 0x74, 0x68, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x42, 0x9f, 0x02, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74,
	0x61, 0x42, 0x17, 0x57, 0x65, 0x62, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x53, 0x63,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x62,
	0x65, 0x74, 0x61, 0x3b, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73,
	0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0xaa, 0x02, 0x26, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x43, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x57, 0x65, 0x62, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x56, 0x31, 0x42, 0x65, 0x74, 0x61, 0xca,
	0x02, 0x26, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x5c, 0x57,
	0x65, 0x62, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x5c, 0x56, 0x31, 0x62, 0x65, 0x74, 0x61, 0xea, 0x02, 0x29, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x3a, 0x3a, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x3a, 0x3a, 0x57, 0x65, 0x62, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x3a, 0x3a, 0x56, 0x31,
	0x62, 0x65, 0x74, 0x61, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescOnce sync.Once
	file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescData = file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDesc
)

func file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescGZIP() []byte {
	file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescOnce.Do(func() {
		file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescData)
	})
	return file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDescData
}

var file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_goTypes = []interface{}{
	(*CreateScanConfigRequest)(nil),      // 0: google.cloud.websecurityscanner.v1beta.CreateScanConfigRequest
	(*DeleteScanConfigRequest)(nil),      // 1: google.cloud.websecurityscanner.v1beta.DeleteScanConfigRequest
	(*GetScanConfigRequest)(nil),         // 2: google.cloud.websecurityscanner.v1beta.GetScanConfigRequest
	(*ListScanConfigsRequest)(nil),       // 3: google.cloud.websecurityscanner.v1beta.ListScanConfigsRequest
	(*UpdateScanConfigRequest)(nil),      // 4: google.cloud.websecurityscanner.v1beta.UpdateScanConfigRequest
	(*ListScanConfigsResponse)(nil),      // 5: google.cloud.websecurityscanner.v1beta.ListScanConfigsResponse
	(*StartScanRunRequest)(nil),          // 6: google.cloud.websecurityscanner.v1beta.StartScanRunRequest
	(*GetScanRunRequest)(nil),            // 7: google.cloud.websecurityscanner.v1beta.GetScanRunRequest
	(*ListScanRunsRequest)(nil),          // 8: google.cloud.websecurityscanner.v1beta.ListScanRunsRequest
	(*ListScanRunsResponse)(nil),         // 9: google.cloud.websecurityscanner.v1beta.ListScanRunsResponse
	(*StopScanRunRequest)(nil),           // 10: google.cloud.websecurityscanner.v1beta.StopScanRunRequest
	(*ListCrawledUrlsRequest)(nil),       // 11: google.cloud.websecurityscanner.v1beta.ListCrawledUrlsRequest
	(*ListCrawledUrlsResponse)(nil),      // 12: google.cloud.websecurityscanner.v1beta.ListCrawledUrlsResponse
	(*GetFindingRequest)(nil),            // 13: google.cloud.websecurityscanner.v1beta.GetFindingRequest
	(*ListFindingsRequest)(nil),          // 14: google.cloud.websecurityscanner.v1beta.ListFindingsRequest
	(*ListFindingsResponse)(nil),         // 15: google.cloud.websecurityscanner.v1beta.ListFindingsResponse
	(*ListFindingTypeStatsRequest)(nil),  // 16: google.cloud.websecurityscanner.v1beta.ListFindingTypeStatsRequest
	(*ListFindingTypeStatsResponse)(nil), // 17: google.cloud.websecurityscanner.v1beta.ListFindingTypeStatsResponse
	(*ScanConfig)(nil),                   // 18: google.cloud.websecurityscanner.v1beta.ScanConfig
	(*fieldmaskpb.FieldMask)(nil),        // 19: google.protobuf.FieldMask
	(*ScanRun)(nil),                      // 20: google.cloud.websecurityscanner.v1beta.ScanRun
	(*CrawledUrl)(nil),                   // 21: google.cloud.websecurityscanner.v1beta.CrawledUrl
	(*Finding)(nil),                      // 22: google.cloud.websecurityscanner.v1beta.Finding
	(*FindingTypeStats)(nil),             // 23: google.cloud.websecurityscanner.v1beta.FindingTypeStats
	(*emptypb.Empty)(nil),                // 24: google.protobuf.Empty
}
var file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_depIdxs = []int32{
	18, // 0: google.cloud.websecurityscanner.v1beta.CreateScanConfigRequest.scan_config:type_name -> google.cloud.websecurityscanner.v1beta.ScanConfig
	18, // 1: google.cloud.websecurityscanner.v1beta.UpdateScanConfigRequest.scan_config:type_name -> google.cloud.websecurityscanner.v1beta.ScanConfig
	19, // 2: google.cloud.websecurityscanner.v1beta.UpdateScanConfigRequest.update_mask:type_name -> google.protobuf.FieldMask
	18, // 3: google.cloud.websecurityscanner.v1beta.ListScanConfigsResponse.scan_configs:type_name -> google.cloud.websecurityscanner.v1beta.ScanConfig
	20, // 4: google.cloud.websecurityscanner.v1beta.ListScanRunsResponse.scan_runs:type_name -> google.cloud.websecurityscanner.v1beta.ScanRun
	21, // 5: google.cloud.websecurityscanner.v1beta.ListCrawledUrlsResponse.crawled_urls:type_name -> google.cloud.websecurityscanner.v1beta.CrawledUrl
	22, // 6: google.cloud.websecurityscanner.v1beta.ListFindingsResponse.findings:type_name -> google.cloud.websecurityscanner.v1beta.Finding
	23, // 7: google.cloud.websecurityscanner.v1beta.ListFindingTypeStatsResponse.finding_type_stats:type_name -> google.cloud.websecurityscanner.v1beta.FindingTypeStats
	0,  // 8: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.CreateScanConfig:input_type -> google.cloud.websecurityscanner.v1beta.CreateScanConfigRequest
	1,  // 9: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.DeleteScanConfig:input_type -> google.cloud.websecurityscanner.v1beta.DeleteScanConfigRequest
	2,  // 10: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.GetScanConfig:input_type -> google.cloud.websecurityscanner.v1beta.GetScanConfigRequest
	3,  // 11: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.ListScanConfigs:input_type -> google.cloud.websecurityscanner.v1beta.ListScanConfigsRequest
	4,  // 12: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.UpdateScanConfig:input_type -> google.cloud.websecurityscanner.v1beta.UpdateScanConfigRequest
	6,  // 13: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.StartScanRun:input_type -> google.cloud.websecurityscanner.v1beta.StartScanRunRequest
	7,  // 14: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.GetScanRun:input_type -> google.cloud.websecurityscanner.v1beta.GetScanRunRequest
	8,  // 15: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.ListScanRuns:input_type -> google.cloud.websecurityscanner.v1beta.ListScanRunsRequest
	10, // 16: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.StopScanRun:input_type -> google.cloud.websecurityscanner.v1beta.StopScanRunRequest
	11, // 17: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.ListCrawledUrls:input_type -> google.cloud.websecurityscanner.v1beta.ListCrawledUrlsRequest
	13, // 18: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.GetFinding:input_type -> google.cloud.websecurityscanner.v1beta.GetFindingRequest
	14, // 19: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.ListFindings:input_type -> google.cloud.websecurityscanner.v1beta.ListFindingsRequest
	16, // 20: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.ListFindingTypeStats:input_type -> google.cloud.websecurityscanner.v1beta.ListFindingTypeStatsRequest
	18, // 21: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.CreateScanConfig:output_type -> google.cloud.websecurityscanner.v1beta.ScanConfig
	24, // 22: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.DeleteScanConfig:output_type -> google.protobuf.Empty
	18, // 23: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.GetScanConfig:output_type -> google.cloud.websecurityscanner.v1beta.ScanConfig
	5,  // 24: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.ListScanConfigs:output_type -> google.cloud.websecurityscanner.v1beta.ListScanConfigsResponse
	18, // 25: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.UpdateScanConfig:output_type -> google.cloud.websecurityscanner.v1beta.ScanConfig
	20, // 26: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.StartScanRun:output_type -> google.cloud.websecurityscanner.v1beta.ScanRun
	20, // 27: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.GetScanRun:output_type -> google.cloud.websecurityscanner.v1beta.ScanRun
	9,  // 28: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.ListScanRuns:output_type -> google.cloud.websecurityscanner.v1beta.ListScanRunsResponse
	20, // 29: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.StopScanRun:output_type -> google.cloud.websecurityscanner.v1beta.ScanRun
	12, // 30: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.ListCrawledUrls:output_type -> google.cloud.websecurityscanner.v1beta.ListCrawledUrlsResponse
	22, // 31: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.GetFinding:output_type -> google.cloud.websecurityscanner.v1beta.Finding
	15, // 32: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.ListFindings:output_type -> google.cloud.websecurityscanner.v1beta.ListFindingsResponse
	17, // 33: google.cloud.websecurityscanner.v1beta.WebSecurityScanner.ListFindingTypeStats:output_type -> google.cloud.websecurityscanner.v1beta.ListFindingTypeStatsResponse
	21, // [21:34] is the sub-list for method output_type
	8,  // [8:21] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_init() }
func file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_init() {
	if File_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto != nil {
		return
	}
	file_google_cloud_websecurityscanner_v1beta_crawled_url_proto_init()
	file_google_cloud_websecurityscanner_v1beta_finding_proto_init()
	file_google_cloud_websecurityscanner_v1beta_finding_type_stats_proto_init()
	file_google_cloud_websecurityscanner_v1beta_scan_config_proto_init()
	file_google_cloud_websecurityscanner_v1beta_scan_run_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateScanConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteScanConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScanConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListScanConfigsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateScanConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListScanConfigsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartScanRunRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetScanRunRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListScanRunsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListScanRunsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopScanRunRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCrawledUrlsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCrawledUrlsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFindingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListFindingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListFindingsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListFindingTypeStatsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListFindingTypeStatsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_goTypes,
		DependencyIndexes: file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_depIdxs,
		MessageInfos:      file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_msgTypes,
	}.Build()
	File_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto = out.File
	file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_rawDesc = nil
	file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_goTypes = nil
	file_google_cloud_websecurityscanner_v1beta_web_security_scanner_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// WebSecurityScannerClient is the client API for WebSecurityScanner service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type WebSecurityScannerClient interface {
	// Creates a new ScanConfig.
	CreateScanConfig(ctx context.Context, in *CreateScanConfigRequest, opts ...grpc.CallOption) (*ScanConfig, error)
	// Deletes an existing ScanConfig and its child resources.
	DeleteScanConfig(ctx context.Context, in *DeleteScanConfigRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Gets a ScanConfig.
	GetScanConfig(ctx context.Context, in *GetScanConfigRequest, opts ...grpc.CallOption) (*ScanConfig, error)
	// Lists ScanConfigs under a given project.
	ListScanConfigs(ctx context.Context, in *ListScanConfigsRequest, opts ...grpc.CallOption) (*ListScanConfigsResponse, error)
	// Updates a ScanConfig. This method support partial update of a ScanConfig.
	UpdateScanConfig(ctx context.Context, in *UpdateScanConfigRequest, opts ...grpc.CallOption) (*ScanConfig, error)
	// Start a ScanRun according to the given ScanConfig.
	StartScanRun(ctx context.Context, in *StartScanRunRequest, opts ...grpc.CallOption) (*ScanRun, error)
	// Gets a ScanRun.
	GetScanRun(ctx context.Context, in *GetScanRunRequest, opts ...grpc.CallOption) (*ScanRun, error)
	// Lists ScanRuns under a given ScanConfig, in descending order of ScanRun
	// stop time.
	ListScanRuns(ctx context.Context, in *ListScanRunsRequest, opts ...grpc.CallOption) (*ListScanRunsResponse, error)
	// Stops a ScanRun. The stopped ScanRun is returned.
	StopScanRun(ctx context.Context, in *StopScanRunRequest, opts ...grpc.CallOption) (*ScanRun, error)
	// List CrawledUrls under a given ScanRun.
	ListCrawledUrls(ctx context.Context, in *ListCrawledUrlsRequest, opts ...grpc.CallOption) (*ListCrawledUrlsResponse, error)
	// Gets a Finding.
	GetFinding(ctx context.Context, in *GetFindingRequest, opts ...grpc.CallOption) (*Finding, error)
	// List Findings under a given ScanRun.
	ListFindings(ctx context.Context, in *ListFindingsRequest, opts ...grpc.CallOption) (*ListFindingsResponse, error)
	// List all FindingTypeStats under a given ScanRun.
	ListFindingTypeStats(ctx context.Context, in *ListFindingTypeStatsRequest, opts ...grpc.CallOption) (*ListFindingTypeStatsResponse, error)
}

type webSecurityScannerClient struct {
	cc grpc.ClientConnInterface
}

func NewWebSecurityScannerClient(cc grpc.ClientConnInterface) WebSecurityScannerClient {
	return &webSecurityScannerClient{cc}
}

func (c *webSecurityScannerClient) CreateScanConfig(ctx context.Context, in *CreateScanConfigRequest, opts ...grpc.CallOption) (*ScanConfig, error) {
	out := new(ScanConfig)
	err := c.cc.Invoke(ctx, "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/CreateScanConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webSecurityScannerClient) DeleteScanConfig(ctx context.Context, in *DeleteScanConfigRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/DeleteScanConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webSecurityScannerClient) GetScanConfig(ctx context.Context, in *GetScanConfigRequest, opts ...grpc.CallOption) (*ScanConfig, error) {
	out := new(ScanConfig)
	err := c.cc.Invoke(ctx, "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/GetScanConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webSecurityScannerClient) ListScanConfigs(ctx context.Context, in *ListScanConfigsRequest, opts ...grpc.CallOption) (*ListScanConfigsResponse, error) {
	out := new(ListScanConfigsResponse)
	err := c.cc.Invoke(ctx, "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/ListScanConfigs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webSecurityScannerClient) UpdateScanConfig(ctx context.Context, in *UpdateScanConfigRequest, opts ...grpc.CallOption) (*ScanConfig, error) {
	out := new(ScanConfig)
	err := c.cc.Invoke(ctx, "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/UpdateScanConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webSecurityScannerClient) StartScanRun(ctx context.Context, in *StartScanRunRequest, opts ...grpc.CallOption) (*ScanRun, error) {
	out := new(ScanRun)
	err := c.cc.Invoke(ctx, "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/StartScanRun", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webSecurityScannerClient) GetScanRun(ctx context.Context, in *GetScanRunRequest, opts ...grpc.CallOption) (*ScanRun, error) {
	out := new(ScanRun)
	err := c.cc.Invoke(ctx, "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/GetScanRun", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webSecurityScannerClient) ListScanRuns(ctx context.Context, in *ListScanRunsRequest, opts ...grpc.CallOption) (*ListScanRunsResponse, error) {
	out := new(ListScanRunsResponse)
	err := c.cc.Invoke(ctx, "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/ListScanRuns", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webSecurityScannerClient) StopScanRun(ctx context.Context, in *StopScanRunRequest, opts ...grpc.CallOption) (*ScanRun, error) {
	out := new(ScanRun)
	err := c.cc.Invoke(ctx, "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/StopScanRun", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webSecurityScannerClient) ListCrawledUrls(ctx context.Context, in *ListCrawledUrlsRequest, opts ...grpc.CallOption) (*ListCrawledUrlsResponse, error) {
	out := new(ListCrawledUrlsResponse)
	err := c.cc.Invoke(ctx, "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/ListCrawledUrls", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webSecurityScannerClient) GetFinding(ctx context.Context, in *GetFindingRequest, opts ...grpc.CallOption) (*Finding, error) {
	out := new(Finding)
	err := c.cc.Invoke(ctx, "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/GetFinding", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webSecurityScannerClient) ListFindings(ctx context.Context, in *ListFindingsRequest, opts ...grpc.CallOption) (*ListFindingsResponse, error) {
	out := new(ListFindingsResponse)
	err := c.cc.Invoke(ctx, "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/ListFindings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *webSecurityScannerClient) ListFindingTypeStats(ctx context.Context, in *ListFindingTypeStatsRequest, opts ...grpc.CallOption) (*ListFindingTypeStatsResponse, error) {
	out := new(ListFindingTypeStatsResponse)
	err := c.cc.Invoke(ctx, "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/ListFindingTypeStats", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WebSecurityScannerServer is the server API for WebSecurityScanner service.
type WebSecurityScannerServer interface {
	// Creates a new ScanConfig.
	CreateScanConfig(context.Context, *CreateScanConfigRequest) (*ScanConfig, error)
	// Deletes an existing ScanConfig and its child resources.
	DeleteScanConfig(context.Context, *DeleteScanConfigRequest) (*emptypb.Empty, error)
	// Gets a ScanConfig.
	GetScanConfig(context.Context, *GetScanConfigRequest) (*ScanConfig, error)
	// Lists ScanConfigs under a given project.
	ListScanConfigs(context.Context, *ListScanConfigsRequest) (*ListScanConfigsResponse, error)
	// Updates a ScanConfig. This method support partial update of a ScanConfig.
	UpdateScanConfig(context.Context, *UpdateScanConfigRequest) (*ScanConfig, error)
	// Start a ScanRun according to the given ScanConfig.
	StartScanRun(context.Context, *StartScanRunRequest) (*ScanRun, error)
	// Gets a ScanRun.
	GetScanRun(context.Context, *GetScanRunRequest) (*ScanRun, error)
	// Lists ScanRuns under a given ScanConfig, in descending order of ScanRun
	// stop time.
	ListScanRuns(context.Context, *ListScanRunsRequest) (*ListScanRunsResponse, error)
	// Stops a ScanRun. The stopped ScanRun is returned.
	StopScanRun(context.Context, *StopScanRunRequest) (*ScanRun, error)
	// List CrawledUrls under a given ScanRun.
	ListCrawledUrls(context.Context, *ListCrawledUrlsRequest) (*ListCrawledUrlsResponse, error)
	// Gets a Finding.
	GetFinding(context.Context, *GetFindingRequest) (*Finding, error)
	// List Findings under a given ScanRun.
	ListFindings(context.Context, *ListFindingsRequest) (*ListFindingsResponse, error)
	// List all FindingTypeStats under a given ScanRun.
	ListFindingTypeStats(context.Context, *ListFindingTypeStatsRequest) (*ListFindingTypeStatsResponse, error)
}

// UnimplementedWebSecurityScannerServer can be embedded to have forward compatible implementations.
type UnimplementedWebSecurityScannerServer struct {
}

func (*UnimplementedWebSecurityScannerServer) CreateScanConfig(context.Context, *CreateScanConfigRequest) (*ScanConfig, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateScanConfig not implemented")
}
func (*UnimplementedWebSecurityScannerServer) DeleteScanConfig(context.Context, *DeleteScanConfigRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteScanConfig not implemented")
}
func (*UnimplementedWebSecurityScannerServer) GetScanConfig(context.Context, *GetScanConfigRequest) (*ScanConfig, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetScanConfig not implemented")
}
func (*UnimplementedWebSecurityScannerServer) ListScanConfigs(context.Context, *ListScanConfigsRequest) (*ListScanConfigsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListScanConfigs not implemented")
}
func (*UnimplementedWebSecurityScannerServer) UpdateScanConfig(context.Context, *UpdateScanConfigRequest) (*ScanConfig, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateScanConfig not implemented")
}
func (*UnimplementedWebSecurityScannerServer) StartScanRun(context.Context, *StartScanRunRequest) (*ScanRun, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartScanRun not implemented")
}
func (*UnimplementedWebSecurityScannerServer) GetScanRun(context.Context, *GetScanRunRequest) (*ScanRun, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetScanRun not implemented")
}
func (*UnimplementedWebSecurityScannerServer) ListScanRuns(context.Context, *ListScanRunsRequest) (*ListScanRunsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListScanRuns not implemented")
}
func (*UnimplementedWebSecurityScannerServer) StopScanRun(context.Context, *StopScanRunRequest) (*ScanRun, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopScanRun not implemented")
}
func (*UnimplementedWebSecurityScannerServer) ListCrawledUrls(context.Context, *ListCrawledUrlsRequest) (*ListCrawledUrlsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCrawledUrls not implemented")
}
func (*UnimplementedWebSecurityScannerServer) GetFinding(context.Context, *GetFindingRequest) (*Finding, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFinding not implemented")
}
func (*UnimplementedWebSecurityScannerServer) ListFindings(context.Context, *ListFindingsRequest) (*ListFindingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListFindings not implemented")
}
func (*UnimplementedWebSecurityScannerServer) ListFindingTypeStats(context.Context, *ListFindingTypeStatsRequest) (*ListFindingTypeStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListFindingTypeStats not implemented")
}

func RegisterWebSecurityScannerServer(s *grpc.Server, srv WebSecurityScannerServer) {
	s.RegisterService(&_WebSecurityScanner_serviceDesc, srv)
}

func _WebSecurityScanner_CreateScanConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateScanConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebSecurityScannerServer).CreateScanConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/CreateScanConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebSecurityScannerServer).CreateScanConfig(ctx, req.(*CreateScanConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebSecurityScanner_DeleteScanConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteScanConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebSecurityScannerServer).DeleteScanConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/DeleteScanConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebSecurityScannerServer).DeleteScanConfig(ctx, req.(*DeleteScanConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebSecurityScanner_GetScanConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScanConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebSecurityScannerServer).GetScanConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/GetScanConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebSecurityScannerServer).GetScanConfig(ctx, req.(*GetScanConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebSecurityScanner_ListScanConfigs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListScanConfigsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebSecurityScannerServer).ListScanConfigs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/ListScanConfigs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebSecurityScannerServer).ListScanConfigs(ctx, req.(*ListScanConfigsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebSecurityScanner_UpdateScanConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateScanConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebSecurityScannerServer).UpdateScanConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/UpdateScanConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebSecurityScannerServer).UpdateScanConfig(ctx, req.(*UpdateScanConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebSecurityScanner_StartScanRun_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartScanRunRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebSecurityScannerServer).StartScanRun(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/StartScanRun",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebSecurityScannerServer).StartScanRun(ctx, req.(*StartScanRunRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebSecurityScanner_GetScanRun_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetScanRunRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebSecurityScannerServer).GetScanRun(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/GetScanRun",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebSecurityScannerServer).GetScanRun(ctx, req.(*GetScanRunRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebSecurityScanner_ListScanRuns_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListScanRunsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebSecurityScannerServer).ListScanRuns(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/ListScanRuns",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebSecurityScannerServer).ListScanRuns(ctx, req.(*ListScanRunsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebSecurityScanner_StopScanRun_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopScanRunRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebSecurityScannerServer).StopScanRun(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/StopScanRun",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebSecurityScannerServer).StopScanRun(ctx, req.(*StopScanRunRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebSecurityScanner_ListCrawledUrls_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCrawledUrlsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebSecurityScannerServer).ListCrawledUrls(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/ListCrawledUrls",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebSecurityScannerServer).ListCrawledUrls(ctx, req.(*ListCrawledUrlsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebSecurityScanner_GetFinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFindingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebSecurityScannerServer).GetFinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/GetFinding",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebSecurityScannerServer).GetFinding(ctx, req.(*GetFindingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebSecurityScanner_ListFindings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListFindingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebSecurityScannerServer).ListFindings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/ListFindings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebSecurityScannerServer).ListFindings(ctx, req.(*ListFindingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebSecurityScanner_ListFindingTypeStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListFindingTypeStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebSecurityScannerServer).ListFindingTypeStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.cloud.websecurityscanner.v1beta.WebSecurityScanner/ListFindingTypeStats",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebSecurityScannerServer).ListFindingTypeStats(ctx, req.(*ListFindingTypeStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _WebSecurityScanner_serviceDesc = grpc.ServiceDesc{
	ServiceName: "google.cloud.websecurityscanner.v1beta.WebSecurityScanner",
	HandlerType: (*WebSecurityScannerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateScanConfig",
			Handler:    _WebSecurityScanner_CreateScanConfig_Handler,
		},
		{
			MethodName: "DeleteScanConfig",
			Handler:    _WebSecurityScanner_DeleteScanConfig_Handler,
		},
		{
			MethodName: "GetScanConfig",
			Handler:    _WebSecurityScanner_GetScanConfig_Handler,
		},
		{
			MethodName: "ListScanConfigs",
			Handler:    _WebSecurityScanner_ListScanConfigs_Handler,
		},
		{
			MethodName: "UpdateScanConfig",
			Handler:    _WebSecurityScanner_UpdateScanConfig_Handler,
		},
		{
			MethodName: "StartScanRun",
			Handler:    _WebSecurityScanner_StartScanRun_Handler,
		},
		{
			MethodName: "GetScanRun",
			Handler:    _WebSecurityScanner_GetScanRun_Handler,
		},
		{
			MethodName: "ListScanRuns",
			Handler:    _WebSecurityScanner_ListScanRuns_Handler,
		},
		{
			MethodName: "StopScanRun",
			Handler:    _WebSecurityScanner_StopScanRun_Handler,
		},
		{
			MethodName: "ListCrawledUrls",
			Handler:    _WebSecurityScanner_ListCrawledUrls_Handler,
		},
		{
			MethodName: "GetFinding",
			Handler:    _WebSecurityScanner_GetFinding_Handler,
		},
		{
			MethodName: "ListFindings",
			Handler:    _WebSecurityScanner_ListFindings_Handler,
		},
		{
			MethodName: "ListFindingTypeStats",
			Handler:    _WebSecurityScanner_ListFindingTypeStats_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "google/cloud/websecurityscanner/v1beta/web_security_scanner.proto",
}
