// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.12.2
// source: google/cloud/workflows/type/engine_call.proto

package _type

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The state of a function call.
type EngineCallLog_State int32

const (
	// Function call state is unspecified or unknown.
	EngineCallLog_STATE_UNSPECIFIED EngineCallLog_State = 0
	// Function call is starting.
	EngineCallLog_BEGUN EngineCallLog_State = 1
	// Function call has completed successfully.
	EngineCallLog_SUCCEEDED EngineCallLog_State = 2
	// Function call did not succeed because an exception was raised.
	EngineCallLog_EXCEPTION_RAISED EngineCallLog_State = 3
	// Function call handled an exception and is continuing.
	EngineCallLog_EXCEPTION_HANDLED EngineCallLog_State = 4
)

// Enum value maps for EngineCallLog_State.
var (
	EngineCallLog_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "BEGUN",
		2: "SUCCEEDED",
		3: "EXCEPTION_RAISED",
		4: "EXCEPTION_HANDLED",
	}
	EngineCallLog_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"BEGUN":             1,
		"SUCCEEDED":         2,
		"EXCEPTION_RAISED":  3,
		"EXCEPTION_HANDLED": 4,
	}
)

func (x EngineCallLog_State) Enum() *EngineCallLog_State {
	p := new(EngineCallLog_State)
	*p = x
	return p
}

func (x EngineCallLog_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EngineCallLog_State) Descriptor() protoreflect.EnumDescriptor {
	return file_google_cloud_workflows_type_engine_call_proto_enumTypes[0].Descriptor()
}

func (EngineCallLog_State) Type() protoreflect.EnumType {
	return &file_google_cloud_workflows_type_engine_call_proto_enumTypes[0]
}

func (x EngineCallLog_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EngineCallLog_State.Descriptor instead.
func (EngineCallLog_State) EnumDescriptor() ([]byte, []int) {
	return file_google_cloud_workflows_type_engine_call_proto_rawDescGZIP(), []int{0, 0}
}

// Logged during a workflow execution if the customer has requested call
// logging.
type EngineCallLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The execution ID of the execution where the call occurred.
	ExecutionId string `protobuf:"bytes,1,opt,name=execution_id,json=executionId,proto3" json:"execution_id,omitempty"`
	// The point in time when the activity occurred.
	ActivityTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=activity_time,json=activityTime,proto3" json:"activity_time,omitempty"`
	// The state of the function execution.
	State EngineCallLog_State `protobuf:"varint,3,opt,name=state,proto3,enum=google.cloud.workflows.type.EngineCallLog_State" json:"state,omitempty"`
	// The name of the step in which the call took place, truncated if necessary.
	Step string `protobuf:"bytes,4,opt,name=step,proto3" json:"step,omitempty"`
	// The name of the target of the call, truncated if necessary.
	Callee string `protobuf:"bytes,5,opt,name=callee,proto3" json:"callee,omitempty"`
	// Types that are assignable to Details:
	//	*EngineCallLog_Begun_
	//	*EngineCallLog_Succeeded_
	//	*EngineCallLog_ExceptionRaised_
	//	*EngineCallLog_ExceptionHandled_
	Details isEngineCallLog_Details `protobuf_oneof:"details"`
}

func (x *EngineCallLog) Reset() {
	*x = EngineCallLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_workflows_type_engine_call_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EngineCallLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EngineCallLog) ProtoMessage() {}

func (x *EngineCallLog) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_workflows_type_engine_call_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EngineCallLog.ProtoReflect.Descriptor instead.
func (*EngineCallLog) Descriptor() ([]byte, []int) {
	return file_google_cloud_workflows_type_engine_call_proto_rawDescGZIP(), []int{0}
}

func (x *EngineCallLog) GetExecutionId() string {
	if x != nil {
		return x.ExecutionId
	}
	return ""
}

func (x *EngineCallLog) GetActivityTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ActivityTime
	}
	return nil
}

func (x *EngineCallLog) GetState() EngineCallLog_State {
	if x != nil {
		return x.State
	}
	return EngineCallLog_STATE_UNSPECIFIED
}

func (x *EngineCallLog) GetStep() string {
	if x != nil {
		return x.Step
	}
	return ""
}

func (x *EngineCallLog) GetCallee() string {
	if x != nil {
		return x.Callee
	}
	return ""
}

func (m *EngineCallLog) GetDetails() isEngineCallLog_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *EngineCallLog) GetBegun() *EngineCallLog_Begun {
	if x, ok := x.GetDetails().(*EngineCallLog_Begun_); ok {
		return x.Begun
	}
	return nil
}

func (x *EngineCallLog) GetSucceeded() *EngineCallLog_Succeeded {
	if x, ok := x.GetDetails().(*EngineCallLog_Succeeded_); ok {
		return x.Succeeded
	}
	return nil
}

func (x *EngineCallLog) GetExceptionRaised() *EngineCallLog_ExceptionRaised {
	if x, ok := x.GetDetails().(*EngineCallLog_ExceptionRaised_); ok {
		return x.ExceptionRaised
	}
	return nil
}

func (x *EngineCallLog) GetExceptionHandled() *EngineCallLog_ExceptionHandled {
	if x, ok := x.GetDetails().(*EngineCallLog_ExceptionHandled_); ok {
		return x.ExceptionHandled
	}
	return nil
}

type isEngineCallLog_Details interface {
	isEngineCallLog_Details()
}

type EngineCallLog_Begun_ struct {
	// Appears at the start of a call.
	Begun *EngineCallLog_Begun `protobuf:"bytes,6,opt,name=begun,proto3,oneof"`
}

type EngineCallLog_Succeeded_ struct {
	// Appears when a call returns successfully.
	Succeeded *EngineCallLog_Succeeded `protobuf:"bytes,7,opt,name=succeeded,proto3,oneof"`
}

type EngineCallLog_ExceptionRaised_ struct {
	// Appears when a call returns because an exception was raised.
	ExceptionRaised *EngineCallLog_ExceptionRaised `protobuf:"bytes,8,opt,name=exception_raised,json=exceptionRaised,proto3,oneof"`
}

type EngineCallLog_ExceptionHandled_ struct {
	// Appears when an exception is handled and normal execution resumes.
	ExceptionHandled *EngineCallLog_ExceptionHandled `protobuf:"bytes,9,opt,name=exception_handled,json=exceptionHandled,proto3,oneof"`
}

func (*EngineCallLog_Begun_) isEngineCallLog_Details() {}

func (*EngineCallLog_Succeeded_) isEngineCallLog_Details() {}

func (*EngineCallLog_ExceptionRaised_) isEngineCallLog_Details() {}

func (*EngineCallLog_ExceptionHandled_) isEngineCallLog_Details() {}

// Information about an argument to a called function.
type EngineCallLog_CallArg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A function argument, serialized to a string. This may be truncated for
	// size reasons.
	Argument string `protobuf:"bytes,1,opt,name=argument,proto3" json:"argument,omitempty"`
}

func (x *EngineCallLog_CallArg) Reset() {
	*x = EngineCallLog_CallArg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_workflows_type_engine_call_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EngineCallLog_CallArg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EngineCallLog_CallArg) ProtoMessage() {}

func (x *EngineCallLog_CallArg) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_workflows_type_engine_call_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EngineCallLog_CallArg.ProtoReflect.Descriptor instead.
func (*EngineCallLog_CallArg) Descriptor() ([]byte, []int) {
	return file_google_cloud_workflows_type_engine_call_proto_rawDescGZIP(), []int{0, 0}
}

func (x *EngineCallLog_CallArg) GetArgument() string {
	if x != nil {
		return x.Argument
	}
	return ""
}

// Information about the start of a call.
type EngineCallLog_Begun struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The arguments passed to the function. Only one of 'args' and 'named_args'
	// will be populated.
	Args []*EngineCallLog_CallArg `protobuf:"bytes,1,rep,name=args,proto3" json:"args,omitempty"`
	// The arguments passed to the function, as a map with the argument names as
	// the keys. The values may be JSON values or they may be the serialized
	// string forms of the arguments truncated for size reasons. Only one of
	// 'args' and 'named_args' will be populated.
	NamedArgs map[string]*structpb.Value `protobuf:"bytes,2,rep,name=named_args,json=namedArgs,proto3" json:"named_args,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *EngineCallLog_Begun) Reset() {
	*x = EngineCallLog_Begun{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_workflows_type_engine_call_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EngineCallLog_Begun) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EngineCallLog_Begun) ProtoMessage() {}

func (x *EngineCallLog_Begun) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_workflows_type_engine_call_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EngineCallLog_Begun.ProtoReflect.Descriptor instead.
func (*EngineCallLog_Begun) Descriptor() ([]byte, []int) {
	return file_google_cloud_workflows_type_engine_call_proto_rawDescGZIP(), []int{0, 1}
}

func (x *EngineCallLog_Begun) GetArgs() []*EngineCallLog_CallArg {
	if x != nil {
		return x.Args
	}
	return nil
}

func (x *EngineCallLog_Begun) GetNamedArgs() map[string]*structpb.Value {
	if x != nil {
		return x.NamedArgs
	}
	return nil
}

// Information about the end of a successful call.
type EngineCallLog_Succeeded struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The time when the call started.
	CallStartTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=call_start_time,json=callStartTime,proto3" json:"call_start_time,omitempty"`
	// The result of the call, if the call succeeded, serialized to a string.
	// This may be truncated for size reasons.
	Response string `protobuf:"bytes,2,opt,name=response,proto3" json:"response,omitempty"`
}

func (x *EngineCallLog_Succeeded) Reset() {
	*x = EngineCallLog_Succeeded{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_workflows_type_engine_call_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EngineCallLog_Succeeded) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EngineCallLog_Succeeded) ProtoMessage() {}

func (x *EngineCallLog_Succeeded) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_workflows_type_engine_call_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EngineCallLog_Succeeded.ProtoReflect.Descriptor instead.
func (*EngineCallLog_Succeeded) Descriptor() ([]byte, []int) {
	return file_google_cloud_workflows_type_engine_call_proto_rawDescGZIP(), []int{0, 2}
}

func (x *EngineCallLog_Succeeded) GetCallStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CallStartTime
	}
	return nil
}

func (x *EngineCallLog_Succeeded) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

// Information about the end of a failed call.
type EngineCallLog_ExceptionRaised struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The time when the call started.
	CallStartTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=call_start_time,json=callStartTime,proto3" json:"call_start_time,omitempty"`
	// The exception message which terminated the call, truncated if necessary.
	Exception string `protobuf:"bytes,2,opt,name=exception,proto3" json:"exception,omitempty"`
	// The name of the step where the failure originates, if known. Truncated
	// if necessary.
	Origin string `protobuf:"bytes,3,opt,name=origin,proto3" json:"origin,omitempty"`
}

func (x *EngineCallLog_ExceptionRaised) Reset() {
	*x = EngineCallLog_ExceptionRaised{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_workflows_type_engine_call_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EngineCallLog_ExceptionRaised) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EngineCallLog_ExceptionRaised) ProtoMessage() {}

func (x *EngineCallLog_ExceptionRaised) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_workflows_type_engine_call_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EngineCallLog_ExceptionRaised.ProtoReflect.Descriptor instead.
func (*EngineCallLog_ExceptionRaised) Descriptor() ([]byte, []int) {
	return file_google_cloud_workflows_type_engine_call_proto_rawDescGZIP(), []int{0, 3}
}

func (x *EngineCallLog_ExceptionRaised) GetCallStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CallStartTime
	}
	return nil
}

func (x *EngineCallLog_ExceptionRaised) GetException() string {
	if x != nil {
		return x.Exception
	}
	return ""
}

func (x *EngineCallLog_ExceptionRaised) GetOrigin() string {
	if x != nil {
		return x.Origin
	}
	return ""
}

// Information about an exception which was handled.
type EngineCallLog_ExceptionHandled struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The time when the call started.
	CallStartTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=call_start_time,json=callStartTime,proto3" json:"call_start_time,omitempty"`
	// The exception message which was handled, truncated if necessary.
	Exception string `protobuf:"bytes,2,opt,name=exception,proto3" json:"exception,omitempty"`
	// The name of the step where the failure originates, if known. Truncated
	// if necessary.
	Origin string `protobuf:"bytes,3,opt,name=origin,proto3" json:"origin,omitempty"`
}

func (x *EngineCallLog_ExceptionHandled) Reset() {
	*x = EngineCallLog_ExceptionHandled{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_workflows_type_engine_call_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EngineCallLog_ExceptionHandled) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EngineCallLog_ExceptionHandled) ProtoMessage() {}

func (x *EngineCallLog_ExceptionHandled) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_workflows_type_engine_call_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EngineCallLog_ExceptionHandled.ProtoReflect.Descriptor instead.
func (*EngineCallLog_ExceptionHandled) Descriptor() ([]byte, []int) {
	return file_google_cloud_workflows_type_engine_call_proto_rawDescGZIP(), []int{0, 4}
}

func (x *EngineCallLog_ExceptionHandled) GetCallStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CallStartTime
	}
	return nil
}

func (x *EngineCallLog_ExceptionHandled) GetException() string {
	if x != nil {
		return x.Exception
	}
	return ""
}

func (x *EngineCallLog_ExceptionHandled) GetOrigin() string {
	if x != nil {
		return x.Origin
	}
	return ""
}

var File_google_cloud_workflows_type_engine_call_proto protoreflect.FileDescriptor

var file_google_cloud_workflows_type_engine_call_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x87, 0x0b, 0x0a, 0x0d,
	0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x4c, 0x6f, 0x67, 0x12, 0x21, 0x0a,
	0x0c, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x3f, 0x0a, 0x0d, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x46, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x30, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x4c, 0x6f, 0x67, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x74, 0x65,
	0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x74, 0x65, 0x70, 0x12, 0x16, 0x0a,
	0x06, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x61, 0x6c, 0x6c, 0x65, 0x65, 0x12, 0x48, 0x0a, 0x05, 0x62, 0x65, 0x67, 0x75, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x4c, 0x6f, 0x67,
	0x2e, 0x42, 0x65, 0x67, 0x75, 0x6e, 0x48, 0x00, 0x52, 0x05, 0x62, 0x65, 0x67, 0x75, 0x6e, 0x12,
	0x54, 0x0a, 0x09, 0x73, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x34, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x4c, 0x6f, 0x67, 0x2e, 0x53,
	0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x48, 0x00, 0x52, 0x09, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x65, 0x64, 0x65, 0x64, 0x12, 0x67, 0x0a, 0x10, 0x65, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x69, 0x73, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x3a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x4c, 0x6f, 0x67, 0x2e, 0x45, 0x78, 0x63, 0x65,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x69, 0x73, 0x65, 0x64, 0x48, 0x00, 0x52, 0x0f, 0x65,
	0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x69, 0x73, 0x65, 0x64, 0x12, 0x6a,
	0x0a, 0x11, 0x65, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x43, 0x61,
	0x6c, 0x6c, 0x4c, 0x6f, 0x67, 0x2e, 0x45, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x64, 0x48, 0x00, 0x52, 0x10, 0x65, 0x78, 0x63, 0x65, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x64, 0x1a, 0x25, 0x0a, 0x07, 0x43, 0x61,
	0x6c, 0x6c, 0x41, 0x72, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x72, 0x67, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x72, 0x67, 0x75, 0x6d, 0x65, 0x6e,
	0x74, 0x1a, 0x85, 0x02, 0x0a, 0x05, 0x42, 0x65, 0x67, 0x75, 0x6e, 0x12, 0x46, 0x0a, 0x04, 0x61,
	0x72, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x43, 0x61,
	0x6c, 0x6c, 0x4c, 0x6f, 0x67, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x41, 0x72, 0x67, 0x52, 0x04, 0x61,
	0x72, 0x67, 0x73, 0x12, 0x5e, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x64, 0x5f, 0x61, 0x72, 0x67,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x43, 0x61, 0x6c, 0x6c,
	0x4c, 0x6f, 0x67, 0x2e, 0x42, 0x65, 0x67, 0x75, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41,
	0x72, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x64, 0x41,
	0x72, 0x67, 0x73, 0x1a, 0x54, 0x0a, 0x0e, 0x4e, 0x61, 0x6d, 0x65, 0x64, 0x41, 0x72, 0x67, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x6b, 0x0a, 0x09, 0x53, 0x75, 0x63,
	0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x12, 0x42, 0x0a, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x63, 0x61, 0x6c,
	0x6c, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x1a, 0x8b, 0x01, 0x0a, 0x0f, 0x45, 0x78, 0x63, 0x65, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x69, 0x73, 0x65, 0x64, 0x12, 0x42, 0x0a, 0x0f, 0x63, 0x61,
	0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x65, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x65, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x1a, 0x8c, 0x01, 0x0a, 0x10, 0x45, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x64, 0x12, 0x42, 0x0a, 0x0f, 0x63, 0x61, 0x6c,
	0x6c, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d,
	0x63, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x65, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x65, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x69,
	0x67, 0x69, 0x6e, 0x22, 0x65, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x15, 0x0a, 0x11,
	0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x45, 0x47, 0x55, 0x4e, 0x10, 0x01, 0x12, 0x0d,
	0x0a, 0x09, 0x53, 0x55, 0x43, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x02, 0x12, 0x14, 0x0a,
	0x10, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x41, 0x49, 0x53, 0x45,
	0x44, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x48, 0x41, 0x4e, 0x44, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x42, 0x09, 0x0a, 0x07, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x70, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x73, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0f, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x43, 0x61, 0x6c, 0x6c, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x3a, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67,
	0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x73, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_cloud_workflows_type_engine_call_proto_rawDescOnce sync.Once
	file_google_cloud_workflows_type_engine_call_proto_rawDescData = file_google_cloud_workflows_type_engine_call_proto_rawDesc
)

func file_google_cloud_workflows_type_engine_call_proto_rawDescGZIP() []byte {
	file_google_cloud_workflows_type_engine_call_proto_rawDescOnce.Do(func() {
		file_google_cloud_workflows_type_engine_call_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_cloud_workflows_type_engine_call_proto_rawDescData)
	})
	return file_google_cloud_workflows_type_engine_call_proto_rawDescData
}

var file_google_cloud_workflows_type_engine_call_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_google_cloud_workflows_type_engine_call_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_google_cloud_workflows_type_engine_call_proto_goTypes = []interface{}{
	(EngineCallLog_State)(0),               // 0: google.cloud.workflows.type.EngineCallLog.State
	(*EngineCallLog)(nil),                  // 1: google.cloud.workflows.type.EngineCallLog
	(*EngineCallLog_CallArg)(nil),          // 2: google.cloud.workflows.type.EngineCallLog.CallArg
	(*EngineCallLog_Begun)(nil),            // 3: google.cloud.workflows.type.EngineCallLog.Begun
	(*EngineCallLog_Succeeded)(nil),        // 4: google.cloud.workflows.type.EngineCallLog.Succeeded
	(*EngineCallLog_ExceptionRaised)(nil),  // 5: google.cloud.workflows.type.EngineCallLog.ExceptionRaised
	(*EngineCallLog_ExceptionHandled)(nil), // 6: google.cloud.workflows.type.EngineCallLog.ExceptionHandled
	nil,                                    // 7: google.cloud.workflows.type.EngineCallLog.Begun.NamedArgsEntry
	(*timestamppb.Timestamp)(nil),          // 8: google.protobuf.Timestamp
	(*structpb.Value)(nil),                 // 9: google.protobuf.Value
}
var file_google_cloud_workflows_type_engine_call_proto_depIdxs = []int32{
	8,  // 0: google.cloud.workflows.type.EngineCallLog.activity_time:type_name -> google.protobuf.Timestamp
	0,  // 1: google.cloud.workflows.type.EngineCallLog.state:type_name -> google.cloud.workflows.type.EngineCallLog.State
	3,  // 2: google.cloud.workflows.type.EngineCallLog.begun:type_name -> google.cloud.workflows.type.EngineCallLog.Begun
	4,  // 3: google.cloud.workflows.type.EngineCallLog.succeeded:type_name -> google.cloud.workflows.type.EngineCallLog.Succeeded
	5,  // 4: google.cloud.workflows.type.EngineCallLog.exception_raised:type_name -> google.cloud.workflows.type.EngineCallLog.ExceptionRaised
	6,  // 5: google.cloud.workflows.type.EngineCallLog.exception_handled:type_name -> google.cloud.workflows.type.EngineCallLog.ExceptionHandled
	2,  // 6: google.cloud.workflows.type.EngineCallLog.Begun.args:type_name -> google.cloud.workflows.type.EngineCallLog.CallArg
	7,  // 7: google.cloud.workflows.type.EngineCallLog.Begun.named_args:type_name -> google.cloud.workflows.type.EngineCallLog.Begun.NamedArgsEntry
	8,  // 8: google.cloud.workflows.type.EngineCallLog.Succeeded.call_start_time:type_name -> google.protobuf.Timestamp
	8,  // 9: google.cloud.workflows.type.EngineCallLog.ExceptionRaised.call_start_time:type_name -> google.protobuf.Timestamp
	8,  // 10: google.cloud.workflows.type.EngineCallLog.ExceptionHandled.call_start_time:type_name -> google.protobuf.Timestamp
	9,  // 11: google.cloud.workflows.type.EngineCallLog.Begun.NamedArgsEntry.value:type_name -> google.protobuf.Value
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_google_cloud_workflows_type_engine_call_proto_init() }
func file_google_cloud_workflows_type_engine_call_proto_init() {
	if File_google_cloud_workflows_type_engine_call_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_cloud_workflows_type_engine_call_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EngineCallLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_workflows_type_engine_call_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EngineCallLog_CallArg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_workflows_type_engine_call_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EngineCallLog_Begun); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_workflows_type_engine_call_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EngineCallLog_Succeeded); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_workflows_type_engine_call_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EngineCallLog_ExceptionRaised); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_workflows_type_engine_call_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EngineCallLog_ExceptionHandled); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_cloud_workflows_type_engine_call_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*EngineCallLog_Begun_)(nil),
		(*EngineCallLog_Succeeded_)(nil),
		(*EngineCallLog_ExceptionRaised_)(nil),
		(*EngineCallLog_ExceptionHandled_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_cloud_workflows_type_engine_call_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_cloud_workflows_type_engine_call_proto_goTypes,
		DependencyIndexes: file_google_cloud_workflows_type_engine_call_proto_depIdxs,
		EnumInfos:         file_google_cloud_workflows_type_engine_call_proto_enumTypes,
		MessageInfos:      file_google_cloud_workflows_type_engine_call_proto_msgTypes,
	}.Build()
	File_google_cloud_workflows_type_engine_call_proto = out.File
	file_google_cloud_workflows_type_engine_call_proto_rawDesc = nil
	file_google_cloud_workflows_type_engine_call_proto_goTypes = nil
	file_google_cloud_workflows_type_engine_call_proto_depIdxs = nil
}
