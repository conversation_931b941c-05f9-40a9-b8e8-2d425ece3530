// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.21.9
// source: google/maps/fleetengine/v1/fleetengine.proto

package fleetengine

import (
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The type of a trip.
type TripType int32

const (
	// Default, used for unspecified or unrecognized trip types.
	TripType_UNKNOWN_TRIP_TYPE TripType = 0
	// The trip may share a vehicle with other trips.
	TripType_SHARED TripType = 1
	// The trip is exclusive to a vehicle.
	TripType_EXCLUSIVE TripType = 2
)

// Enum value maps for TripType.
var (
	TripType_name = map[int32]string{
		0: "UNKNOWN_TRIP_TYPE",
		1: "SHARED",
		2: "EXCLUSIVE",
	}
	TripType_value = map[string]int32{
		"UNKNOWN_TRIP_TYPE": 0,
		"SHARED":            1,
		"EXCLUSIVE":         2,
	}
)

func (x TripType) Enum() *TripType {
	p := new(TripType)
	*p = x
	return p
}

func (x TripType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TripType) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_v1_fleetengine_proto_enumTypes[0].Descriptor()
}

func (TripType) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_v1_fleetengine_proto_enumTypes[0]
}

func (x TripType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TripType.Descriptor instead.
func (TripType) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_fleetengine_proto_rawDescGZIP(), []int{0}
}

// The type of waypoint.
type WaypointType int32

const (
	// Unknown or unspecified waypoint type.
	WaypointType_UNKNOWN_WAYPOINT_TYPE WaypointType = 0
	// Waypoints for picking up riders or items.
	WaypointType_PICKUP_WAYPOINT_TYPE WaypointType = 1
	// Waypoints for dropping off riders or items.
	WaypointType_DROP_OFF_WAYPOINT_TYPE WaypointType = 2
	// Waypoints for intermediate destinations in a multi-destination trip.
	WaypointType_INTERMEDIATE_DESTINATION_WAYPOINT_TYPE WaypointType = 3
)

// Enum value maps for WaypointType.
var (
	WaypointType_name = map[int32]string{
		0: "UNKNOWN_WAYPOINT_TYPE",
		1: "PICKUP_WAYPOINT_TYPE",
		2: "DROP_OFF_WAYPOINT_TYPE",
		3: "INTERMEDIATE_DESTINATION_WAYPOINT_TYPE",
	}
	WaypointType_value = map[string]int32{
		"UNKNOWN_WAYPOINT_TYPE":                  0,
		"PICKUP_WAYPOINT_TYPE":                   1,
		"DROP_OFF_WAYPOINT_TYPE":                 2,
		"INTERMEDIATE_DESTINATION_WAYPOINT_TYPE": 3,
	}
)

func (x WaypointType) Enum() *WaypointType {
	p := new(WaypointType)
	*p = x
	return p
}

func (x WaypointType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WaypointType) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_v1_fleetengine_proto_enumTypes[1].Descriptor()
}

func (WaypointType) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_v1_fleetengine_proto_enumTypes[1]
}

func (x WaypointType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WaypointType.Descriptor instead.
func (WaypointType) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_fleetengine_proto_rawDescGZIP(), []int{1}
}

// The type of polyline format.
type PolylineFormatType int32

const (
	// The format is unspecified or unknown.
	PolylineFormatType_UNKNOWN_FORMAT_TYPE PolylineFormatType = 0
	// A list of `google.type.LatLng`.
	PolylineFormatType_LAT_LNG_LIST_TYPE PolylineFormatType = 1
	// A polyline encoded with a polyline compression algorithm. Decoding is not
	// yet supported.
	PolylineFormatType_ENCODED_POLYLINE_TYPE PolylineFormatType = 2
)

// Enum value maps for PolylineFormatType.
var (
	PolylineFormatType_name = map[int32]string{
		0: "UNKNOWN_FORMAT_TYPE",
		1: "LAT_LNG_LIST_TYPE",
		2: "ENCODED_POLYLINE_TYPE",
	}
	PolylineFormatType_value = map[string]int32{
		"UNKNOWN_FORMAT_TYPE":   0,
		"LAT_LNG_LIST_TYPE":     1,
		"ENCODED_POLYLINE_TYPE": 2,
	}
)

func (x PolylineFormatType) Enum() *PolylineFormatType {
	p := new(PolylineFormatType)
	*p = x
	return p
}

func (x PolylineFormatType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PolylineFormatType) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_v1_fleetengine_proto_enumTypes[2].Descriptor()
}

func (PolylineFormatType) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_v1_fleetengine_proto_enumTypes[2]
}

func (x PolylineFormatType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PolylineFormatType.Descriptor instead.
func (PolylineFormatType) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_fleetengine_proto_rawDescGZIP(), []int{2}
}

// The vehicle's navigation status.
type NavigationStatus int32

const (
	// Unspecified navigation status.
	NavigationStatus_UNKNOWN_NAVIGATION_STATUS NavigationStatus = 0
	// The Driver app's navigation is in `FREE_NAV` mode.
	NavigationStatus_NO_GUIDANCE NavigationStatus = 1
	// Turn-by-turn navigation is available and the Driver app navigation has
	// entered `GUIDED_NAV` mode.
	NavigationStatus_ENROUTE_TO_DESTINATION NavigationStatus = 2
	// The vehicle has gone off the suggested route.
	NavigationStatus_OFF_ROUTE NavigationStatus = 3
	// The vehicle is within approximately 50m of the destination.
	NavigationStatus_ARRIVED_AT_DESTINATION NavigationStatus = 4
)

// Enum value maps for NavigationStatus.
var (
	NavigationStatus_name = map[int32]string{
		0: "UNKNOWN_NAVIGATION_STATUS",
		1: "NO_GUIDANCE",
		2: "ENROUTE_TO_DESTINATION",
		3: "OFF_ROUTE",
		4: "ARRIVED_AT_DESTINATION",
	}
	NavigationStatus_value = map[string]int32{
		"UNKNOWN_NAVIGATION_STATUS": 0,
		"NO_GUIDANCE":               1,
		"ENROUTE_TO_DESTINATION":    2,
		"OFF_ROUTE":                 3,
		"ARRIVED_AT_DESTINATION":    4,
	}
)

func (x NavigationStatus) Enum() *NavigationStatus {
	p := new(NavigationStatus)
	*p = x
	return p
}

func (x NavigationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NavigationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_v1_fleetengine_proto_enumTypes[3].Descriptor()
}

func (NavigationStatus) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_v1_fleetengine_proto_enumTypes[3]
}

func (x NavigationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NavigationStatus.Descriptor instead.
func (NavigationStatus) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_fleetengine_proto_rawDescGZIP(), []int{3}
}

// The sensor or methodology used to determine the location.
type LocationSensor int32

const (
	// The sensor is unspecified or unknown.
	LocationSensor_UNKNOWN_SENSOR LocationSensor = 0
	// GPS or Assisted GPS.
	LocationSensor_GPS LocationSensor = 1
	// Assisted GPS, cell tower ID, or WiFi access point.
	LocationSensor_NETWORK LocationSensor = 2
	// Cell tower ID or WiFi access point.
	LocationSensor_PASSIVE LocationSensor = 3
	// A location signal snapped to the best road position.
	LocationSensor_ROAD_SNAPPED_LOCATION_PROVIDER LocationSensor = 4
	// The fused location provider in Google Play services.
	LocationSensor_FUSED_LOCATION_PROVIDER LocationSensor = 100
	// The location provider on Apple operating systems.
	LocationSensor_CORE_LOCATION LocationSensor = 200
)

// Enum value maps for LocationSensor.
var (
	LocationSensor_name = map[int32]string{
		0:   "UNKNOWN_SENSOR",
		1:   "GPS",
		2:   "NETWORK",
		3:   "PASSIVE",
		4:   "ROAD_SNAPPED_LOCATION_PROVIDER",
		100: "FUSED_LOCATION_PROVIDER",
		200: "CORE_LOCATION",
	}
	LocationSensor_value = map[string]int32{
		"UNKNOWN_SENSOR":                 0,
		"GPS":                            1,
		"NETWORK":                        2,
		"PASSIVE":                        3,
		"ROAD_SNAPPED_LOCATION_PROVIDER": 4,
		"FUSED_LOCATION_PROVIDER":        100,
		"CORE_LOCATION":                  200,
	}
)

func (x LocationSensor) Enum() *LocationSensor {
	p := new(LocationSensor)
	*p = x
	return p
}

func (x LocationSensor) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LocationSensor) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_v1_fleetengine_proto_enumTypes[4].Descriptor()
}

func (LocationSensor) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_v1_fleetengine_proto_enumTypes[4]
}

func (x LocationSensor) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LocationSensor.Descriptor instead.
func (LocationSensor) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_fleetengine_proto_rawDescGZIP(), []int{4}
}

// Identifies a terminal point.
type TerminalPointId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated.
	//
	// Types that are assignable to Id:
	//
	//	*TerminalPointId_PlaceId
	//	*TerminalPointId_GeneratedId
	Id isTerminalPointId_Id `protobuf_oneof:"Id"`
	// Unique ID of the terminal point.
	Value string `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *TerminalPointId) Reset() {
	*x = TerminalPointId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_fleetengine_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TerminalPointId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TerminalPointId) ProtoMessage() {}

func (x *TerminalPointId) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_fleetengine_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TerminalPointId.ProtoReflect.Descriptor instead.
func (*TerminalPointId) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_fleetengine_proto_rawDescGZIP(), []int{0}
}

func (m *TerminalPointId) GetId() isTerminalPointId_Id {
	if m != nil {
		return m.Id
	}
	return nil
}

// Deprecated: Do not use.
func (x *TerminalPointId) GetPlaceId() string {
	if x, ok := x.GetId().(*TerminalPointId_PlaceId); ok {
		return x.PlaceId
	}
	return ""
}

// Deprecated: Do not use.
func (x *TerminalPointId) GetGeneratedId() string {
	if x, ok := x.GetId().(*TerminalPointId_GeneratedId); ok {
		return x.GeneratedId
	}
	return ""
}

func (x *TerminalPointId) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type isTerminalPointId_Id interface {
	isTerminalPointId_Id()
}

type TerminalPointId_PlaceId struct {
	// Deprecated.
	//
	// Deprecated: Do not use.
	PlaceId string `protobuf:"bytes,2,opt,name=place_id,json=placeId,proto3,oneof"`
}

type TerminalPointId_GeneratedId struct {
	// Deprecated.
	//
	// Deprecated: Do not use.
	GeneratedId string `protobuf:"bytes,3,opt,name=generated_id,json=generatedId,proto3,oneof"`
}

func (*TerminalPointId_PlaceId) isTerminalPointId_Id() {}

func (*TerminalPointId_GeneratedId) isTerminalPointId_Id() {}

// Describes the location of a waypoint.
type TerminalLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. Denotes the location of a trip waypoint.
	Point *latlng.LatLng `protobuf:"bytes,1,opt,name=point,proto3" json:"point,omitempty"`
	// ID of the terminal point.
	TerminalPointId *TerminalPointId `protobuf:"bytes,2,opt,name=terminal_point_id,json=terminalPointId,proto3" json:"terminal_point_id,omitempty"`
	// Deprecated.
	//
	// Deprecated: Do not use.
	AccessPointId string `protobuf:"bytes,3,opt,name=access_point_id,json=accessPointId,proto3" json:"access_point_id,omitempty"`
	// Deprecated.
	//
	// Deprecated: Do not use.
	TripId string `protobuf:"bytes,4,opt,name=trip_id,json=tripId,proto3" json:"trip_id,omitempty"`
	// Deprecated: `Vehicle.waypoint` will have this data.
	//
	// Deprecated: Do not use.
	TerminalLocationType WaypointType `protobuf:"varint,5,opt,name=terminal_location_type,json=terminalLocationType,proto3,enum=maps.fleetengine.v1.WaypointType" json:"terminal_location_type,omitempty"`
}

func (x *TerminalLocation) Reset() {
	*x = TerminalLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_fleetengine_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TerminalLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TerminalLocation) ProtoMessage() {}

func (x *TerminalLocation) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_fleetengine_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TerminalLocation.ProtoReflect.Descriptor instead.
func (*TerminalLocation) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_fleetengine_proto_rawDescGZIP(), []int{1}
}

func (x *TerminalLocation) GetPoint() *latlng.LatLng {
	if x != nil {
		return x.Point
	}
	return nil
}

func (x *TerminalLocation) GetTerminalPointId() *TerminalPointId {
	if x != nil {
		return x.TerminalPointId
	}
	return nil
}

// Deprecated: Do not use.
func (x *TerminalLocation) GetAccessPointId() string {
	if x != nil {
		return x.AccessPointId
	}
	return ""
}

// Deprecated: Do not use.
func (x *TerminalLocation) GetTripId() string {
	if x != nil {
		return x.TripId
	}
	return ""
}

// Deprecated: Do not use.
func (x *TerminalLocation) GetTerminalLocationType() WaypointType {
	if x != nil {
		return x.TerminalLocationType
	}
	return WaypointType_UNKNOWN_WAYPOINT_TYPE
}

// Describes a stopping point on a vehicle's route or an ending point on a
// vehicle's trip.
type TripWaypoint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The location of this waypoint.
	Location *TerminalLocation `protobuf:"bytes,1,opt,name=location,proto3" json:"location,omitempty"`
	// The trip associated with this waypoint.
	TripId string `protobuf:"bytes,2,opt,name=trip_id,json=tripId,proto3" json:"trip_id,omitempty"`
	// The role this waypoint plays in this trip, such as pickup or dropoff.
	WaypointType WaypointType `protobuf:"varint,3,opt,name=waypoint_type,json=waypointType,proto3,enum=maps.fleetengine.v1.WaypointType" json:"waypoint_type,omitempty"`
	// The path from the previous waypoint to the current waypoint.  Undefined for
	// the first waypoint in a list. This field is only populated when requested.
	PathToWaypoint []*latlng.LatLng `protobuf:"bytes,4,rep,name=path_to_waypoint,json=pathToWaypoint,proto3" json:"path_to_waypoint,omitempty"`
	// The encoded path from the previous waypoint to the current waypoint.
	//
	// <p>Note: This field is intended only for use by the Driver SDK and Consumer
	// SDK. Decoding is not yet supported.
	EncodedPathToWaypoint string `protobuf:"bytes,5,opt,name=encoded_path_to_waypoint,json=encodedPathToWaypoint,proto3" json:"encoded_path_to_waypoint,omitempty"`
	// The traffic conditions along the path to this waypoint.  Note that traffic
	// is only available for Google Map Platform Rides and Deliveries Solution
	// customers.
	TrafficToWaypoint *ConsumableTrafficPolyline `protobuf:"bytes,10,opt,name=traffic_to_waypoint,json=trafficToWaypoint,proto3" json:"traffic_to_waypoint,omitempty"`
	// The path distance from the previous waypoint to the current waypoint.
	// Undefined for the first waypoint in a list.
	DistanceMeters *wrapperspb.Int32Value `protobuf:"bytes,6,opt,name=distance_meters,json=distanceMeters,proto3" json:"distance_meters,omitempty"`
	// The estimated time of arrival at this waypoint. Undefined for the first
	// waypoint in a list.
	Eta *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=eta,proto3" json:"eta,omitempty"`
	// The travel time from previous waypoint to this point. Undefined for the
	// first waypoint in a list.
	Duration *durationpb.Duration `protobuf:"bytes,8,opt,name=duration,proto3" json:"duration,omitempty"`
}

func (x *TripWaypoint) Reset() {
	*x = TripWaypoint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_fleetengine_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TripWaypoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TripWaypoint) ProtoMessage() {}

func (x *TripWaypoint) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_fleetengine_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TripWaypoint.ProtoReflect.Descriptor instead.
func (*TripWaypoint) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_fleetengine_proto_rawDescGZIP(), []int{2}
}

func (x *TripWaypoint) GetLocation() *TerminalLocation {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *TripWaypoint) GetTripId() string {
	if x != nil {
		return x.TripId
	}
	return ""
}

func (x *TripWaypoint) GetWaypointType() WaypointType {
	if x != nil {
		return x.WaypointType
	}
	return WaypointType_UNKNOWN_WAYPOINT_TYPE
}

func (x *TripWaypoint) GetPathToWaypoint() []*latlng.LatLng {
	if x != nil {
		return x.PathToWaypoint
	}
	return nil
}

func (x *TripWaypoint) GetEncodedPathToWaypoint() string {
	if x != nil {
		return x.EncodedPathToWaypoint
	}
	return ""
}

func (x *TripWaypoint) GetTrafficToWaypoint() *ConsumableTrafficPolyline {
	if x != nil {
		return x.TrafficToWaypoint
	}
	return nil
}

func (x *TripWaypoint) GetDistanceMeters() *wrapperspb.Int32Value {
	if x != nil {
		return x.DistanceMeters
	}
	return nil
}

func (x *TripWaypoint) GetEta() *timestamppb.Timestamp {
	if x != nil {
		return x.Eta
	}
	return nil
}

func (x *TripWaypoint) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

// Describes a vehicle attribute as a key-value pair. The "key:value" string
// length cannot exceed 256 characters.
type VehicleAttribute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The attribute's key. Keys may not contain the colon character (:).
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// The attribute's value.
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *VehicleAttribute) Reset() {
	*x = VehicleAttribute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_fleetengine_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VehicleAttribute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VehicleAttribute) ProtoMessage() {}

func (x *VehicleAttribute) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_fleetengine_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VehicleAttribute.ProtoReflect.Descriptor instead.
func (*VehicleAttribute) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_fleetengine_proto_rawDescGZIP(), []int{3}
}

func (x *VehicleAttribute) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *VehicleAttribute) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// The location, speed, and heading of a vehicle at a point in time.
type VehicleLocation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The location of the vehicle.
	// When it is sent to Fleet Engine, the vehicle's location is a GPS location.
	// When you receive it in a response, the vehicle's location can be either a
	// GPS location, a supplemental location, or some other estimated location.
	// The source is specified in `location_sensor`.
	Location *latlng.LatLng `protobuf:"bytes,1,opt,name=location,proto3" json:"location,omitempty"`
	// Deprecated: Use `latlng_accuracy` instead.
	//
	// Deprecated: Do not use.
	HorizontalAccuracy *wrapperspb.DoubleValue `protobuf:"bytes,8,opt,name=horizontal_accuracy,json=horizontalAccuracy,proto3" json:"horizontal_accuracy,omitempty"`
	// Accuracy of `location` in meters as a radius.
	LatlngAccuracy *wrapperspb.DoubleValue `protobuf:"bytes,22,opt,name=latlng_accuracy,json=latlngAccuracy,proto3" json:"latlng_accuracy,omitempty"`
	// Direction the vehicle is moving in degrees.  0 represents North.
	// The valid range is [0,360).
	Heading *wrapperspb.Int32Value `protobuf:"bytes,2,opt,name=heading,proto3" json:"heading,omitempty"`
	// Deprecated: Use `heading_accuracy` instead.
	//
	// Deprecated: Do not use.
	BearingAccuracy *wrapperspb.DoubleValue `protobuf:"bytes,10,opt,name=bearing_accuracy,json=bearingAccuracy,proto3" json:"bearing_accuracy,omitempty"`
	// Accuracy of `heading` in degrees.
	HeadingAccuracy *wrapperspb.DoubleValue `protobuf:"bytes,23,opt,name=heading_accuracy,json=headingAccuracy,proto3" json:"heading_accuracy,omitempty"`
	// Altitude in meters above WGS84.
	Altitude *wrapperspb.DoubleValue `protobuf:"bytes,5,opt,name=altitude,proto3" json:"altitude,omitempty"`
	// Deprecated: Use `altitude_accuracy` instead.
	//
	// Deprecated: Do not use.
	VerticalAccuracy *wrapperspb.DoubleValue `protobuf:"bytes,9,opt,name=vertical_accuracy,json=verticalAccuracy,proto3" json:"vertical_accuracy,omitempty"`
	// Accuracy of `altitude` in meters.
	AltitudeAccuracy *wrapperspb.DoubleValue `protobuf:"bytes,24,opt,name=altitude_accuracy,json=altitudeAccuracy,proto3" json:"altitude_accuracy,omitempty"`
	// Speed of the vehicle in kilometers per hour.
	// Deprecated: Use `speed` instead.
	//
	// Deprecated: Do not use.
	SpeedKmph *wrapperspb.Int32Value `protobuf:"bytes,3,opt,name=speed_kmph,json=speedKmph,proto3" json:"speed_kmph,omitempty"`
	// Speed of the vehicle in meters/second
	Speed *wrapperspb.DoubleValue `protobuf:"bytes,6,opt,name=speed,proto3" json:"speed,omitempty"`
	// Accuracy of `speed` in meters/second.
	SpeedAccuracy *wrapperspb.DoubleValue `protobuf:"bytes,7,opt,name=speed_accuracy,json=speedAccuracy,proto3" json:"speed_accuracy,omitempty"`
	// The time when `location` was reported by the sensor according to the
	// sensor's clock.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// Output only. The time when the server received the location information.
	ServerTime *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	// Provider of location data (for example, `GPS`).
	LocationSensor LocationSensor `protobuf:"varint,11,opt,name=location_sensor,json=locationSensor,proto3,enum=maps.fleetengine.v1.LocationSensor" json:"location_sensor,omitempty"`
	// Whether `location` is snapped to a road.
	IsRoadSnapped *wrapperspb.BoolValue `protobuf:"bytes,27,opt,name=is_road_snapped,json=isRoadSnapped,proto3" json:"is_road_snapped,omitempty"`
	// Input only. Indicates whether the GPS sensor is enabled on the mobile
	// device.
	IsGpsSensorEnabled *wrapperspb.BoolValue `protobuf:"bytes,12,opt,name=is_gps_sensor_enabled,json=isGpsSensorEnabled,proto3" json:"is_gps_sensor_enabled,omitempty"`
	// Input only. Time (in seconds) since this location was first sent to the
	// server. This will be zero for the first update. If the time is unknown (for
	// example, when the app restarts), this value resets to zero.
	TimeSinceUpdate *wrapperspb.Int32Value `protobuf:"bytes,14,opt,name=time_since_update,json=timeSinceUpdate,proto3" json:"time_since_update,omitempty"`
	// Input only. Number of additional attempts to send this location to the
	// server. If this value is zero, then it is not stale.
	NumStaleUpdates *wrapperspb.Int32Value `protobuf:"bytes,15,opt,name=num_stale_updates,json=numStaleUpdates,proto3" json:"num_stale_updates,omitempty"`
	// Raw vehicle location (unprocessed by road-snapper).
	RawLocation *latlng.LatLng `protobuf:"bytes,16,opt,name=raw_location,json=rawLocation,proto3" json:"raw_location,omitempty"`
	// Input only. Timestamp associated with the raw location.
	RawLocationTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=raw_location_time,json=rawLocationTime,proto3" json:"raw_location_time,omitempty"`
	// Input only. Source of the raw location.
	RawLocationSensor LocationSensor `protobuf:"varint,28,opt,name=raw_location_sensor,json=rawLocationSensor,proto3,enum=maps.fleetengine.v1.LocationSensor" json:"raw_location_sensor,omitempty"`
	// Input only. Accuracy of `raw_location` as a radius, in meters.
	RawLocationAccuracy *wrapperspb.DoubleValue `protobuf:"bytes,25,opt,name=raw_location_accuracy,json=rawLocationAccuracy,proto3" json:"raw_location_accuracy,omitempty"`
	// Input only. Supplemental location provided by the integrating app.
	SupplementalLocation *latlng.LatLng `protobuf:"bytes,18,opt,name=supplemental_location,json=supplementalLocation,proto3" json:"supplemental_location,omitempty"`
	// Input only. Timestamp associated with the supplemental location.
	SupplementalLocationTime *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=supplemental_location_time,json=supplementalLocationTime,proto3" json:"supplemental_location_time,omitempty"`
	// Input only. Source of the supplemental location.
	SupplementalLocationSensor LocationSensor `protobuf:"varint,20,opt,name=supplemental_location_sensor,json=supplementalLocationSensor,proto3,enum=maps.fleetengine.v1.LocationSensor" json:"supplemental_location_sensor,omitempty"`
	// Input only. Accuracy of `supplemental_location` as a radius, in meters.
	SupplementalLocationAccuracy *wrapperspb.DoubleValue `protobuf:"bytes,21,opt,name=supplemental_location_accuracy,json=supplementalLocationAccuracy,proto3" json:"supplemental_location_accuracy,omitempty"`
	// Deprecated: Use `is_road_snapped` instead.
	//
	// Deprecated: Do not use.
	RoadSnapped bool `protobuf:"varint,26,opt,name=road_snapped,json=roadSnapped,proto3" json:"road_snapped,omitempty"`
}

func (x *VehicleLocation) Reset() {
	*x = VehicleLocation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_fleetengine_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VehicleLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VehicleLocation) ProtoMessage() {}

func (x *VehicleLocation) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_fleetengine_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VehicleLocation.ProtoReflect.Descriptor instead.
func (*VehicleLocation) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_fleetengine_proto_rawDescGZIP(), []int{4}
}

func (x *VehicleLocation) GetLocation() *latlng.LatLng {
	if x != nil {
		return x.Location
	}
	return nil
}

// Deprecated: Do not use.
func (x *VehicleLocation) GetHorizontalAccuracy() *wrapperspb.DoubleValue {
	if x != nil {
		return x.HorizontalAccuracy
	}
	return nil
}

func (x *VehicleLocation) GetLatlngAccuracy() *wrapperspb.DoubleValue {
	if x != nil {
		return x.LatlngAccuracy
	}
	return nil
}

func (x *VehicleLocation) GetHeading() *wrapperspb.Int32Value {
	if x != nil {
		return x.Heading
	}
	return nil
}

// Deprecated: Do not use.
func (x *VehicleLocation) GetBearingAccuracy() *wrapperspb.DoubleValue {
	if x != nil {
		return x.BearingAccuracy
	}
	return nil
}

func (x *VehicleLocation) GetHeadingAccuracy() *wrapperspb.DoubleValue {
	if x != nil {
		return x.HeadingAccuracy
	}
	return nil
}

func (x *VehicleLocation) GetAltitude() *wrapperspb.DoubleValue {
	if x != nil {
		return x.Altitude
	}
	return nil
}

// Deprecated: Do not use.
func (x *VehicleLocation) GetVerticalAccuracy() *wrapperspb.DoubleValue {
	if x != nil {
		return x.VerticalAccuracy
	}
	return nil
}

func (x *VehicleLocation) GetAltitudeAccuracy() *wrapperspb.DoubleValue {
	if x != nil {
		return x.AltitudeAccuracy
	}
	return nil
}

// Deprecated: Do not use.
func (x *VehicleLocation) GetSpeedKmph() *wrapperspb.Int32Value {
	if x != nil {
		return x.SpeedKmph
	}
	return nil
}

func (x *VehicleLocation) GetSpeed() *wrapperspb.DoubleValue {
	if x != nil {
		return x.Speed
	}
	return nil
}

func (x *VehicleLocation) GetSpeedAccuracy() *wrapperspb.DoubleValue {
	if x != nil {
		return x.SpeedAccuracy
	}
	return nil
}

func (x *VehicleLocation) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *VehicleLocation) GetServerTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ServerTime
	}
	return nil
}

func (x *VehicleLocation) GetLocationSensor() LocationSensor {
	if x != nil {
		return x.LocationSensor
	}
	return LocationSensor_UNKNOWN_SENSOR
}

func (x *VehicleLocation) GetIsRoadSnapped() *wrapperspb.BoolValue {
	if x != nil {
		return x.IsRoadSnapped
	}
	return nil
}

func (x *VehicleLocation) GetIsGpsSensorEnabled() *wrapperspb.BoolValue {
	if x != nil {
		return x.IsGpsSensorEnabled
	}
	return nil
}

func (x *VehicleLocation) GetTimeSinceUpdate() *wrapperspb.Int32Value {
	if x != nil {
		return x.TimeSinceUpdate
	}
	return nil
}

func (x *VehicleLocation) GetNumStaleUpdates() *wrapperspb.Int32Value {
	if x != nil {
		return x.NumStaleUpdates
	}
	return nil
}

func (x *VehicleLocation) GetRawLocation() *latlng.LatLng {
	if x != nil {
		return x.RawLocation
	}
	return nil
}

func (x *VehicleLocation) GetRawLocationTime() *timestamppb.Timestamp {
	if x != nil {
		return x.RawLocationTime
	}
	return nil
}

func (x *VehicleLocation) GetRawLocationSensor() LocationSensor {
	if x != nil {
		return x.RawLocationSensor
	}
	return LocationSensor_UNKNOWN_SENSOR
}

func (x *VehicleLocation) GetRawLocationAccuracy() *wrapperspb.DoubleValue {
	if x != nil {
		return x.RawLocationAccuracy
	}
	return nil
}

func (x *VehicleLocation) GetSupplementalLocation() *latlng.LatLng {
	if x != nil {
		return x.SupplementalLocation
	}
	return nil
}

func (x *VehicleLocation) GetSupplementalLocationTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SupplementalLocationTime
	}
	return nil
}

func (x *VehicleLocation) GetSupplementalLocationSensor() LocationSensor {
	if x != nil {
		return x.SupplementalLocationSensor
	}
	return LocationSensor_UNKNOWN_SENSOR
}

func (x *VehicleLocation) GetSupplementalLocationAccuracy() *wrapperspb.DoubleValue {
	if x != nil {
		return x.SupplementalLocationAccuracy
	}
	return nil
}

// Deprecated: Do not use.
func (x *VehicleLocation) GetRoadSnapped() bool {
	if x != nil {
		return x.RoadSnapped
	}
	return false
}

var File_google_maps_fleetengine_v1_fleetengine_proto protoreflect.FileDescriptor

var file_google_maps_fleetengine_v1_fleetengine_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x6c, 0x65,
	0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13,
	0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70,
	0x73, 0x2f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2f, 0x76, 0x31,
	0x2f, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6c, 0x61, 0x74,
	0x6c, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x77, 0x0a, 0x0f, 0x54, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x08,
	0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02,
	0x18, 0x01, 0x48, 0x00, 0x52, 0x07, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x27, 0x0a,
	0x0c, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x0b, 0x67, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x64, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x04, 0x0a, 0x02,
	0x49, 0x64, 0x22, 0xba, 0x02, 0x0a, 0x10, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x05, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x05, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x50, 0x0a, 0x11, 0x74, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x64, 0x52, 0x0f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x0f, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x07, 0x74, 0x72, 0x69, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x06, 0x74, 0x72, 0x69, 0x70,
	0x49, 0x64, 0x12, 0x5b, 0x0a, 0x16, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x14, 0x74, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22,
	0xb5, 0x04, 0x0a, 0x0c, 0x54, 0x72, 0x69, 0x70, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x12, 0x41, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x72, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x72, 0x69, 0x70, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x0d,
	0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74,
	0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x10, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x74, 0x6f, 0x5f,
	0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74,
	0x4c, 0x6e, 0x67, 0x52, 0x0e, 0x70, 0x61, 0x74, 0x68, 0x54, 0x6f, 0x57, 0x61, 0x79, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x18, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x64, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x5f, 0x74, 0x6f, 0x5f, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x64, 0x50, 0x61,
	0x74, 0x68, 0x54, 0x6f, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x5e, 0x0a, 0x13,
	0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x5f, 0x74, 0x6f, 0x5f, 0x77, 0x61, 0x79, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x61, 0x70, 0x73,
	0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69,
	0x63, 0x50, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x11, 0x74, 0x72, 0x61, 0x66, 0x66,
	0x69, 0x63, 0x54, 0x6f, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x44, 0x0a, 0x0f,
	0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x12, 0x2c, 0x0a, 0x03, 0x65, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x03, 0x65, 0x74, 0x61,
	0x12, 0x35, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x3a, 0x0a, 0x10, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x22, 0x97, 0x10, 0x0a, 0x0f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x08,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x51, 0x0a, 0x13, 0x68, 0x6f, 0x72, 0x69,
	0x7a, 0x6f, 0x6e, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x12, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x6f, 0x6e,
	0x74, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x12, 0x45, 0x0a, 0x0f, 0x6c,
	0x61, 0x74, 0x6c, 0x6e, 0x67, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x0e, 0x6c, 0x61, 0x74, 0x6c, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61,
	0x63, 0x79, 0x12, 0x35, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x4b, 0x0a, 0x10, 0x62, 0x65, 0x61,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0f, 0x62, 0x65, 0x61, 0x72, 0x69, 0x6e, 0x67, 0x41, 0x63,
	0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x12, 0x47, 0x0a, 0x10, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0f,
	0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x12,
	0x38, 0x0a, 0x08, 0x61, 0x6c, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x08, 0x61, 0x6c, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x4d, 0x0a, 0x11, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x10, 0x76, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c,
	0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x12, 0x49, 0x0a, 0x11, 0x61, 0x6c, 0x74, 0x69,
	0x74, 0x75, 0x64, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x18, 0x18, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x10, 0x61, 0x6c, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x41, 0x63, 0x63, 0x75, 0x72,
	0x61, 0x63, 0x79, 0x12, 0x3e, 0x0a, 0x0a, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x6b, 0x6d, 0x70,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x73, 0x70, 0x65, 0x65, 0x64, 0x4b,
	0x6d, 0x70, 0x68, 0x12, 0x32, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x12, 0x43, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x65, 0x64,
	0x5f, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d, 0x73,
	0x70, 0x65, 0x65, 0x64, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x12, 0x3b, 0x0a, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52,
	0x0a, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4c, 0x0a, 0x0f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65,
	0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x0e, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x42, 0x0a, 0x0f, 0x69, 0x73, 0x5f,
	0x72, 0x6f, 0x61, 0x64, 0x5f, 0x73, 0x6e, 0x61, 0x70, 0x70, 0x65, 0x64, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0d,
	0x69, 0x73, 0x52, 0x6f, 0x61, 0x64, 0x53, 0x6e, 0x61, 0x70, 0x70, 0x65, 0x64, 0x12, 0x52, 0x0a,
	0x15, 0x69, 0x73, 0x5f, 0x67, 0x70, 0x73, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x42,
	0x6f, 0x6f, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x12, 0x69,
	0x73, 0x47, 0x70, 0x73, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x12, 0x4c, 0x0a, 0x11, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x5f,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x0f,
	0x74, 0x69, 0x6d, 0x65, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12,
	0x4c, 0x0a, 0x11, 0x6e, 0x75, 0x6d, 0x5f, 0x73, 0x74, 0x61, 0x6c, 0x65, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74,
	0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x0f, 0x6e, 0x75,
	0x6d, 0x53, 0x74, 0x61, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x73, 0x12, 0x36, 0x0a,
	0x0c, 0x72, 0x61, 0x77, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x0b, 0x72, 0x61, 0x77, 0x4c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4b, 0x0a, 0x11, 0x72, 0x61, 0x77, 0x5f, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41,
	0x04, 0x52, 0x0f, 0x72, 0x61, 0x77, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x58, 0x0a, 0x13, 0x72, 0x61, 0x77, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x23, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65,
	0x6e, 0x73, 0x6f, 0x72, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x11, 0x72, 0x61, 0x77, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x55, 0x0a, 0x15,
	0x72, 0x61, 0x77, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x63, 0x63,
	0x75, 0x72, 0x61, 0x63, 0x79, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f,
	0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x13,
	0x72, 0x61, 0x77, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x63, 0x75, 0x72,
	0x61, 0x63, 0x79, 0x12, 0x4d, 0x0a, 0x15, 0x73, 0x75, 0x70, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x14, 0x73, 0x75,
	0x70, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x5d, 0x0a, 0x1a, 0x73, 0x75, 0x70, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x61, 0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x18, 0x73, 0x75, 0x70, 0x70, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x6a, 0x0a, 0x1c, 0x73, 0x75, 0x70, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61,
	0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66,
	0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x42, 0x03, 0xe0, 0x41,
	0x04, 0x52, 0x1a, 0x73, 0x75, 0x70, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x67, 0x0a,
	0x1e, 0x73, 0x75, 0x70, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x04, 0x52, 0x1c, 0x73, 0x75, 0x70, 0x70, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x63,
	0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x12, 0x25, 0x0a, 0x0c, 0x72, 0x6f, 0x61, 0x64, 0x5f, 0x73,
	0x6e, 0x61, 0x70, 0x70, 0x65, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x0b, 0x72, 0x6f, 0x61, 0x64, 0x53, 0x6e, 0x61, 0x70, 0x70, 0x65, 0x64, 0x2a, 0x3c, 0x0a,
	0x08, 0x54, 0x72, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x00,
	0x12, 0x0a, 0x0a, 0x06, 0x53, 0x48, 0x41, 0x52, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09,
	0x45, 0x58, 0x43, 0x4c, 0x55, 0x53, 0x49, 0x56, 0x45, 0x10, 0x02, 0x2a, 0x8b, 0x01, 0x0a, 0x0c,
	0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x57, 0x41, 0x59, 0x50, 0x4f, 0x49, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x49, 0x43, 0x4b, 0x55,
	0x50, 0x5f, 0x57, 0x41, 0x59, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10,
	0x01, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x52, 0x4f, 0x50, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x57, 0x41,
	0x59, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x02, 0x12, 0x2a, 0x0a,
	0x26, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4d, 0x45, 0x44, 0x49, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x45,
	0x53, 0x54, 0x49, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x57, 0x41, 0x59, 0x50, 0x4f, 0x49,
	0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x03, 0x2a, 0x5f, 0x0a, 0x12, 0x50, 0x6f, 0x6c,
	0x79, 0x6c, 0x69, 0x6e, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x17, 0x0a, 0x13, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x41, 0x54, 0x5f,
	0x4c, 0x4e, 0x47, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x01, 0x12,
	0x19, 0x0a, 0x15, 0x45, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x44, 0x5f, 0x50, 0x4f, 0x4c, 0x59, 0x4c,
	0x49, 0x4e, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x02, 0x2a, 0x89, 0x01, 0x0a, 0x10, 0x4e,
	0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1d, 0x0a, 0x19, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x4e, 0x41, 0x56, 0x49, 0x47,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x00, 0x12, 0x0f,
	0x0a, 0x0b, 0x4e, 0x4f, 0x5f, 0x47, 0x55, 0x49, 0x44, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x01, 0x12,
	0x1a, 0x0a, 0x16, 0x45, 0x4e, 0x52, 0x4f, 0x55, 0x54, 0x45, 0x5f, 0x54, 0x4f, 0x5f, 0x44, 0x45,
	0x53, 0x54, 0x49, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x4f,
	0x46, 0x46, 0x5f, 0x52, 0x4f, 0x55, 0x54, 0x45, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x52,
	0x52, 0x49, 0x56, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x5f, 0x44, 0x45, 0x53, 0x54, 0x49, 0x4e, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x2a, 0x9c, 0x01, 0x0a, 0x0e, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x0e, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x53, 0x45, 0x4e, 0x53, 0x4f, 0x52, 0x10, 0x00, 0x12, 0x07, 0x0a,
	0x03, 0x47, 0x50, 0x53, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52,
	0x4b, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x41, 0x53, 0x53, 0x49, 0x56, 0x45, 0x10, 0x03,
	0x12, 0x22, 0x0a, 0x1e, 0x52, 0x4f, 0x41, 0x44, 0x5f, 0x53, 0x4e, 0x41, 0x50, 0x50, 0x45, 0x44,
	0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44,
	0x45, 0x52, 0x10, 0x04, 0x12, 0x1b, 0x0a, 0x17, 0x46, 0x55, 0x53, 0x45, 0x44, 0x5f, 0x4c, 0x4f,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x52, 0x10,
	0x64, 0x12, 0x12, 0x0a, 0x0d, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0xc8, 0x01, 0x42, 0x78, 0x0a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x2e, 0x76, 0x31, 0x42, 0x0b, 0x46, 0x6c, 0x65, 0x65, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x50, 0x01, 0x5a, 0x45, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e,
	0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x66,
	0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0xa2, 0x02, 0x03, 0x43, 0x46, 0x45, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_maps_fleetengine_v1_fleetengine_proto_rawDescOnce sync.Once
	file_google_maps_fleetengine_v1_fleetengine_proto_rawDescData = file_google_maps_fleetengine_v1_fleetengine_proto_rawDesc
)

func file_google_maps_fleetengine_v1_fleetengine_proto_rawDescGZIP() []byte {
	file_google_maps_fleetengine_v1_fleetengine_proto_rawDescOnce.Do(func() {
		file_google_maps_fleetengine_v1_fleetengine_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_maps_fleetengine_v1_fleetengine_proto_rawDescData)
	})
	return file_google_maps_fleetengine_v1_fleetengine_proto_rawDescData
}

var file_google_maps_fleetengine_v1_fleetengine_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_google_maps_fleetengine_v1_fleetengine_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_google_maps_fleetengine_v1_fleetengine_proto_goTypes = []interface{}{
	(TripType)(0),                     // 0: maps.fleetengine.v1.TripType
	(WaypointType)(0),                 // 1: maps.fleetengine.v1.WaypointType
	(PolylineFormatType)(0),           // 2: maps.fleetengine.v1.PolylineFormatType
	(NavigationStatus)(0),             // 3: maps.fleetengine.v1.NavigationStatus
	(LocationSensor)(0),               // 4: maps.fleetengine.v1.LocationSensor
	(*TerminalPointId)(nil),           // 5: maps.fleetengine.v1.TerminalPointId
	(*TerminalLocation)(nil),          // 6: maps.fleetengine.v1.TerminalLocation
	(*TripWaypoint)(nil),              // 7: maps.fleetengine.v1.TripWaypoint
	(*VehicleAttribute)(nil),          // 8: maps.fleetengine.v1.VehicleAttribute
	(*VehicleLocation)(nil),           // 9: maps.fleetengine.v1.VehicleLocation
	(*latlng.LatLng)(nil),             // 10: google.type.LatLng
	(*ConsumableTrafficPolyline)(nil), // 11: maps.fleetengine.v1.ConsumableTrafficPolyline
	(*wrapperspb.Int32Value)(nil),     // 12: google.protobuf.Int32Value
	(*timestamppb.Timestamp)(nil),     // 13: google.protobuf.Timestamp
	(*durationpb.Duration)(nil),       // 14: google.protobuf.Duration
	(*wrapperspb.DoubleValue)(nil),    // 15: google.protobuf.DoubleValue
	(*wrapperspb.BoolValue)(nil),      // 16: google.protobuf.BoolValue
}
var file_google_maps_fleetengine_v1_fleetengine_proto_depIdxs = []int32{
	10, // 0: maps.fleetengine.v1.TerminalLocation.point:type_name -> google.type.LatLng
	5,  // 1: maps.fleetengine.v1.TerminalLocation.terminal_point_id:type_name -> maps.fleetengine.v1.TerminalPointId
	1,  // 2: maps.fleetengine.v1.TerminalLocation.terminal_location_type:type_name -> maps.fleetengine.v1.WaypointType
	6,  // 3: maps.fleetengine.v1.TripWaypoint.location:type_name -> maps.fleetengine.v1.TerminalLocation
	1,  // 4: maps.fleetengine.v1.TripWaypoint.waypoint_type:type_name -> maps.fleetengine.v1.WaypointType
	10, // 5: maps.fleetengine.v1.TripWaypoint.path_to_waypoint:type_name -> google.type.LatLng
	11, // 6: maps.fleetengine.v1.TripWaypoint.traffic_to_waypoint:type_name -> maps.fleetengine.v1.ConsumableTrafficPolyline
	12, // 7: maps.fleetengine.v1.TripWaypoint.distance_meters:type_name -> google.protobuf.Int32Value
	13, // 8: maps.fleetengine.v1.TripWaypoint.eta:type_name -> google.protobuf.Timestamp
	14, // 9: maps.fleetengine.v1.TripWaypoint.duration:type_name -> google.protobuf.Duration
	10, // 10: maps.fleetengine.v1.VehicleLocation.location:type_name -> google.type.LatLng
	15, // 11: maps.fleetengine.v1.VehicleLocation.horizontal_accuracy:type_name -> google.protobuf.DoubleValue
	15, // 12: maps.fleetengine.v1.VehicleLocation.latlng_accuracy:type_name -> google.protobuf.DoubleValue
	12, // 13: maps.fleetengine.v1.VehicleLocation.heading:type_name -> google.protobuf.Int32Value
	15, // 14: maps.fleetengine.v1.VehicleLocation.bearing_accuracy:type_name -> google.protobuf.DoubleValue
	15, // 15: maps.fleetengine.v1.VehicleLocation.heading_accuracy:type_name -> google.protobuf.DoubleValue
	15, // 16: maps.fleetengine.v1.VehicleLocation.altitude:type_name -> google.protobuf.DoubleValue
	15, // 17: maps.fleetengine.v1.VehicleLocation.vertical_accuracy:type_name -> google.protobuf.DoubleValue
	15, // 18: maps.fleetengine.v1.VehicleLocation.altitude_accuracy:type_name -> google.protobuf.DoubleValue
	12, // 19: maps.fleetengine.v1.VehicleLocation.speed_kmph:type_name -> google.protobuf.Int32Value
	15, // 20: maps.fleetengine.v1.VehicleLocation.speed:type_name -> google.protobuf.DoubleValue
	15, // 21: maps.fleetengine.v1.VehicleLocation.speed_accuracy:type_name -> google.protobuf.DoubleValue
	13, // 22: maps.fleetengine.v1.VehicleLocation.update_time:type_name -> google.protobuf.Timestamp
	13, // 23: maps.fleetengine.v1.VehicleLocation.server_time:type_name -> google.protobuf.Timestamp
	4,  // 24: maps.fleetengine.v1.VehicleLocation.location_sensor:type_name -> maps.fleetengine.v1.LocationSensor
	16, // 25: maps.fleetengine.v1.VehicleLocation.is_road_snapped:type_name -> google.protobuf.BoolValue
	16, // 26: maps.fleetengine.v1.VehicleLocation.is_gps_sensor_enabled:type_name -> google.protobuf.BoolValue
	12, // 27: maps.fleetengine.v1.VehicleLocation.time_since_update:type_name -> google.protobuf.Int32Value
	12, // 28: maps.fleetengine.v1.VehicleLocation.num_stale_updates:type_name -> google.protobuf.Int32Value
	10, // 29: maps.fleetengine.v1.VehicleLocation.raw_location:type_name -> google.type.LatLng
	13, // 30: maps.fleetengine.v1.VehicleLocation.raw_location_time:type_name -> google.protobuf.Timestamp
	4,  // 31: maps.fleetengine.v1.VehicleLocation.raw_location_sensor:type_name -> maps.fleetengine.v1.LocationSensor
	15, // 32: maps.fleetengine.v1.VehicleLocation.raw_location_accuracy:type_name -> google.protobuf.DoubleValue
	10, // 33: maps.fleetengine.v1.VehicleLocation.supplemental_location:type_name -> google.type.LatLng
	13, // 34: maps.fleetengine.v1.VehicleLocation.supplemental_location_time:type_name -> google.protobuf.Timestamp
	4,  // 35: maps.fleetengine.v1.VehicleLocation.supplemental_location_sensor:type_name -> maps.fleetengine.v1.LocationSensor
	15, // 36: maps.fleetengine.v1.VehicleLocation.supplemental_location_accuracy:type_name -> google.protobuf.DoubleValue
	37, // [37:37] is the sub-list for method output_type
	37, // [37:37] is the sub-list for method input_type
	37, // [37:37] is the sub-list for extension type_name
	37, // [37:37] is the sub-list for extension extendee
	0,  // [0:37] is the sub-list for field type_name
}

func init() { file_google_maps_fleetengine_v1_fleetengine_proto_init() }
func file_google_maps_fleetengine_v1_fleetengine_proto_init() {
	if File_google_maps_fleetengine_v1_fleetengine_proto != nil {
		return
	}
	file_google_maps_fleetengine_v1_traffic_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_maps_fleetengine_v1_fleetengine_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TerminalPointId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_fleetengine_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TerminalLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_fleetengine_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TripWaypoint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_fleetengine_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VehicleAttribute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_fleetengine_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VehicleLocation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_maps_fleetengine_v1_fleetengine_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*TerminalPointId_PlaceId)(nil),
		(*TerminalPointId_GeneratedId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_maps_fleetengine_v1_fleetengine_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_maps_fleetengine_v1_fleetengine_proto_goTypes,
		DependencyIndexes: file_google_maps_fleetengine_v1_fleetengine_proto_depIdxs,
		EnumInfos:         file_google_maps_fleetengine_v1_fleetengine_proto_enumTypes,
		MessageInfos:      file_google_maps_fleetengine_v1_fleetengine_proto_msgTypes,
	}.Build()
	File_google_maps_fleetengine_v1_fleetengine_proto = out.File
	file_google_maps_fleetengine_v1_fleetengine_proto_rawDesc = nil
	file_google_maps_fleetengine_v1_fleetengine_proto_goTypes = nil
	file_google_maps_fleetengine_v1_fleetengine_proto_depIdxs = nil
}
