// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.12.2
// source: google/maps/routes/v1/route.proto

package routes

import (
	reflect "reflect"
	sync "sync"

	viewport "google.golang.org/genproto/googleapis/geo/type/viewport"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// A set of values that specify the navigation action to take for the current
// step (e.g., turn left, merge, straight, etc.).
type Maneuver int32

const (
	// Not used.
	Maneuver_MANEUVER_UNSPECIFIED Maneuver = 0
	// Turn slightly to the left.
	Maneuver_TURN_SLIGHT_LEFT Maneuver = 1
	// Turn sharply to the left.
	Maneuver_TURN_SHARP_LEFT Maneuver = 2
	// Make a left u-turn.
	Maneuver_UTURN_LEFT Maneuver = 3
	// Turn left.
	Maneuver_TURN_LEFT Maneuver = 4
	// Turn slightly to the right.
	Maneuver_TURN_SLIGHT_RIGHT Maneuver = 5
	// Turn sharply to the right.
	Maneuver_TURN_SHARP_RIGHT Maneuver = 6
	// Make a right u-turn.
	Maneuver_UTURN_RIGHT Maneuver = 7
	// Turn right.
	Maneuver_TURN_RIGHT Maneuver = 8
	// Go straight.
	Maneuver_STRAIGHT Maneuver = 9
	// Take the left ramp.
	Maneuver_RAMP_LEFT Maneuver = 10
	// Take the right ramp.
	Maneuver_RAMP_RIGHT Maneuver = 11
	// Merge into traffic.
	Maneuver_MERGE Maneuver = 12
	// Take the left fork.
	Maneuver_FORK_LEFT Maneuver = 13
	// Take the right fork.
	Maneuver_FORK_RIGHT Maneuver = 14
	// Take the ferry.
	Maneuver_FERRY Maneuver = 15
	// Take the train leading onto the ferry.
	Maneuver_FERRY_TRAIN Maneuver = 16
	// Turn left at the roundabout.
	Maneuver_ROUNDABOUT_LEFT Maneuver = 17
	// Turn right at the roundabout.
	Maneuver_ROUNDABOUT_RIGHT Maneuver = 18
)

// Enum value maps for Maneuver.
var (
	Maneuver_name = map[int32]string{
		0:  "MANEUVER_UNSPECIFIED",
		1:  "TURN_SLIGHT_LEFT",
		2:  "TURN_SHARP_LEFT",
		3:  "UTURN_LEFT",
		4:  "TURN_LEFT",
		5:  "TURN_SLIGHT_RIGHT",
		6:  "TURN_SHARP_RIGHT",
		7:  "UTURN_RIGHT",
		8:  "TURN_RIGHT",
		9:  "STRAIGHT",
		10: "RAMP_LEFT",
		11: "RAMP_RIGHT",
		12: "MERGE",
		13: "FORK_LEFT",
		14: "FORK_RIGHT",
		15: "FERRY",
		16: "FERRY_TRAIN",
		17: "ROUNDABOUT_LEFT",
		18: "ROUNDABOUT_RIGHT",
	}
	Maneuver_value = map[string]int32{
		"MANEUVER_UNSPECIFIED": 0,
		"TURN_SLIGHT_LEFT":     1,
		"TURN_SHARP_LEFT":      2,
		"UTURN_LEFT":           3,
		"TURN_LEFT":            4,
		"TURN_SLIGHT_RIGHT":    5,
		"TURN_SHARP_RIGHT":     6,
		"UTURN_RIGHT":          7,
		"TURN_RIGHT":           8,
		"STRAIGHT":             9,
		"RAMP_LEFT":            10,
		"RAMP_RIGHT":           11,
		"MERGE":                12,
		"FORK_LEFT":            13,
		"FORK_RIGHT":           14,
		"FERRY":                15,
		"FERRY_TRAIN":          16,
		"ROUNDABOUT_LEFT":      17,
		"ROUNDABOUT_RIGHT":     18,
	}
)

func (x Maneuver) Enum() *Maneuver {
	p := new(Maneuver)
	*p = x
	return p
}

func (x Maneuver) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Maneuver) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_routes_v1_route_proto_enumTypes[0].Descriptor()
}

func (Maneuver) Type() protoreflect.EnumType {
	return &file_google_maps_routes_v1_route_proto_enumTypes[0]
}

func (x Maneuver) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Maneuver.Descriptor instead.
func (Maneuver) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_routes_v1_route_proto_rawDescGZIP(), []int{0}
}

// The classification of polyline speed based on traffic data.
type SpeedReadingInterval_Speed int32

const (
	// Default value. This value is unused.
	SpeedReadingInterval_SPEED_UNSPECIFIED SpeedReadingInterval_Speed = 0
	// Normal speed, no slowdown is detected.
	SpeedReadingInterval_NORMAL SpeedReadingInterval_Speed = 1
	// Slowdown detected, but no traffic jam formed.
	SpeedReadingInterval_SLOW SpeedReadingInterval_Speed = 2
	// Traffic jam detected.
	SpeedReadingInterval_TRAFFIC_JAM SpeedReadingInterval_Speed = 3
)

// Enum value maps for SpeedReadingInterval_Speed.
var (
	SpeedReadingInterval_Speed_name = map[int32]string{
		0: "SPEED_UNSPECIFIED",
		1: "NORMAL",
		2: "SLOW",
		3: "TRAFFIC_JAM",
	}
	SpeedReadingInterval_Speed_value = map[string]int32{
		"SPEED_UNSPECIFIED": 0,
		"NORMAL":            1,
		"SLOW":              2,
		"TRAFFIC_JAM":       3,
	}
)

func (x SpeedReadingInterval_Speed) Enum() *SpeedReadingInterval_Speed {
	p := new(SpeedReadingInterval_Speed)
	*p = x
	return p
}

func (x SpeedReadingInterval_Speed) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SpeedReadingInterval_Speed) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_routes_v1_route_proto_enumTypes[1].Descriptor()
}

func (SpeedReadingInterval_Speed) Type() protoreflect.EnumType {
	return &file_google_maps_routes_v1_route_proto_enumTypes[1]
}

func (x SpeedReadingInterval_Speed) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SpeedReadingInterval_Speed.Descriptor instead.
func (SpeedReadingInterval_Speed) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_routes_v1_route_proto_rawDescGZIP(), []int{10, 0}
}

// Encapsulates a route, which consists of a series of connected road segments
// that join beginning, ending, and intermediate waypoints.
type Route struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A collection of legs (path segments between waypoints) that make-up the
	// route. Each leg corresponds to the trip between two non-`via` Waypoints.
	// For example, a route with no intermediate waypoints has only one leg. A
	// route that includes one non-`via` intermediate waypoint has two legs. A
	// route that includes one `via` intermediate waypoint has one leg. The order
	// of the legs matches the order of Waypoints from `origin` to `intermediates`
	// to `destination`.
	Legs []*RouteLeg `protobuf:"bytes,1,rep,name=legs,proto3" json:"legs,omitempty"`
	// The travel distance of the route, in meters.
	DistanceMeters int32 `protobuf:"varint,2,opt,name=distance_meters,json=distanceMeters,proto3" json:"distance_meters,omitempty"`
	// The length of time needed to navigate the route. If you set the
	// `routing_preference` to `TRAFFIC_UNAWARE`, then this value is the same as
	// `static_duration`. If you set the `routing_preference` to either
	// `TRAFFIC_AWARE` or `TRAFFIC_AWARE_OPTIMAL`, then this value is calculated
	// taking traffic conditions into account.
	Duration *durationpb.Duration `protobuf:"bytes,3,opt,name=duration,proto3" json:"duration,omitempty"`
	// The duration of traveling through the route without taking traffic
	// conditions into consideration.
	StaticDuration *durationpb.Duration `protobuf:"bytes,4,opt,name=static_duration,json=staticDuration,proto3" json:"static_duration,omitempty"`
	// The overall route polyline. This polyline will be the combined polyline of
	// all `legs`.
	Polyline *Polyline `protobuf:"bytes,5,opt,name=polyline,proto3" json:"polyline,omitempty"`
	// A description of the route.
	Description string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	// An array of warnings to show when displaying the route.
	Warnings []string `protobuf:"bytes,7,rep,name=warnings,proto3" json:"warnings,omitempty"`
	// The viewport bounding box of the polyline.
	Viewport *viewport.Viewport `protobuf:"bytes,8,opt,name=viewport,proto3" json:"viewport,omitempty"`
	// Additional information about the route.
	TravelAdvisory *RouteTravelAdvisory `protobuf:"bytes,9,opt,name=travel_advisory,json=travelAdvisory,proto3" json:"travel_advisory,omitempty"`
	// If ComputeRoutesRequest.optimize_waypoint_order is set to true, this field
	// contains the optimized ordering of intermediates waypoints.
	// otherwise, this field is empty.
	// For example, suppose the input is Origin: LA; Intermediates: Dallas,
	// Bangor, Phoenix;  Destination: New York; and the optimized intermediate
	// waypoint order is:  Phoenix, Dallas, Bangor. Then this field contains the
	// values [2, 0, 1]. The index starts with 0 for the first intermediate
	// waypoint.
	OptimizedIntermediateWaypointIndex []int32 `protobuf:"varint,10,rep,packed,name=optimized_intermediate_waypoint_index,json=optimizedIntermediateWaypointIndex,proto3" json:"optimized_intermediate_waypoint_index,omitempty"`
}

func (x *Route) Reset() {
	*x = Route{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_routes_v1_route_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Route) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Route) ProtoMessage() {}

func (x *Route) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_routes_v1_route_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Route.ProtoReflect.Descriptor instead.
func (*Route) Descriptor() ([]byte, []int) {
	return file_google_maps_routes_v1_route_proto_rawDescGZIP(), []int{0}
}

func (x *Route) GetLegs() []*RouteLeg {
	if x != nil {
		return x.Legs
	}
	return nil
}

func (x *Route) GetDistanceMeters() int32 {
	if x != nil {
		return x.DistanceMeters
	}
	return 0
}

func (x *Route) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *Route) GetStaticDuration() *durationpb.Duration {
	if x != nil {
		return x.StaticDuration
	}
	return nil
}

func (x *Route) GetPolyline() *Polyline {
	if x != nil {
		return x.Polyline
	}
	return nil
}

func (x *Route) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Route) GetWarnings() []string {
	if x != nil {
		return x.Warnings
	}
	return nil
}

func (x *Route) GetViewport() *viewport.Viewport {
	if x != nil {
		return x.Viewport
	}
	return nil
}

func (x *Route) GetTravelAdvisory() *RouteTravelAdvisory {
	if x != nil {
		return x.TravelAdvisory
	}
	return nil
}

func (x *Route) GetOptimizedIntermediateWaypointIndex() []int32 {
	if x != nil {
		return x.OptimizedIntermediateWaypointIndex
	}
	return nil
}

// Encapsulates the additional information that the user should be informed
// about, such as possible traffic zone restriction etc.
type RouteTravelAdvisory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The traffic restriction that applies to the route. A vehicle that is
	// subject to the restriction is not allowed to travel on the route. As of
	// October 2019, only Jakarta, Indonesia takes into consideration.
	TrafficRestriction *TrafficRestriction `protobuf:"bytes,1,opt,name=traffic_restriction,json=trafficRestriction,proto3" json:"traffic_restriction,omitempty"`
	// Encapsulates information about tolls on the Route.
	// This field is only populated if we expect there are tolls on the Route.
	// If this field is set but the estimated_price subfield is not populated,
	// we expect that road contains tolls but we do not know an estimated price.
	// If this field is not set, then we expect there is no toll on the Route.
	TollInfo *TollInfo `protobuf:"bytes,2,opt,name=toll_info,json=tollInfo,proto3" json:"toll_info,omitempty"`
	// Speed reading intervals detailing traffic density. Applicable in case of
	// `TRAFFIC_AWARE` and `TRAFFIC_AWARE_OPTIMAL` routing preferences.
	// The intervals cover the entire polyline of the route without overlap.
	// The start point of a specified interval is the same as the end point of the
	// preceding interval.
	//
	// Example:
	//
	//	polyline: A ---- B ---- C ---- D ---- E ---- F ---- G
	//	speed_reading_intervals: [A,C), [C,D), [D,G).
	SpeedReadingIntervals []*SpeedReadingInterval `protobuf:"bytes,3,rep,name=speed_reading_intervals,json=speedReadingIntervals,proto3" json:"speed_reading_intervals,omitempty"`
	// Information related to the custom layer data that the customer specified
	// (e.g. time spent in a customer specified area).
	CustomLayerInfo *CustomLayerInfo `protobuf:"bytes,4,opt,name=custom_layer_info,json=customLayerInfo,proto3" json:"custom_layer_info,omitempty"`
}

func (x *RouteTravelAdvisory) Reset() {
	*x = RouteTravelAdvisory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_routes_v1_route_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RouteTravelAdvisory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteTravelAdvisory) ProtoMessage() {}

func (x *RouteTravelAdvisory) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_routes_v1_route_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteTravelAdvisory.ProtoReflect.Descriptor instead.
func (*RouteTravelAdvisory) Descriptor() ([]byte, []int) {
	return file_google_maps_routes_v1_route_proto_rawDescGZIP(), []int{1}
}

func (x *RouteTravelAdvisory) GetTrafficRestriction() *TrafficRestriction {
	if x != nil {
		return x.TrafficRestriction
	}
	return nil
}

func (x *RouteTravelAdvisory) GetTollInfo() *TollInfo {
	if x != nil {
		return x.TollInfo
	}
	return nil
}

func (x *RouteTravelAdvisory) GetSpeedReadingIntervals() []*SpeedReadingInterval {
	if x != nil {
		return x.SpeedReadingIntervals
	}
	return nil
}

func (x *RouteTravelAdvisory) GetCustomLayerInfo() *CustomLayerInfo {
	if x != nil {
		return x.CustomLayerInfo
	}
	return nil
}

// Encapsulates the additional information that the user should be informed
// about, such as possible traffic zone restriction etc. on a route leg.
type RouteLegTravelAdvisory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Encapsulates information about tolls on the specific RouteLeg.
	// This field is only populated if we expect there are tolls on the RouteLeg.
	// If this field is set but the estimated_price subfield is not populated,
	// we expect that road contains tolls but we do not know an estimated price.
	// If this field does not exist, then there is no toll on the RouteLeg.
	TollInfo *TollInfo `protobuf:"bytes,1,opt,name=toll_info,json=tollInfo,proto3" json:"toll_info,omitempty"`
	// Speed reading intervals detailing traffic density. Applicable in case of
	// `TRAFFIC_AWARE` and `TRAFFIC_AWARE_OPTIMAL` routing preferences.
	// The intervals cover the entire polyline of the RouteLg without overlap.
	// The start point of a specified interval is the same as the end point of the
	// preceding interval.
	//
	// Example:
	//
	//	polyline: A ---- B ---- C ---- D ---- E ---- F ---- G
	//	speed_reading_intervals: [A,C), [C,D), [D,G).
	SpeedReadingIntervals []*SpeedReadingInterval `protobuf:"bytes,2,rep,name=speed_reading_intervals,json=speedReadingIntervals,proto3" json:"speed_reading_intervals,omitempty"`
	// Information related to the custom layer data that the customer specified
	// (e.g. time spent in a customer specified area).
	CustomLayerInfo *CustomLayerInfo `protobuf:"bytes,3,opt,name=custom_layer_info,json=customLayerInfo,proto3" json:"custom_layer_info,omitempty"`
}

func (x *RouteLegTravelAdvisory) Reset() {
	*x = RouteLegTravelAdvisory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_routes_v1_route_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RouteLegTravelAdvisory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteLegTravelAdvisory) ProtoMessage() {}

func (x *RouteLegTravelAdvisory) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_routes_v1_route_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteLegTravelAdvisory.ProtoReflect.Descriptor instead.
func (*RouteLegTravelAdvisory) Descriptor() ([]byte, []int) {
	return file_google_maps_routes_v1_route_proto_rawDescGZIP(), []int{2}
}

func (x *RouteLegTravelAdvisory) GetTollInfo() *TollInfo {
	if x != nil {
		return x.TollInfo
	}
	return nil
}

func (x *RouteLegTravelAdvisory) GetSpeedReadingIntervals() []*SpeedReadingInterval {
	if x != nil {
		return x.SpeedReadingIntervals
	}
	return nil
}

func (x *RouteLegTravelAdvisory) GetCustomLayerInfo() *CustomLayerInfo {
	if x != nil {
		return x.CustomLayerInfo
	}
	return nil
}

// Encapsulates the additional information that the user should be informed
// about, such as possible traffic zone restriction on a leg step.
type RouteLegStepTravelAdvisory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Speed reading intervals detailing traffic density. Applicable in case of
	// `TRAFFIC_AWARE` and `TRAFFIC_AWARE_OPTIMAL` routing preferences.
	// The intervals cover the entire polyline of the RouteLegStep without
	// overlap. The start point of a specified interval is the same as the end
	// point of the preceding interval.
	//
	// Example:
	//
	//	polyline: A ---- B ---- C ---- D ---- E ---- F ---- G
	//	speed_reading_intervals: [A,C), [C,D), [D,G).
	SpeedReadingIntervals []*SpeedReadingInterval `protobuf:"bytes,1,rep,name=speed_reading_intervals,json=speedReadingIntervals,proto3" json:"speed_reading_intervals,omitempty"`
}

func (x *RouteLegStepTravelAdvisory) Reset() {
	*x = RouteLegStepTravelAdvisory{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_routes_v1_route_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RouteLegStepTravelAdvisory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteLegStepTravelAdvisory) ProtoMessage() {}

func (x *RouteLegStepTravelAdvisory) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_routes_v1_route_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteLegStepTravelAdvisory.ProtoReflect.Descriptor instead.
func (*RouteLegStepTravelAdvisory) Descriptor() ([]byte, []int) {
	return file_google_maps_routes_v1_route_proto_rawDescGZIP(), []int{3}
}

func (x *RouteLegStepTravelAdvisory) GetSpeedReadingIntervals() []*SpeedReadingInterval {
	if x != nil {
		return x.SpeedReadingIntervals
	}
	return nil
}

// Encapsulates the traffic restriction applied to the route. As of October
// 2019, only Jakarta, Indonesia takes into consideration.
type TrafficRestriction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The restriction based on the vehicle's license plate last character. If
	// this field does not exist, then no restriction on route.
	LicensePlateLastCharacterRestriction *LicensePlateLastCharacterRestriction `protobuf:"bytes,1,opt,name=license_plate_last_character_restriction,json=licensePlateLastCharacterRestriction,proto3" json:"license_plate_last_character_restriction,omitempty"`
}

func (x *TrafficRestriction) Reset() {
	*x = TrafficRestriction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_routes_v1_route_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrafficRestriction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrafficRestriction) ProtoMessage() {}

func (x *TrafficRestriction) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_routes_v1_route_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrafficRestriction.ProtoReflect.Descriptor instead.
func (*TrafficRestriction) Descriptor() ([]byte, []int) {
	return file_google_maps_routes_v1_route_proto_rawDescGZIP(), []int{4}
}

func (x *TrafficRestriction) GetLicensePlateLastCharacterRestriction() *LicensePlateLastCharacterRestriction {
	if x != nil {
		return x.LicensePlateLastCharacterRestriction
	}
	return nil
}

// Encapsulates the license plate last character restriction.
type LicensePlateLastCharacterRestriction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The allowed last character of a license plate of a vehicle. Only vehicles
	// whose license plate's last characters match these are allowed to travel on
	// the route. If empty, no vehicle is allowed.
	AllowedLastCharacters []string `protobuf:"bytes,1,rep,name=allowed_last_characters,json=allowedLastCharacters,proto3" json:"allowed_last_characters,omitempty"`
}

func (x *LicensePlateLastCharacterRestriction) Reset() {
	*x = LicensePlateLastCharacterRestriction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_routes_v1_route_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LicensePlateLastCharacterRestriction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LicensePlateLastCharacterRestriction) ProtoMessage() {}

func (x *LicensePlateLastCharacterRestriction) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_routes_v1_route_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LicensePlateLastCharacterRestriction.ProtoReflect.Descriptor instead.
func (*LicensePlateLastCharacterRestriction) Descriptor() ([]byte, []int) {
	return file_google_maps_routes_v1_route_proto_rawDescGZIP(), []int{5}
}

func (x *LicensePlateLastCharacterRestriction) GetAllowedLastCharacters() []string {
	if x != nil {
		return x.AllowedLastCharacters
	}
	return nil
}

// Encapsulates a segment between non-`via` waypoints.
type RouteLeg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The travel distance of the route leg, in meters.
	DistanceMeters int32 `protobuf:"varint,1,opt,name=distance_meters,json=distanceMeters,proto3" json:"distance_meters,omitempty"`
	// The length of time needed to navigate the leg. If the `route_preference`
	// is set to `TRAFFIC_UNAWARE`, then this value is the same as
	// `static_duration`. If the `route_preference` is either `TRAFFIC_AWARE` or
	// `TRAFFIC_AWARE_OPTIMAL`, then this value is calculated taking traffic
	// conditions into account.
	Duration *durationpb.Duration `protobuf:"bytes,2,opt,name=duration,proto3" json:"duration,omitempty"`
	// The duration of traveling through the leg, calculated without taking
	// traffic conditions into consideration.
	StaticDuration *durationpb.Duration `protobuf:"bytes,3,opt,name=static_duration,json=staticDuration,proto3" json:"static_duration,omitempty"`
	// The overall polyline for this leg. This includes that each `step`'s
	// polyline.
	Polyline *Polyline `protobuf:"bytes,4,opt,name=polyline,proto3" json:"polyline,omitempty"`
	// The start location of this leg. This might be different from the provided
	// `origin`. For example, when the provided `origin` is not near a road, this
	// is a point on the road.
	StartLocation *Location `protobuf:"bytes,5,opt,name=start_location,json=startLocation,proto3" json:"start_location,omitempty"`
	// The end location of this leg. This might be different from the provided
	// `destination`. For example, when the provided `destination` is not near a
	// road, this is a point on the road.
	EndLocation *Location `protobuf:"bytes,6,opt,name=end_location,json=endLocation,proto3" json:"end_location,omitempty"`
	// An array of steps denoting segments within this leg. Each step represents
	// one navigation instruction.
	Steps []*RouteLegStep `protobuf:"bytes,7,rep,name=steps,proto3" json:"steps,omitempty"`
	// Encapsulates the additional information that the user should be informed
	// about, such as possible traffic zone restriction etc. on a route leg.
	TravelAdvisory *RouteLegTravelAdvisory `protobuf:"bytes,8,opt,name=travel_advisory,json=travelAdvisory,proto3" json:"travel_advisory,omitempty"`
}

func (x *RouteLeg) Reset() {
	*x = RouteLeg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_routes_v1_route_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RouteLeg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteLeg) ProtoMessage() {}

func (x *RouteLeg) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_routes_v1_route_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteLeg.ProtoReflect.Descriptor instead.
func (*RouteLeg) Descriptor() ([]byte, []int) {
	return file_google_maps_routes_v1_route_proto_rawDescGZIP(), []int{6}
}

func (x *RouteLeg) GetDistanceMeters() int32 {
	if x != nil {
		return x.DistanceMeters
	}
	return 0
}

func (x *RouteLeg) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *RouteLeg) GetStaticDuration() *durationpb.Duration {
	if x != nil {
		return x.StaticDuration
	}
	return nil
}

func (x *RouteLeg) GetPolyline() *Polyline {
	if x != nil {
		return x.Polyline
	}
	return nil
}

func (x *RouteLeg) GetStartLocation() *Location {
	if x != nil {
		return x.StartLocation
	}
	return nil
}

func (x *RouteLeg) GetEndLocation() *Location {
	if x != nil {
		return x.EndLocation
	}
	return nil
}

func (x *RouteLeg) GetSteps() []*RouteLegStep {
	if x != nil {
		return x.Steps
	}
	return nil
}

func (x *RouteLeg) GetTravelAdvisory() *RouteLegTravelAdvisory {
	if x != nil {
		return x.TravelAdvisory
	}
	return nil
}

// Encapsulates toll information on a `Route` or on a `RouteLeg`.
type TollInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The monetary amount of tolls for the corresponding Route or RouteLeg.
	// This list contains a money amount for each currency that is expected
	// to be charged by the toll stations. Typically this list will contain only
	// one item for routes with tolls in one currency. For international trips,
	// this list may contain multiple items to reflect tolls in different
	// currencies.
	EstimatedPrice []*money.Money `protobuf:"bytes,1,rep,name=estimated_price,json=estimatedPrice,proto3" json:"estimated_price,omitempty"`
}

func (x *TollInfo) Reset() {
	*x = TollInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_routes_v1_route_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TollInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TollInfo) ProtoMessage() {}

func (x *TollInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_routes_v1_route_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TollInfo.ProtoReflect.Descriptor instead.
func (*TollInfo) Descriptor() ([]byte, []int) {
	return file_google_maps_routes_v1_route_proto_rawDescGZIP(), []int{7}
}

func (x *TollInfo) GetEstimatedPrice() []*money.Money {
	if x != nil {
		return x.EstimatedPrice
	}
	return nil
}

// Encapsulates a segment of a `RouteLeg`. A step corresponds to a single
// navigation instruction. Route legs are made up of steps.
type RouteLegStep struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The travel distance of this step, in meters. In some circumstances, this
	// field might not have a value.
	DistanceMeters int32 `protobuf:"varint,1,opt,name=distance_meters,json=distanceMeters,proto3" json:"distance_meters,omitempty"`
	// The duration of travel through this step without taking traffic conditions
	// into consideration. In some circumstances, this field might not have a
	// value.
	StaticDuration *durationpb.Duration `protobuf:"bytes,2,opt,name=static_duration,json=staticDuration,proto3" json:"static_duration,omitempty"`
	// The polyline associated with this step.
	Polyline *Polyline `protobuf:"bytes,3,opt,name=polyline,proto3" json:"polyline,omitempty"`
	// The start location of this step.
	StartLocation *Location `protobuf:"bytes,4,opt,name=start_location,json=startLocation,proto3" json:"start_location,omitempty"`
	// The end location of this step.
	EndLocation *Location `protobuf:"bytes,5,opt,name=end_location,json=endLocation,proto3" json:"end_location,omitempty"`
	// Navigation instructions.
	NavigationInstruction *NavigationInstruction `protobuf:"bytes,6,opt,name=navigation_instruction,json=navigationInstruction,proto3" json:"navigation_instruction,omitempty"`
	// Encapsulates the additional information that the user should be informed
	// about, such as possible traffic zone restriction on a leg step.
	TravelAdvisory *RouteLegStepTravelAdvisory `protobuf:"bytes,7,opt,name=travel_advisory,json=travelAdvisory,proto3" json:"travel_advisory,omitempty"`
}

func (x *RouteLegStep) Reset() {
	*x = RouteLegStep{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_routes_v1_route_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RouteLegStep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteLegStep) ProtoMessage() {}

func (x *RouteLegStep) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_routes_v1_route_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteLegStep.ProtoReflect.Descriptor instead.
func (*RouteLegStep) Descriptor() ([]byte, []int) {
	return file_google_maps_routes_v1_route_proto_rawDescGZIP(), []int{8}
}

func (x *RouteLegStep) GetDistanceMeters() int32 {
	if x != nil {
		return x.DistanceMeters
	}
	return 0
}

func (x *RouteLegStep) GetStaticDuration() *durationpb.Duration {
	if x != nil {
		return x.StaticDuration
	}
	return nil
}

func (x *RouteLegStep) GetPolyline() *Polyline {
	if x != nil {
		return x.Polyline
	}
	return nil
}

func (x *RouteLegStep) GetStartLocation() *Location {
	if x != nil {
		return x.StartLocation
	}
	return nil
}

func (x *RouteLegStep) GetEndLocation() *Location {
	if x != nil {
		return x.EndLocation
	}
	return nil
}

func (x *RouteLegStep) GetNavigationInstruction() *NavigationInstruction {
	if x != nil {
		return x.NavigationInstruction
	}
	return nil
}

func (x *RouteLegStep) GetTravelAdvisory() *RouteLegStepTravelAdvisory {
	if x != nil {
		return x.TravelAdvisory
	}
	return nil
}

type NavigationInstruction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Encapsulates the navigation instructions for the current step (e.g., turn
	// left, merge, straight, etc.). This field determines which icon to display.
	Maneuver Maneuver `protobuf:"varint,1,opt,name=maneuver,proto3,enum=google.maps.routes.v1.Maneuver" json:"maneuver,omitempty"`
	// Instructions for navigating this step.
	Instructions string `protobuf:"bytes,2,opt,name=instructions,proto3" json:"instructions,omitempty"`
}

func (x *NavigationInstruction) Reset() {
	*x = NavigationInstruction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_routes_v1_route_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NavigationInstruction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NavigationInstruction) ProtoMessage() {}

func (x *NavigationInstruction) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_routes_v1_route_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NavigationInstruction.ProtoReflect.Descriptor instead.
func (*NavigationInstruction) Descriptor() ([]byte, []int) {
	return file_google_maps_routes_v1_route_proto_rawDescGZIP(), []int{9}
}

func (x *NavigationInstruction) GetManeuver() Maneuver {
	if x != nil {
		return x.Maneuver
	}
	return Maneuver_MANEUVER_UNSPECIFIED
}

func (x *NavigationInstruction) GetInstructions() string {
	if x != nil {
		return x.Instructions
	}
	return ""
}

// Traffic density indicator on a contiguous segment of a polyline or path.
// Given a path with points P_0, P_1, ... , P_N (zero-based index), the
// SpeedReadingInterval defines an interval and describes its traffic using the
// following categories.
type SpeedReadingInterval struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The starting index of this interval in the polyline.
	// In JSON, when the index is 0, the field appears to be unpopulated.
	StartPolylinePointIndex int32 `protobuf:"varint,1,opt,name=start_polyline_point_index,json=startPolylinePointIndex,proto3" json:"start_polyline_point_index,omitempty"`
	// The ending index of this interval in the polyline.
	// In JSON, when the index is 0, the field appears to be unpopulated.
	EndPolylinePointIndex int32 `protobuf:"varint,2,opt,name=end_polyline_point_index,json=endPolylinePointIndex,proto3" json:"end_polyline_point_index,omitempty"`
	// Traffic speed in this interval.
	Speed SpeedReadingInterval_Speed `protobuf:"varint,3,opt,name=speed,proto3,enum=google.maps.routes.v1.SpeedReadingInterval_Speed" json:"speed,omitempty"`
}

func (x *SpeedReadingInterval) Reset() {
	*x = SpeedReadingInterval{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_routes_v1_route_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpeedReadingInterval) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpeedReadingInterval) ProtoMessage() {}

func (x *SpeedReadingInterval) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_routes_v1_route_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpeedReadingInterval.ProtoReflect.Descriptor instead.
func (*SpeedReadingInterval) Descriptor() ([]byte, []int) {
	return file_google_maps_routes_v1_route_proto_rawDescGZIP(), []int{10}
}

func (x *SpeedReadingInterval) GetStartPolylinePointIndex() int32 {
	if x != nil {
		return x.StartPolylinePointIndex
	}
	return 0
}

func (x *SpeedReadingInterval) GetEndPolylinePointIndex() int32 {
	if x != nil {
		return x.EndPolylinePointIndex
	}
	return 0
}

func (x *SpeedReadingInterval) GetSpeed() SpeedReadingInterval_Speed {
	if x != nil {
		return x.Speed
	}
	return SpeedReadingInterval_SPEED_UNSPECIFIED
}

// Encapsulates statistics about the time spent and distance travelled in a
// custom area.
type CustomLayerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Encapsulates information about areas in the custom layer on the Route.
	// This field is only populated if a route travels through areas in the
	// custom layer.
	AreaInfo []*CustomLayerInfo_AreaInfo `protobuf:"bytes,1,rep,name=area_info,json=areaInfo,proto3" json:"area_info,omitempty"`
}

func (x *CustomLayerInfo) Reset() {
	*x = CustomLayerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_routes_v1_route_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomLayerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomLayerInfo) ProtoMessage() {}

func (x *CustomLayerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_routes_v1_route_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomLayerInfo.ProtoReflect.Descriptor instead.
func (*CustomLayerInfo) Descriptor() ([]byte, []int) {
	return file_google_maps_routes_v1_route_proto_rawDescGZIP(), []int{11}
}

func (x *CustomLayerInfo) GetAreaInfo() []*CustomLayerInfo_AreaInfo {
	if x != nil {
		return x.AreaInfo
	}
	return nil
}

// Encapsulates areas related information on a `Route` or on a `RouteLeg`.
type CustomLayerInfo_AreaInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ID of an area inside a customer provided dataset. An area represents a
	// collection of polygons on the map that are of concern to the customer.
	// For example, the customer may be interested in knowing whether a
	// returned route is traveling through multiple busy city blocks during
	// a predefined period of time. An area ID is unique within a single
	// dataset uploaded by a customer. That is, a (customer_id, dataset_id,
	// area_id) triplet should uniquely identify a set of polygons on the map
	// that "activates" following a common schedule.
	AreaId string `protobuf:"bytes,1,opt,name=area_id,json=areaId,proto3" json:"area_id,omitempty"`
	// Total distance traveled in the area (in meters).
	DistanceInAreaMeters float32 `protobuf:"fixed32,2,opt,name=distance_in_area_meters,json=distanceInAreaMeters,proto3" json:"distance_in_area_meters,omitempty"`
	// Total time spent in the area.
	DurationInArea *durationpb.Duration `protobuf:"bytes,3,opt,name=duration_in_area,json=durationInArea,proto3" json:"duration_in_area,omitempty"`
}

func (x *CustomLayerInfo_AreaInfo) Reset() {
	*x = CustomLayerInfo_AreaInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_routes_v1_route_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomLayerInfo_AreaInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomLayerInfo_AreaInfo) ProtoMessage() {}

func (x *CustomLayerInfo_AreaInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_routes_v1_route_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomLayerInfo_AreaInfo.ProtoReflect.Descriptor instead.
func (*CustomLayerInfo_AreaInfo) Descriptor() ([]byte, []int) {
	return file_google_maps_routes_v1_route_proto_rawDescGZIP(), []int{11, 0}
}

func (x *CustomLayerInfo_AreaInfo) GetAreaId() string {
	if x != nil {
		return x.AreaId
	}
	return ""
}

func (x *CustomLayerInfo_AreaInfo) GetDistanceInAreaMeters() float32 {
	if x != nil {
		return x.DistanceInAreaMeters
	}
	return 0
}

func (x *CustomLayerInfo_AreaInfo) GetDurationInArea() *durationpb.Duration {
	if x != nil {
		return x.DurationInArea
	}
	return nil
}

var File_google_maps_routes_v1_route_proto protoreflect.FileDescriptor

var file_google_maps_routes_v1_route_proto_rawDesc = []byte{
	0x0a, 0x21, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x72, 0x6f,
	0x75, 0x74, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x15, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73,
	0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x67, 0x65, 0x6f, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x76, 0x69, 0x65, 0x77,
	0x70, 0x6f, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x24, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x72, 0x6f,
	0x75, 0x74, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xba, 0x04, 0x0a, 0x05, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x04, 0x6c, 0x65, 0x67,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x6f, 0x75, 0x74, 0x65, 0x4c, 0x65, 0x67, 0x52, 0x04, 0x6c, 0x65, 0x67, 0x73, 0x12, 0x27,
	0x0a, 0x0f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x4d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x35, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42,
	0x0a, 0x0f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x63, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x74, 0x69, 0x63, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x08, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61,
	0x70, 0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x6c,
	0x79, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x08, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x35, 0x0a,
	0x08, 0x76, 0x69, 0x65, 0x77, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x56, 0x69, 0x65, 0x77, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x08, 0x76, 0x69, 0x65, 0x77,
	0x70, 0x6f, 0x72, 0x74, 0x12, 0x53, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x5f, 0x61,
	0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x54, 0x72, 0x61, 0x76, 0x65,
	0x6c, 0x41, 0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x76, 0x65,
	0x6c, 0x41, 0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x12, 0x51, 0x0a, 0x25, 0x6f, 0x70, 0x74,
	0x69, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6d, 0x65, 0x64, 0x69,
	0x61, 0x74, 0x65, 0x5f, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x05, 0x52, 0x22, 0x6f, 0x70, 0x74, 0x69, 0x6d, 0x69,
	0x7a, 0x65, 0x64, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x57,
	0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0xe8, 0x02, 0x0a,
	0x13, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x41, 0x64, 0x76, 0x69,
	0x73, 0x6f, 0x72, 0x79, 0x12, 0x5a, 0x0a, 0x13, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x5f,
	0x72, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e,
	0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69,
	0x63, 0x52, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x12, 0x74, 0x72,
	0x61, 0x66, 0x66, 0x69, 0x63, 0x52, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x3c, 0x0a, 0x09, 0x74, 0x6f, 0x6c, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70,
	0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f, 0x6c, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x74, 0x6f, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x63,
	0x0a, 0x17, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x6f,
	0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x70, 0x65, 0x65, 0x64, 0x52, 0x65, 0x61,
	0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x15, 0x73, 0x70,
	0x65, 0x65, 0x64, 0x52, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x73, 0x12, 0x52, 0x0a, 0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x6f, 0x75,
	0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4c, 0x61, 0x79,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x8f, 0x02, 0x0a, 0x16, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x4c, 0x65, 0x67, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x41, 0x64, 0x76, 0x69, 0x73, 0x6f,
	0x72, 0x79, 0x12, 0x3c, 0x0a, 0x09, 0x74, 0x6f, 0x6c, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d,
	0x61, 0x70, 0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x6f,
	0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x74, 0x6f, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x63, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e,
	0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x70, 0x65, 0x65, 0x64, 0x52,
	0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x15,
	0x73, 0x70, 0x65, 0x65, 0x64, 0x52, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x73, 0x12, 0x52, 0x0a, 0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72,
	0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x4c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x81, 0x01, 0x0a, 0x1a, 0x52, 0x6f,
	0x75, 0x74, 0x65, 0x4c, 0x65, 0x67, 0x53, 0x74, 0x65, 0x70, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c,
	0x41, 0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x12, 0x63, 0x0a, 0x17, 0x73, 0x70, 0x65, 0x65,
	0x64, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x70, 0x65, 0x65, 0x64, 0x52, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x15, 0x73, 0x70, 0x65, 0x65, 0x64, 0x52, 0x65, 0x61,
	0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x73, 0x22, 0xaa, 0x01,
	0x0a, 0x12, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x52, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x93, 0x01, 0x0a, 0x28, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65,
	0x5f, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x72,
	0x61, 0x63, 0x74, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x65, 0x4c, 0x61, 0x73, 0x74,
	0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x24, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x50, 0x6c, 0x61,
	0x74, 0x65, 0x4c, 0x61, 0x73, 0x74, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x5e, 0x0a, 0x24, 0x4c, 0x69,
	0x63, 0x65, 0x6e, 0x73, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x65, 0x4c, 0x61, 0x73, 0x74, 0x43, 0x68,
	0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x17, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x5f, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x15, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x4c, 0x61, 0x73, 0x74,
	0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x73, 0x22, 0x8a, 0x04, 0x0a, 0x08, 0x52,
	0x6f, 0x75, 0x74, 0x65, 0x4c, 0x65, 0x67, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0e, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x12, 0x35, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x0f, 0x73, 0x74, 0x61, 0x74, 0x69,
	0x63, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x73, 0x74, 0x61,
	0x74, 0x69, 0x63, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x08, 0x70,
	0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74,
	0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x08,
	0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x46, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72,
	0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x42, 0x0a, 0x0c, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65, 0x6e, 0x64, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70,
	0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x4c, 0x65, 0x67, 0x53, 0x74, 0x65, 0x70, 0x52, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x12,
	0x56, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x5f, 0x61, 0x64, 0x76, 0x69, 0x73, 0x6f,
	0x72, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x4c, 0x65, 0x67, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x41,
	0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x41,
	0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x22, 0x47, 0x0a, 0x08, 0x54, 0x6f, 0x6c, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x3b, 0x0a, 0x0f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x0e, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x22, 0x85, 0x04, 0x0a, 0x0c, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x4c, 0x65, 0x67, 0x53, 0x74, 0x65,
	0x70, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x42, 0x0a, 0x0f, 0x73, 0x74,
	0x61, 0x74, 0x69, 0x63, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e,
	0x73, 0x74, 0x61, 0x74, 0x69, 0x63, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b,
	0x0a, 0x08, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72,
	0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e,
	0x65, 0x52, 0x08, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x46, 0x0a, 0x0e, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70,
	0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x0c, 0x65, 0x6e, 0x64, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65, 0x6e, 0x64, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x63, 0x0a, 0x16, 0x6e, 0x61, 0x76, 0x69, 0x67,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x15, 0x6e, 0x61, 0x76, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5a, 0x0a, 0x0f,
	0x74, 0x72, 0x61, 0x76, 0x65, 0x6c, 0x5f, 0x61, 0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d,
	0x61, 0x70, 0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f,
	0x75, 0x74, 0x65, 0x4c, 0x65, 0x67, 0x53, 0x74, 0x65, 0x70, 0x54, 0x72, 0x61, 0x76, 0x65, 0x6c,
	0x41, 0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x52, 0x0e, 0x74, 0x72, 0x61, 0x76, 0x65, 0x6c,
	0x41, 0x64, 0x76, 0x69, 0x73, 0x6f, 0x72, 0x79, 0x22, 0x78, 0x0a, 0x15, 0x4e, 0x61, 0x76, 0x69,
	0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x3b, 0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x65, 0x75, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70,
	0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x61, 0x6e, 0x65,
	0x75, 0x76, 0x65, 0x72, 0x52, 0x08, 0x6d, 0x61, 0x6e, 0x65, 0x75, 0x76, 0x65, 0x72, 0x12, 0x22,
	0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x22, 0x9c, 0x02, 0x0a, 0x14, 0x53, 0x70, 0x65, 0x65, 0x64, 0x52, 0x65, 0x61, 0x64,
	0x69, 0x6e, 0x67, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x3b, 0x0a, 0x1a, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x17, 0x73, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x37, 0x0a, 0x18, 0x65, 0x6e, 0x64, 0x5f,
	0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x65, 0x6e, 0x64, 0x50,
	0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x12, 0x47, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x31, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72,
	0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x70, 0x65, 0x65, 0x64, 0x52, 0x65,
	0x61, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x53, 0x70,
	0x65, 0x65, 0x64, 0x52, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x22, 0x45, 0x0a, 0x05, 0x53, 0x70,
	0x65, 0x65, 0x64, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x50, 0x45, 0x45, 0x44, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x4f,
	0x52, 0x4d, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x53, 0x4c, 0x4f, 0x57, 0x10, 0x02,
	0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x52, 0x41, 0x46, 0x46, 0x49, 0x43, 0x5f, 0x4a, 0x41, 0x4d, 0x10,
	0x03, 0x22, 0x81, 0x02, 0x0a, 0x0f, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4c, 0x61, 0x79, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4c, 0x0a, 0x09, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x2e, 0x41, 0x72, 0x65, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x61, 0x72, 0x65, 0x61, 0x49,
	0x6e, 0x66, 0x6f, 0x1a, 0x9f, 0x01, 0x0a, 0x08, 0x41, 0x72, 0x65, 0x61, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x17, 0x0a, 0x07, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x61, 0x72, 0x65, 0x61, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x17, 0x64, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x14, 0x64, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x41, 0x72, 0x65, 0x61, 0x4d, 0x65, 0x74, 0x65, 0x72, 0x73,
	0x12, 0x43, 0x0a, 0x10, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x5f,
	0x61, 0x72, 0x65, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x41, 0x72, 0x65, 0x61, 0x2a, 0xda, 0x02, 0x0a, 0x08, 0x4d, 0x61, 0x6e, 0x65, 0x75, 0x76,
	0x65, 0x72, 0x12, 0x18, 0x0a, 0x14, 0x4d, 0x41, 0x4e, 0x45, 0x55, 0x56, 0x45, 0x52, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10,
	0x54, 0x55, 0x52, 0x4e, 0x5f, 0x53, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x4c, 0x45, 0x46, 0x54,
	0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x55, 0x52, 0x4e, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x50,
	0x5f, 0x4c, 0x45, 0x46, 0x54, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x54, 0x55, 0x52, 0x4e,
	0x5f, 0x4c, 0x45, 0x46, 0x54, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x55, 0x52, 0x4e, 0x5f,
	0x4c, 0x45, 0x46, 0x54, 0x10, 0x04, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x55, 0x52, 0x4e, 0x5f, 0x53,
	0x4c, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x52, 0x49, 0x47, 0x48, 0x54, 0x10, 0x05, 0x12, 0x14, 0x0a,
	0x10, 0x54, 0x55, 0x52, 0x4e, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x50, 0x5f, 0x52, 0x49, 0x47, 0x48,
	0x54, 0x10, 0x06, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x54, 0x55, 0x52, 0x4e, 0x5f, 0x52, 0x49, 0x47,
	0x48, 0x54, 0x10, 0x07, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x55, 0x52, 0x4e, 0x5f, 0x52, 0x49, 0x47,
	0x48, 0x54, 0x10, 0x08, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x54, 0x52, 0x41, 0x49, 0x47, 0x48, 0x54,
	0x10, 0x09, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x41, 0x4d, 0x50, 0x5f, 0x4c, 0x45, 0x46, 0x54, 0x10,
	0x0a, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x41, 0x4d, 0x50, 0x5f, 0x52, 0x49, 0x47, 0x48, 0x54, 0x10,
	0x0b, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x45, 0x52, 0x47, 0x45, 0x10, 0x0c, 0x12, 0x0d, 0x0a, 0x09,
	0x46, 0x4f, 0x52, 0x4b, 0x5f, 0x4c, 0x45, 0x46, 0x54, 0x10, 0x0d, 0x12, 0x0e, 0x0a, 0x0a, 0x46,
	0x4f, 0x52, 0x4b, 0x5f, 0x52, 0x49, 0x47, 0x48, 0x54, 0x10, 0x0e, 0x12, 0x09, 0x0a, 0x05, 0x46,
	0x45, 0x52, 0x52, 0x59, 0x10, 0x0f, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x45, 0x52, 0x52, 0x59, 0x5f,
	0x54, 0x52, 0x41, 0x49, 0x4e, 0x10, 0x10, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x4f, 0x55, 0x4e, 0x44,
	0x41, 0x42, 0x4f, 0x55, 0x54, 0x5f, 0x4c, 0x45, 0x46, 0x54, 0x10, 0x11, 0x12, 0x14, 0x0a, 0x10,
	0x52, 0x4f, 0x55, 0x4e, 0x44, 0x41, 0x42, 0x4f, 0x55, 0x54, 0x5f, 0x52, 0x49, 0x47, 0x48, 0x54,
	0x10, 0x12, 0x42, 0xa0, 0x01, 0x0a, 0x19, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x76, 0x31,
	0x42, 0x0a, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x3b,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72,
	0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x72, 0x6f, 0x75, 0x74, 0x65,
	0x73, 0x2f, 0x76, 0x31, 0x3b, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x73, 0xf8, 0x01, 0x01, 0xa2, 0x02,
	0x04, 0x47, 0x4d, 0x52, 0x53, 0xaa, 0x02, 0x15, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x4d,
	0x61, 0x70, 0x73, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x73, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x15,
	0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x4d, 0x61, 0x70, 0x73, 0x5c, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x73, 0x5c, 0x56, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_maps_routes_v1_route_proto_rawDescOnce sync.Once
	file_google_maps_routes_v1_route_proto_rawDescData = file_google_maps_routes_v1_route_proto_rawDesc
)

func file_google_maps_routes_v1_route_proto_rawDescGZIP() []byte {
	file_google_maps_routes_v1_route_proto_rawDescOnce.Do(func() {
		file_google_maps_routes_v1_route_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_maps_routes_v1_route_proto_rawDescData)
	})
	return file_google_maps_routes_v1_route_proto_rawDescData
}

var file_google_maps_routes_v1_route_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_google_maps_routes_v1_route_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_google_maps_routes_v1_route_proto_goTypes = []interface{}{
	(Maneuver)(0),                                // 0: google.maps.routes.v1.Maneuver
	(SpeedReadingInterval_Speed)(0),              // 1: google.maps.routes.v1.SpeedReadingInterval.Speed
	(*Route)(nil),                                // 2: google.maps.routes.v1.Route
	(*RouteTravelAdvisory)(nil),                  // 3: google.maps.routes.v1.RouteTravelAdvisory
	(*RouteLegTravelAdvisory)(nil),               // 4: google.maps.routes.v1.RouteLegTravelAdvisory
	(*RouteLegStepTravelAdvisory)(nil),           // 5: google.maps.routes.v1.RouteLegStepTravelAdvisory
	(*TrafficRestriction)(nil),                   // 6: google.maps.routes.v1.TrafficRestriction
	(*LicensePlateLastCharacterRestriction)(nil), // 7: google.maps.routes.v1.LicensePlateLastCharacterRestriction
	(*RouteLeg)(nil),                             // 8: google.maps.routes.v1.RouteLeg
	(*TollInfo)(nil),                             // 9: google.maps.routes.v1.TollInfo
	(*RouteLegStep)(nil),                         // 10: google.maps.routes.v1.RouteLegStep
	(*NavigationInstruction)(nil),                // 11: google.maps.routes.v1.NavigationInstruction
	(*SpeedReadingInterval)(nil),                 // 12: google.maps.routes.v1.SpeedReadingInterval
	(*CustomLayerInfo)(nil),                      // 13: google.maps.routes.v1.CustomLayerInfo
	(*CustomLayerInfo_AreaInfo)(nil),             // 14: google.maps.routes.v1.CustomLayerInfo.AreaInfo
	(*durationpb.Duration)(nil),                  // 15: google.protobuf.Duration
	(*Polyline)(nil),                             // 16: google.maps.routes.v1.Polyline
	(*viewport.Viewport)(nil),                    // 17: google.geo.type.Viewport
	(*Location)(nil),                             // 18: google.maps.routes.v1.Location
	(*money.Money)(nil),                          // 19: google.type.Money
}
var file_google_maps_routes_v1_route_proto_depIdxs = []int32{
	8,  // 0: google.maps.routes.v1.Route.legs:type_name -> google.maps.routes.v1.RouteLeg
	15, // 1: google.maps.routes.v1.Route.duration:type_name -> google.protobuf.Duration
	15, // 2: google.maps.routes.v1.Route.static_duration:type_name -> google.protobuf.Duration
	16, // 3: google.maps.routes.v1.Route.polyline:type_name -> google.maps.routes.v1.Polyline
	17, // 4: google.maps.routes.v1.Route.viewport:type_name -> google.geo.type.Viewport
	3,  // 5: google.maps.routes.v1.Route.travel_advisory:type_name -> google.maps.routes.v1.RouteTravelAdvisory
	6,  // 6: google.maps.routes.v1.RouteTravelAdvisory.traffic_restriction:type_name -> google.maps.routes.v1.TrafficRestriction
	9,  // 7: google.maps.routes.v1.RouteTravelAdvisory.toll_info:type_name -> google.maps.routes.v1.TollInfo
	12, // 8: google.maps.routes.v1.RouteTravelAdvisory.speed_reading_intervals:type_name -> google.maps.routes.v1.SpeedReadingInterval
	13, // 9: google.maps.routes.v1.RouteTravelAdvisory.custom_layer_info:type_name -> google.maps.routes.v1.CustomLayerInfo
	9,  // 10: google.maps.routes.v1.RouteLegTravelAdvisory.toll_info:type_name -> google.maps.routes.v1.TollInfo
	12, // 11: google.maps.routes.v1.RouteLegTravelAdvisory.speed_reading_intervals:type_name -> google.maps.routes.v1.SpeedReadingInterval
	13, // 12: google.maps.routes.v1.RouteLegTravelAdvisory.custom_layer_info:type_name -> google.maps.routes.v1.CustomLayerInfo
	12, // 13: google.maps.routes.v1.RouteLegStepTravelAdvisory.speed_reading_intervals:type_name -> google.maps.routes.v1.SpeedReadingInterval
	7,  // 14: google.maps.routes.v1.TrafficRestriction.license_plate_last_character_restriction:type_name -> google.maps.routes.v1.LicensePlateLastCharacterRestriction
	15, // 15: google.maps.routes.v1.RouteLeg.duration:type_name -> google.protobuf.Duration
	15, // 16: google.maps.routes.v1.RouteLeg.static_duration:type_name -> google.protobuf.Duration
	16, // 17: google.maps.routes.v1.RouteLeg.polyline:type_name -> google.maps.routes.v1.Polyline
	18, // 18: google.maps.routes.v1.RouteLeg.start_location:type_name -> google.maps.routes.v1.Location
	18, // 19: google.maps.routes.v1.RouteLeg.end_location:type_name -> google.maps.routes.v1.Location
	10, // 20: google.maps.routes.v1.RouteLeg.steps:type_name -> google.maps.routes.v1.RouteLegStep
	4,  // 21: google.maps.routes.v1.RouteLeg.travel_advisory:type_name -> google.maps.routes.v1.RouteLegTravelAdvisory
	19, // 22: google.maps.routes.v1.TollInfo.estimated_price:type_name -> google.type.Money
	15, // 23: google.maps.routes.v1.RouteLegStep.static_duration:type_name -> google.protobuf.Duration
	16, // 24: google.maps.routes.v1.RouteLegStep.polyline:type_name -> google.maps.routes.v1.Polyline
	18, // 25: google.maps.routes.v1.RouteLegStep.start_location:type_name -> google.maps.routes.v1.Location
	18, // 26: google.maps.routes.v1.RouteLegStep.end_location:type_name -> google.maps.routes.v1.Location
	11, // 27: google.maps.routes.v1.RouteLegStep.navigation_instruction:type_name -> google.maps.routes.v1.NavigationInstruction
	5,  // 28: google.maps.routes.v1.RouteLegStep.travel_advisory:type_name -> google.maps.routes.v1.RouteLegStepTravelAdvisory
	0,  // 29: google.maps.routes.v1.NavigationInstruction.maneuver:type_name -> google.maps.routes.v1.Maneuver
	1,  // 30: google.maps.routes.v1.SpeedReadingInterval.speed:type_name -> google.maps.routes.v1.SpeedReadingInterval.Speed
	14, // 31: google.maps.routes.v1.CustomLayerInfo.area_info:type_name -> google.maps.routes.v1.CustomLayerInfo.AreaInfo
	15, // 32: google.maps.routes.v1.CustomLayerInfo.AreaInfo.duration_in_area:type_name -> google.protobuf.Duration
	33, // [33:33] is the sub-list for method output_type
	33, // [33:33] is the sub-list for method input_type
	33, // [33:33] is the sub-list for extension type_name
	33, // [33:33] is the sub-list for extension extendee
	0,  // [0:33] is the sub-list for field type_name
}

func init() { file_google_maps_routes_v1_route_proto_init() }
func file_google_maps_routes_v1_route_proto_init() {
	if File_google_maps_routes_v1_route_proto != nil {
		return
	}
	file_google_maps_routes_v1_polyline_proto_init()
	file_google_maps_routes_v1_waypoint_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_maps_routes_v1_route_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Route); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_routes_v1_route_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RouteTravelAdvisory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_routes_v1_route_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RouteLegTravelAdvisory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_routes_v1_route_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RouteLegStepTravelAdvisory); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_routes_v1_route_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrafficRestriction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_routes_v1_route_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LicensePlateLastCharacterRestriction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_routes_v1_route_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RouteLeg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_routes_v1_route_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TollInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_routes_v1_route_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RouteLegStep); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_routes_v1_route_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NavigationInstruction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_routes_v1_route_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpeedReadingInterval); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_routes_v1_route_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomLayerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_routes_v1_route_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomLayerInfo_AreaInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_maps_routes_v1_route_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_maps_routes_v1_route_proto_goTypes,
		DependencyIndexes: file_google_maps_routes_v1_route_proto_depIdxs,
		EnumInfos:         file_google_maps_routes_v1_route_proto_enumTypes,
		MessageInfos:      file_google_maps_routes_v1_route_proto_msgTypes,
	}.Build()
	File_google_maps_routes_v1_route_proto = out.File
	file_google_maps_routes_v1_route_proto_rawDesc = nil
	file_google_maps_routes_v1_route_proto_goTypes = nil
	file_google_maps_routes_v1_route_proto_depIdxs = nil
}
