package com.fuck.fuckinggooo.model;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.lang.Boolean;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class ProxyNodeDao_Impl implements ProxyNodeDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<ProxyNode> __insertionAdapterOfProxyNode;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAll;

  public ProxyNodeDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfProxyNode = new EntityInsertionAdapter<ProxyNode>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `proxy_nodes` (`id`,`name`,`server`,`port`,`protocol`,`password`,`uuid`,`alterId`,`security`,`network`,`username`,`path`,`host`,`serviceName`,`flow`,`headerType`,`tls`,`sni`,`allowInsecure`,`latency`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ProxyNode entity) {
        statement.bindString(1, entity.getId());
        statement.bindString(2, entity.getName());
        statement.bindString(3, entity.getServer());
        statement.bindLong(4, entity.getPort());
        statement.bindString(5, entity.getProtocol());
        if (entity.getPassword() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getPassword());
        }
        if (entity.getUuid() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getUuid());
        }
        if (entity.getAlterId() == null) {
          statement.bindNull(8);
        } else {
          statement.bindLong(8, entity.getAlterId());
        }
        if (entity.getSecurity() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getSecurity());
        }
        if (entity.getNetwork() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getNetwork());
        }
        if (entity.getUsername() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getUsername());
        }
        if (entity.getPath() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getPath());
        }
        if (entity.getHost() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getHost());
        }
        if (entity.getServiceName() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getServiceName());
        }
        if (entity.getFlow() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getFlow());
        }
        if (entity.getHeaderType() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getHeaderType());
        }
        final Integer _tmp = entity.getTls() == null ? null : (entity.getTls() ? 1 : 0);
        if (_tmp == null) {
          statement.bindNull(17);
        } else {
          statement.bindLong(17, _tmp);
        }
        if (entity.getSni() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getSni());
        }
        final Integer _tmp_1 = entity.getAllowInsecure() == null ? null : (entity.getAllowInsecure() ? 1 : 0);
        if (_tmp_1 == null) {
          statement.bindNull(19);
        } else {
          statement.bindLong(19, _tmp_1);
        }
        statement.bindLong(20, entity.getLatency());
      }
    };
    this.__preparedStmtOfDeleteAll = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM proxy_nodes";
        return _query;
      }
    };
  }

  @Override
  public Object insertAll(final List<ProxyNode> nodes,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfProxyNode.insert(nodes);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAll(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAll.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAll.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<ProxyNode>> getAll() {
    final String _sql = "SELECT * FROM proxy_nodes";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"proxy_nodes"}, new Callable<List<ProxyNode>>() {
      @Override
      @NonNull
      public List<ProxyNode> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfServer = CursorUtil.getColumnIndexOrThrow(_cursor, "server");
          final int _cursorIndexOfPort = CursorUtil.getColumnIndexOrThrow(_cursor, "port");
          final int _cursorIndexOfProtocol = CursorUtil.getColumnIndexOrThrow(_cursor, "protocol");
          final int _cursorIndexOfPassword = CursorUtil.getColumnIndexOrThrow(_cursor, "password");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfAlterId = CursorUtil.getColumnIndexOrThrow(_cursor, "alterId");
          final int _cursorIndexOfSecurity = CursorUtil.getColumnIndexOrThrow(_cursor, "security");
          final int _cursorIndexOfNetwork = CursorUtil.getColumnIndexOrThrow(_cursor, "network");
          final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
          final int _cursorIndexOfPath = CursorUtil.getColumnIndexOrThrow(_cursor, "path");
          final int _cursorIndexOfHost = CursorUtil.getColumnIndexOrThrow(_cursor, "host");
          final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
          final int _cursorIndexOfFlow = CursorUtil.getColumnIndexOrThrow(_cursor, "flow");
          final int _cursorIndexOfHeaderType = CursorUtil.getColumnIndexOrThrow(_cursor, "headerType");
          final int _cursorIndexOfTls = CursorUtil.getColumnIndexOrThrow(_cursor, "tls");
          final int _cursorIndexOfSni = CursorUtil.getColumnIndexOrThrow(_cursor, "sni");
          final int _cursorIndexOfAllowInsecure = CursorUtil.getColumnIndexOrThrow(_cursor, "allowInsecure");
          final int _cursorIndexOfLatency = CursorUtil.getColumnIndexOrThrow(_cursor, "latency");
          final List<ProxyNode> _result = new ArrayList<ProxyNode>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ProxyNode _item;
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpServer;
            _tmpServer = _cursor.getString(_cursorIndexOfServer);
            final int _tmpPort;
            _tmpPort = _cursor.getInt(_cursorIndexOfPort);
            final String _tmpProtocol;
            _tmpProtocol = _cursor.getString(_cursorIndexOfProtocol);
            final String _tmpPassword;
            if (_cursor.isNull(_cursorIndexOfPassword)) {
              _tmpPassword = null;
            } else {
              _tmpPassword = _cursor.getString(_cursorIndexOfPassword);
            }
            final String _tmpUuid;
            if (_cursor.isNull(_cursorIndexOfUuid)) {
              _tmpUuid = null;
            } else {
              _tmpUuid = _cursor.getString(_cursorIndexOfUuid);
            }
            final Integer _tmpAlterId;
            if (_cursor.isNull(_cursorIndexOfAlterId)) {
              _tmpAlterId = null;
            } else {
              _tmpAlterId = _cursor.getInt(_cursorIndexOfAlterId);
            }
            final String _tmpSecurity;
            if (_cursor.isNull(_cursorIndexOfSecurity)) {
              _tmpSecurity = null;
            } else {
              _tmpSecurity = _cursor.getString(_cursorIndexOfSecurity);
            }
            final String _tmpNetwork;
            if (_cursor.isNull(_cursorIndexOfNetwork)) {
              _tmpNetwork = null;
            } else {
              _tmpNetwork = _cursor.getString(_cursorIndexOfNetwork);
            }
            final String _tmpUsername;
            if (_cursor.isNull(_cursorIndexOfUsername)) {
              _tmpUsername = null;
            } else {
              _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
            }
            final String _tmpPath;
            if (_cursor.isNull(_cursorIndexOfPath)) {
              _tmpPath = null;
            } else {
              _tmpPath = _cursor.getString(_cursorIndexOfPath);
            }
            final String _tmpHost;
            if (_cursor.isNull(_cursorIndexOfHost)) {
              _tmpHost = null;
            } else {
              _tmpHost = _cursor.getString(_cursorIndexOfHost);
            }
            final String _tmpServiceName;
            if (_cursor.isNull(_cursorIndexOfServiceName)) {
              _tmpServiceName = null;
            } else {
              _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
            }
            final String _tmpFlow;
            if (_cursor.isNull(_cursorIndexOfFlow)) {
              _tmpFlow = null;
            } else {
              _tmpFlow = _cursor.getString(_cursorIndexOfFlow);
            }
            final String _tmpHeaderType;
            if (_cursor.isNull(_cursorIndexOfHeaderType)) {
              _tmpHeaderType = null;
            } else {
              _tmpHeaderType = _cursor.getString(_cursorIndexOfHeaderType);
            }
            final Boolean _tmpTls;
            final Integer _tmp;
            if (_cursor.isNull(_cursorIndexOfTls)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(_cursorIndexOfTls);
            }
            _tmpTls = _tmp == null ? null : _tmp != 0;
            final String _tmpSni;
            if (_cursor.isNull(_cursorIndexOfSni)) {
              _tmpSni = null;
            } else {
              _tmpSni = _cursor.getString(_cursorIndexOfSni);
            }
            final Boolean _tmpAllowInsecure;
            final Integer _tmp_1;
            if (_cursor.isNull(_cursorIndexOfAllowInsecure)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(_cursorIndexOfAllowInsecure);
            }
            _tmpAllowInsecure = _tmp_1 == null ? null : _tmp_1 != 0;
            final long _tmpLatency;
            _tmpLatency = _cursor.getLong(_cursorIndexOfLatency);
            _item = new ProxyNode(_tmpId,_tmpName,_tmpServer,_tmpPort,_tmpProtocol,_tmpPassword,_tmpUuid,_tmpAlterId,_tmpSecurity,_tmpNetwork,_tmpUsername,_tmpPath,_tmpHost,_tmpServiceName,_tmpFlow,_tmpHeaderType,_tmpTls,_tmpSni,_tmpAllowInsecure,_tmpLatency);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<ProxyNode> getById(final String id) {
    final String _sql = "SELECT * FROM proxy_nodes WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, id);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"proxy_nodes"}, new Callable<ProxyNode>() {
      @Override
      @Nullable
      public ProxyNode call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfServer = CursorUtil.getColumnIndexOrThrow(_cursor, "server");
          final int _cursorIndexOfPort = CursorUtil.getColumnIndexOrThrow(_cursor, "port");
          final int _cursorIndexOfProtocol = CursorUtil.getColumnIndexOrThrow(_cursor, "protocol");
          final int _cursorIndexOfPassword = CursorUtil.getColumnIndexOrThrow(_cursor, "password");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfAlterId = CursorUtil.getColumnIndexOrThrow(_cursor, "alterId");
          final int _cursorIndexOfSecurity = CursorUtil.getColumnIndexOrThrow(_cursor, "security");
          final int _cursorIndexOfNetwork = CursorUtil.getColumnIndexOrThrow(_cursor, "network");
          final int _cursorIndexOfUsername = CursorUtil.getColumnIndexOrThrow(_cursor, "username");
          final int _cursorIndexOfPath = CursorUtil.getColumnIndexOrThrow(_cursor, "path");
          final int _cursorIndexOfHost = CursorUtil.getColumnIndexOrThrow(_cursor, "host");
          final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
          final int _cursorIndexOfFlow = CursorUtil.getColumnIndexOrThrow(_cursor, "flow");
          final int _cursorIndexOfHeaderType = CursorUtil.getColumnIndexOrThrow(_cursor, "headerType");
          final int _cursorIndexOfTls = CursorUtil.getColumnIndexOrThrow(_cursor, "tls");
          final int _cursorIndexOfSni = CursorUtil.getColumnIndexOrThrow(_cursor, "sni");
          final int _cursorIndexOfAllowInsecure = CursorUtil.getColumnIndexOrThrow(_cursor, "allowInsecure");
          final int _cursorIndexOfLatency = CursorUtil.getColumnIndexOrThrow(_cursor, "latency");
          final ProxyNode _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            _tmpId = _cursor.getString(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpServer;
            _tmpServer = _cursor.getString(_cursorIndexOfServer);
            final int _tmpPort;
            _tmpPort = _cursor.getInt(_cursorIndexOfPort);
            final String _tmpProtocol;
            _tmpProtocol = _cursor.getString(_cursorIndexOfProtocol);
            final String _tmpPassword;
            if (_cursor.isNull(_cursorIndexOfPassword)) {
              _tmpPassword = null;
            } else {
              _tmpPassword = _cursor.getString(_cursorIndexOfPassword);
            }
            final String _tmpUuid;
            if (_cursor.isNull(_cursorIndexOfUuid)) {
              _tmpUuid = null;
            } else {
              _tmpUuid = _cursor.getString(_cursorIndexOfUuid);
            }
            final Integer _tmpAlterId;
            if (_cursor.isNull(_cursorIndexOfAlterId)) {
              _tmpAlterId = null;
            } else {
              _tmpAlterId = _cursor.getInt(_cursorIndexOfAlterId);
            }
            final String _tmpSecurity;
            if (_cursor.isNull(_cursorIndexOfSecurity)) {
              _tmpSecurity = null;
            } else {
              _tmpSecurity = _cursor.getString(_cursorIndexOfSecurity);
            }
            final String _tmpNetwork;
            if (_cursor.isNull(_cursorIndexOfNetwork)) {
              _tmpNetwork = null;
            } else {
              _tmpNetwork = _cursor.getString(_cursorIndexOfNetwork);
            }
            final String _tmpUsername;
            if (_cursor.isNull(_cursorIndexOfUsername)) {
              _tmpUsername = null;
            } else {
              _tmpUsername = _cursor.getString(_cursorIndexOfUsername);
            }
            final String _tmpPath;
            if (_cursor.isNull(_cursorIndexOfPath)) {
              _tmpPath = null;
            } else {
              _tmpPath = _cursor.getString(_cursorIndexOfPath);
            }
            final String _tmpHost;
            if (_cursor.isNull(_cursorIndexOfHost)) {
              _tmpHost = null;
            } else {
              _tmpHost = _cursor.getString(_cursorIndexOfHost);
            }
            final String _tmpServiceName;
            if (_cursor.isNull(_cursorIndexOfServiceName)) {
              _tmpServiceName = null;
            } else {
              _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
            }
            final String _tmpFlow;
            if (_cursor.isNull(_cursorIndexOfFlow)) {
              _tmpFlow = null;
            } else {
              _tmpFlow = _cursor.getString(_cursorIndexOfFlow);
            }
            final String _tmpHeaderType;
            if (_cursor.isNull(_cursorIndexOfHeaderType)) {
              _tmpHeaderType = null;
            } else {
              _tmpHeaderType = _cursor.getString(_cursorIndexOfHeaderType);
            }
            final Boolean _tmpTls;
            final Integer _tmp;
            if (_cursor.isNull(_cursorIndexOfTls)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(_cursorIndexOfTls);
            }
            _tmpTls = _tmp == null ? null : _tmp != 0;
            final String _tmpSni;
            if (_cursor.isNull(_cursorIndexOfSni)) {
              _tmpSni = null;
            } else {
              _tmpSni = _cursor.getString(_cursorIndexOfSni);
            }
            final Boolean _tmpAllowInsecure;
            final Integer _tmp_1;
            if (_cursor.isNull(_cursorIndexOfAllowInsecure)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(_cursorIndexOfAllowInsecure);
            }
            _tmpAllowInsecure = _tmp_1 == null ? null : _tmp_1 != 0;
            final long _tmpLatency;
            _tmpLatency = _cursor.getLong(_cursorIndexOfLatency);
            _result = new ProxyNode(_tmpId,_tmpName,_tmpServer,_tmpPort,_tmpProtocol,_tmpPassword,_tmpUuid,_tmpAlterId,_tmpSecurity,_tmpNetwork,_tmpUsername,_tmpPath,_tmpHost,_tmpServiceName,_tmpFlow,_tmpHeaderType,_tmpTls,_tmpSni,_tmpAllowInsecure,_tmpLatency);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
