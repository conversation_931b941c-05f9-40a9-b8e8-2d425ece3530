// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by aliasgen. DO NOT EDIT.

// Package workflows aliases all exported identifiers in package
// "cloud.google.com/go/workflows/apiv1beta/workflowspb".
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/apiv1beta/workflowspb.
// Please read https://github.com/googleapis/google-cloud-go/blob/main/migration.md
// for more details.
package workflows

import (
	src "cloud.google.com/go/workflows/apiv1beta/workflowspb"
	grpc "google.golang.org/grpc"
)

// Deprecated: Please use consts in: cloud.google.com/go/workflows/apiv1beta/workflowspb
const (
	Workflow_ACTIVE            = src.Workflow_ACTIVE
	Workflow_STATE_UNSPECIFIED = src.Workflow_STATE_UNSPECIFIED
)

// Deprecated: Please use vars in: cloud.google.com/go/workflows/apiv1beta/workflowspb
var (
	File_google_cloud_workflows_v1beta_workflows_proto = src.File_google_cloud_workflows_v1beta_workflows_proto
	Workflow_State_name                                = src.Workflow_State_name
	Workflow_State_value                               = src.Workflow_State_value
)

// Request for the
// [CreateWorkflow][google.cloud.workflows.v1beta.Workflows.CreateWorkflow]
// method.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/apiv1beta/workflowspb
type CreateWorkflowRequest = src.CreateWorkflowRequest

// Request for the
// [DeleteWorkflow][google.cloud.workflows.v1beta.Workflows.DeleteWorkflow]
// method.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/apiv1beta/workflowspb
type DeleteWorkflowRequest = src.DeleteWorkflowRequest

// Request for the
// [GetWorkflow][google.cloud.workflows.v1beta.Workflows.GetWorkflow] method.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/apiv1beta/workflowspb
type GetWorkflowRequest = src.GetWorkflowRequest

// Request for the
// [ListWorkflows][google.cloud.workflows.v1beta.Workflows.ListWorkflows]
// method.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/apiv1beta/workflowspb
type ListWorkflowsRequest = src.ListWorkflowsRequest

// Response for the
// [ListWorkflows][google.cloud.workflows.v1beta.Workflows.ListWorkflows]
// method.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/apiv1beta/workflowspb
type ListWorkflowsResponse = src.ListWorkflowsResponse

// Represents the metadata of the long-running operation.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/apiv1beta/workflowspb
type OperationMetadata = src.OperationMetadata

// UnimplementedWorkflowsServer can be embedded to have forward compatible
// implementations.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/apiv1beta/workflowspb
type UnimplementedWorkflowsServer = src.UnimplementedWorkflowsServer

// Request for the
// [UpdateWorkflow][google.cloud.workflows.v1beta.Workflows.UpdateWorkflow]
// method.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/apiv1beta/workflowspb
type UpdateWorkflowRequest = src.UpdateWorkflowRequest

// Workflow program to be executed by Workflows.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/apiv1beta/workflowspb
type Workflow = src.Workflow
type Workflow_SourceContents = src.Workflow_SourceContents

// Describes the current state of workflow deployment. More states may be
// added in the future.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/apiv1beta/workflowspb
type Workflow_State = src.Workflow_State

// WorkflowsClient is the client API for Workflows service. For semantics
// around ctx use and closing/ending streaming RPCs, please refer to
// https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/apiv1beta/workflowspb
type WorkflowsClient = src.WorkflowsClient

// WorkflowsServer is the server API for Workflows service.
//
// Deprecated: Please use types in: cloud.google.com/go/workflows/apiv1beta/workflowspb
type WorkflowsServer = src.WorkflowsServer

// Deprecated: Please use funcs in: cloud.google.com/go/workflows/apiv1beta/workflowspb
func NewWorkflowsClient(cc grpc.ClientConnInterface) WorkflowsClient {
	return src.NewWorkflowsClient(cc)
}

// Deprecated: Please use funcs in: cloud.google.com/go/workflows/apiv1beta/workflowspb
func RegisterWorkflowsServer(s *grpc.Server, srv WorkflowsServer) {
	src.RegisterWorkflowsServer(s, srv)
}
