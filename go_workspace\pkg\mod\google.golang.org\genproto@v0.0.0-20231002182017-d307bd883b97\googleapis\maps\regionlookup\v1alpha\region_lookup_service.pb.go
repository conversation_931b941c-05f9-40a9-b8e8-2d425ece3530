// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.12.2
// source: google/maps/regionlookup/v1alpha/region_lookup_service.proto

package regionlookup

import (
	context "context"
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Lookup Region Request.
//
// Next available tag: 4
type LookupRegionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Each `RegionIdentifier` represents the desired fields used to lookup a
	// single region. See `RegionIdentifier` proto for more details and examples.
	Identifiers []*RegionIdentifier `protobuf:"bytes,1,rep,name=identifiers,proto3" json:"identifiers,omitempty"`
	// The maximum number of matches to return. The service may return fewer than
	// this value.
	//
	// If unspecified, at most 50 matches will be returned. The maximum value is
	// 1000; values above 1000 will be coerced to 1000.
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// A page token, received from a previous `LookupRegion` call. Provide this to
	// retrieve the subsequent page.
	//
	// When paginating, all other parameters provided to `LookupRegion` must match
	// the call that provided the page token.
	PageToken string `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
}

func (x *LookupRegionRequest) Reset() {
	*x = LookupRegionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LookupRegionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LookupRegionRequest) ProtoMessage() {}

func (x *LookupRegionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LookupRegionRequest.ProtoReflect.Descriptor instead.
func (*LookupRegionRequest) Descriptor() ([]byte, []int) {
	return file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_rawDescGZIP(), []int{0}
}

func (x *LookupRegionRequest) GetIdentifiers() []*RegionIdentifier {
	if x != nil {
		return x.Identifiers
	}
	return nil
}

func (x *LookupRegionRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *LookupRegionRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// Lookup Region Response.
//
// Next available tag: 3
type LookupRegionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Lookup region matches, one for each `RegionIdentifier` in
	// `LookupRegionRequest.identifiers`.
	Matches []*RegionMatch `protobuf:"bytes,1,rep,name=matches,proto3" json:"matches,omitempty"`
	// A token that can be sent as `page_token` to retrieve the next page.
	// If this field is omitted, there are no subsequent pages.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *LookupRegionResponse) Reset() {
	*x = LookupRegionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LookupRegionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LookupRegionResponse) ProtoMessage() {}

func (x *LookupRegionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LookupRegionResponse.ProtoReflect.Descriptor instead.
func (*LookupRegionResponse) Descriptor() ([]byte, []int) {
	return file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_rawDescGZIP(), []int{1}
}

func (x *LookupRegionResponse) GetMatches() []*RegionMatch {
	if x != nil {
		return x.Matches
	}
	return nil
}

func (x *LookupRegionResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// Search Region Request.
//
// Next available tag: 4
type SearchRegionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Each value represents desired search values of a single region to match.
	// The API tries to match them to Place IDs. See `RegionSearchValue`
	// proto for more info and examples.
	SearchValues []*RegionSearchValue `protobuf:"bytes,1,rep,name=search_values,json=searchValues,proto3" json:"search_values,omitempty"`
	// The maximum number of matches to return. The service may return fewer than
	// this value.
	//
	// If unspecified, at most 50 matches will be returned. The maximum value is
	// 1000; values above 1000 will be coerced to 1000.
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// A page token, received from a previous `SearchRegion` call. Provide this to
	// retrieve the subsequent page.
	//
	// When paginating, all other parameters provided to `LookupRegion` must match
	// the call that provided the page token.
	PageToken string `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
}

func (x *SearchRegionRequest) Reset() {
	*x = SearchRegionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchRegionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRegionRequest) ProtoMessage() {}

func (x *SearchRegionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRegionRequest.ProtoReflect.Descriptor instead.
func (*SearchRegionRequest) Descriptor() ([]byte, []int) {
	return file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_rawDescGZIP(), []int{2}
}

func (x *SearchRegionRequest) GetSearchValues() []*RegionSearchValue {
	if x != nil {
		return x.SearchValues
	}
	return nil
}

func (x *SearchRegionRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SearchRegionRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// Match Region Response.
//
// Next available tag: 3
type SearchRegionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Search region matches, one for each `RegionSearchValue` in
	// `SearchRegionRequest.search_values`.
	Matches []*RegionMatch `protobuf:"bytes,1,rep,name=matches,proto3" json:"matches,omitempty"`
	// A token that can be sent as `page_token` to retrieve the next page.
	// If this field is omitted, there are no subsequent pages.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *SearchRegionResponse) Reset() {
	*x = SearchRegionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchRegionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRegionResponse) ProtoMessage() {}

func (x *SearchRegionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRegionResponse.ProtoReflect.Descriptor instead.
func (*SearchRegionResponse) Descriptor() ([]byte, []int) {
	return file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_rawDescGZIP(), []int{3}
}

func (x *SearchRegionResponse) GetMatches() []*RegionMatch {
	if x != nil {
		return x.Matches
	}
	return nil
}

func (x *SearchRegionResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

var File_google_maps_regionlookup_v1alpha_region_lookup_service_proto protoreflect.FileDescriptor

var file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_rawDesc = []byte{
	0x0a, 0x3c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x2f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x20,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x6d, 0x61, 0x70, 0x73, 0x2f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75,
	0x70, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x33, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x2f, 0x76, 0x31, 0x61, 0x6c,
	0x70, 0x68, 0x61, 0x2f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d,
	0x61, 0x70, 0x73, 0x2f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70,
	0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xa7, 0x01, 0x0a, 0x13, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x52, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x54, 0x0a, 0x0b, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x61, 0x6c,
	0x70, 0x68, 0x61, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x52, 0x0b, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x73, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x87, 0x01,
	0x0a, 0x14, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b,
	0x75, 0x70, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x12,
	0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61,
	0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xab, 0x01, 0x0a, 0x13, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x58, 0x0a, 0x0d, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75,
	0x70, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x0c, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x87, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47,
	0x0a, 0x07, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x52, 0x07,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x32,
	0xf2, 0x02, 0x0a, 0x0c, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70,
	0x12, 0x9f, 0x01, 0x0a, 0x0c, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x12, 0x35, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x61,
	0x6c, 0x70, 0x68, 0x61, 0x2e, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f,
	0x6b, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x4c, 0x6f, 0x6f, 0x6b,
	0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x22, 0x15, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x3a, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x3a,
	0x01, 0x2a, 0x12, 0x9f, 0x01, 0x0a, 0x0c, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x12, 0x35, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70,
	0x73, 0x2e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x2e, 0x76,
	0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x6c,
	0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x22, 0x15, 0x2f, 0x76, 0x31, 0x61,
	0x6c, 0x70, 0x68, 0x61, 0x3a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x3a, 0x01, 0x2a, 0x1a, 0x1e, 0xca, 0x41, 0x1b, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x6c,
	0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x63, 0x6f, 0x6d, 0x42, 0xe2, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x6c,
	0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x42, 0x18, 0x52,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x4c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73,
	0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x6c, 0x6f, 0x6f, 0x6b,
	0x75, 0x70, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x3b, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x6c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0xf8, 0x01, 0x01, 0xa2, 0x02, 0x06, 0x4d, 0x52, 0x4c,
	0x56, 0x31, 0x41, 0xaa, 0x02, 0x20, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x4d, 0x61, 0x70,
	0x73, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x2e, 0x56,
	0x31, 0x41, 0x6c, 0x70, 0x68, 0x61, 0xca, 0x02, 0x20, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c,
	0x4d, 0x61, 0x70, 0x73, 0x5c, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x6f, 0x6b, 0x75,
	0x70, 0x5c, 0x56, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_rawDescOnce sync.Once
	file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_rawDescData = file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_rawDesc
)

func file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_rawDescGZIP() []byte {
	file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_rawDescOnce.Do(func() {
		file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_rawDescData)
	})
	return file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_rawDescData
}

var file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_goTypes = []interface{}{
	(*LookupRegionRequest)(nil),  // 0: google.maps.regionlookup.v1alpha.LookupRegionRequest
	(*LookupRegionResponse)(nil), // 1: google.maps.regionlookup.v1alpha.LookupRegionResponse
	(*SearchRegionRequest)(nil),  // 2: google.maps.regionlookup.v1alpha.SearchRegionRequest
	(*SearchRegionResponse)(nil), // 3: google.maps.regionlookup.v1alpha.SearchRegionResponse
	(*RegionIdentifier)(nil),     // 4: google.maps.regionlookup.v1alpha.RegionIdentifier
	(*RegionMatch)(nil),          // 5: google.maps.regionlookup.v1alpha.RegionMatch
	(*RegionSearchValue)(nil),    // 6: google.maps.regionlookup.v1alpha.RegionSearchValue
}
var file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_depIdxs = []int32{
	4, // 0: google.maps.regionlookup.v1alpha.LookupRegionRequest.identifiers:type_name -> google.maps.regionlookup.v1alpha.RegionIdentifier
	5, // 1: google.maps.regionlookup.v1alpha.LookupRegionResponse.matches:type_name -> google.maps.regionlookup.v1alpha.RegionMatch
	6, // 2: google.maps.regionlookup.v1alpha.SearchRegionRequest.search_values:type_name -> google.maps.regionlookup.v1alpha.RegionSearchValue
	5, // 3: google.maps.regionlookup.v1alpha.SearchRegionResponse.matches:type_name -> google.maps.regionlookup.v1alpha.RegionMatch
	0, // 4: google.maps.regionlookup.v1alpha.RegionLookup.LookupRegion:input_type -> google.maps.regionlookup.v1alpha.LookupRegionRequest
	2, // 5: google.maps.regionlookup.v1alpha.RegionLookup.SearchRegion:input_type -> google.maps.regionlookup.v1alpha.SearchRegionRequest
	1, // 6: google.maps.regionlookup.v1alpha.RegionLookup.LookupRegion:output_type -> google.maps.regionlookup.v1alpha.LookupRegionResponse
	3, // 7: google.maps.regionlookup.v1alpha.RegionLookup.SearchRegion:output_type -> google.maps.regionlookup.v1alpha.SearchRegionResponse
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_init() }
func file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_init() {
	if File_google_maps_regionlookup_v1alpha_region_lookup_service_proto != nil {
		return
	}
	file_google_maps_regionlookup_v1alpha_region_identifier_proto_init()
	file_google_maps_regionlookup_v1alpha_region_match_proto_init()
	file_google_maps_regionlookup_v1alpha_region_search_values_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LookupRegionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LookupRegionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchRegionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchRegionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_goTypes,
		DependencyIndexes: file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_depIdxs,
		MessageInfos:      file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_msgTypes,
	}.Build()
	File_google_maps_regionlookup_v1alpha_region_lookup_service_proto = out.File
	file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_rawDesc = nil
	file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_goTypes = nil
	file_google_maps_regionlookup_v1alpha_region_lookup_service_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// RegionLookupClient is the client API for RegionLookup service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RegionLookupClient interface {
	// Lookup region RPC.
	//
	// Looks up a set of region Place IDs of types related to geographic
	// boundaries.
	//
	// The API looks up a region Place ID using the `RegionIdentifier` proto. See
	// `RegionIdentifier` for more details and examples.
	//
	// The following region place types are supported for look up: postal_code,
	// administrative_area_level_1, administrative_area_level_2, locality,
	// neighborhood, and country.
	LookupRegion(ctx context.Context, in *LookupRegionRequest, opts ...grpc.CallOption) (*LookupRegionResponse, error)
	// Search region RPC.
	//
	// Searches for a set of region Place IDs of types related to geographic
	// boundaries.
	//
	// Similar to `LookupRegion` RPC but instead of looking up Place IDs for the
	// given `RegionIdentifier`, the API searches for Region Place IDs by
	// considering all regions that are contained within a specified location. The
	// `RegionSearchValue` is used to specify the search values. See
	// `RegionSearchValue` for more details and examples.
	//
	// The following region place types are supported for searching: postal_code,
	// administrative_area_level_1, administrative_area_level_2, locality,
	// neighborhood, and country.
	SearchRegion(ctx context.Context, in *SearchRegionRequest, opts ...grpc.CallOption) (*SearchRegionResponse, error)
}

type regionLookupClient struct {
	cc grpc.ClientConnInterface
}

func NewRegionLookupClient(cc grpc.ClientConnInterface) RegionLookupClient {
	return &regionLookupClient{cc}
}

func (c *regionLookupClient) LookupRegion(ctx context.Context, in *LookupRegionRequest, opts ...grpc.CallOption) (*LookupRegionResponse, error) {
	out := new(LookupRegionResponse)
	err := c.cc.Invoke(ctx, "/google.maps.regionlookup.v1alpha.RegionLookup/LookupRegion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *regionLookupClient) SearchRegion(ctx context.Context, in *SearchRegionRequest, opts ...grpc.CallOption) (*SearchRegionResponse, error) {
	out := new(SearchRegionResponse)
	err := c.cc.Invoke(ctx, "/google.maps.regionlookup.v1alpha.RegionLookup/SearchRegion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RegionLookupServer is the server API for RegionLookup service.
type RegionLookupServer interface {
	// Lookup region RPC.
	//
	// Looks up a set of region Place IDs of types related to geographic
	// boundaries.
	//
	// The API looks up a region Place ID using the `RegionIdentifier` proto. See
	// `RegionIdentifier` for more details and examples.
	//
	// The following region place types are supported for look up: postal_code,
	// administrative_area_level_1, administrative_area_level_2, locality,
	// neighborhood, and country.
	LookupRegion(context.Context, *LookupRegionRequest) (*LookupRegionResponse, error)
	// Search region RPC.
	//
	// Searches for a set of region Place IDs of types related to geographic
	// boundaries.
	//
	// Similar to `LookupRegion` RPC but instead of looking up Place IDs for the
	// given `RegionIdentifier`, the API searches for Region Place IDs by
	// considering all regions that are contained within a specified location. The
	// `RegionSearchValue` is used to specify the search values. See
	// `RegionSearchValue` for more details and examples.
	//
	// The following region place types are supported for searching: postal_code,
	// administrative_area_level_1, administrative_area_level_2, locality,
	// neighborhood, and country.
	SearchRegion(context.Context, *SearchRegionRequest) (*SearchRegionResponse, error)
}

// UnimplementedRegionLookupServer can be embedded to have forward compatible implementations.
type UnimplementedRegionLookupServer struct {
}

func (*UnimplementedRegionLookupServer) LookupRegion(context.Context, *LookupRegionRequest) (*LookupRegionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LookupRegion not implemented")
}
func (*UnimplementedRegionLookupServer) SearchRegion(context.Context, *SearchRegionRequest) (*SearchRegionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchRegion not implemented")
}

func RegisterRegionLookupServer(s *grpc.Server, srv RegionLookupServer) {
	s.RegisterService(&_RegionLookup_serviceDesc, srv)
}

func _RegionLookup_LookupRegion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LookupRegionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegionLookupServer).LookupRegion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.maps.regionlookup.v1alpha.RegionLookup/LookupRegion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegionLookupServer).LookupRegion(ctx, req.(*LookupRegionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RegionLookup_SearchRegion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchRegionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegionLookupServer).SearchRegion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.maps.regionlookup.v1alpha.RegionLookup/SearchRegion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegionLookupServer).SearchRegion(ctx, req.(*SearchRegionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _RegionLookup_serviceDesc = grpc.ServiceDesc{
	ServiceName: "google.maps.regionlookup.v1alpha.RegionLookup",
	HandlerType: (*RegionLookupServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "LookupRegion",
			Handler:    _RegionLookup_LookupRegion_Handler,
		},
		{
			MethodName: "SearchRegion",
			Handler:    _RegionLookup_SearchRegion_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "google/maps/regionlookup/v1alpha/region_lookup_service.proto",
}
