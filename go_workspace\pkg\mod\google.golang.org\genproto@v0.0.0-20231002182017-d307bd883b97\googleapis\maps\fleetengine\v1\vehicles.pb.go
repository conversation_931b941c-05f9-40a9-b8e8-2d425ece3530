// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.21.9
// source: google/maps/fleetengine/v1/vehicles.proto

package fleetengine

import (
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The state of a `Vehicle`.
type VehicleState int32

const (
	// Default, used for unspecified or unrecognized vehicle states.
	VehicleState_UNKNOWN_VEHICLE_STATE VehicleState = 0
	// The vehicle is not accepting new trips. Note: the vehicle may continue to
	// operate in this state while completing a trip assigned to it.
	VehicleState_OFFLINE VehicleState = 1
	// The vehicle is accepting new trips.
	VehicleState_ONLINE VehicleState = 2
)

// Enum value maps for VehicleState.
var (
	VehicleState_name = map[int32]string{
		0: "UNKNOWN_VEHICLE_STATE",
		1: "OFFLINE",
		2: "ONLINE",
	}
	VehicleState_value = map[string]int32{
		"UNKNOWN_VEHICLE_STATE": 0,
		"OFFLINE":               1,
		"ONLINE":                2,
	}
)

func (x VehicleState) Enum() *VehicleState {
	p := new(VehicleState)
	*p = x
	return p
}

func (x VehicleState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VehicleState) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_v1_vehicles_proto_enumTypes[0].Descriptor()
}

func (VehicleState) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_v1_vehicles_proto_enumTypes[0]
}

func (x VehicleState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VehicleState.Descriptor instead.
func (VehicleState) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicles_proto_rawDescGZIP(), []int{0}
}

// How location features are configured to behave on the mobile device when the
// devices "battery saver" feature is on.
// (https://developer.android.com/reference/android/os/PowerManager#getLocationPowerSaveMode())
type LocationPowerSaveMode int32

const (
	// Undefined LocationPowerSaveMode
	LocationPowerSaveMode_UNKNOWN_LOCATION_POWER_SAVE_MODE LocationPowerSaveMode = 0
	// Either the location providers shouldn't be affected by battery saver, or
	// battery saver is off.
	LocationPowerSaveMode_LOCATION_MODE_NO_CHANGE LocationPowerSaveMode = 1
	// The GPS based location provider should be disabled when battery saver is on
	// and the device is non-interactive.
	LocationPowerSaveMode_LOCATION_MODE_GPS_DISABLED_WHEN_SCREEN_OFF LocationPowerSaveMode = 2
	// All location providers should be disabled when battery saver is on and the
	// device is non-interactive.
	LocationPowerSaveMode_LOCATION_MODE_ALL_DISABLED_WHEN_SCREEN_OFF LocationPowerSaveMode = 3
	// All the location providers will be kept available, but location fixes
	// should only be provided to foreground apps.
	LocationPowerSaveMode_LOCATION_MODE_FOREGROUND_ONLY LocationPowerSaveMode = 4
	// Location will not be turned off, but LocationManager will throttle all
	// requests to providers when the device is non-interactive.
	LocationPowerSaveMode_LOCATION_MODE_THROTTLE_REQUESTS_WHEN_SCREEN_OFF LocationPowerSaveMode = 5
)

// Enum value maps for LocationPowerSaveMode.
var (
	LocationPowerSaveMode_name = map[int32]string{
		0: "UNKNOWN_LOCATION_POWER_SAVE_MODE",
		1: "LOCATION_MODE_NO_CHANGE",
		2: "LOCATION_MODE_GPS_DISABLED_WHEN_SCREEN_OFF",
		3: "LOCATION_MODE_ALL_DISABLED_WHEN_SCREEN_OFF",
		4: "LOCATION_MODE_FOREGROUND_ONLY",
		5: "LOCATION_MODE_THROTTLE_REQUESTS_WHEN_SCREEN_OFF",
	}
	LocationPowerSaveMode_value = map[string]int32{
		"UNKNOWN_LOCATION_POWER_SAVE_MODE":                0,
		"LOCATION_MODE_NO_CHANGE":                         1,
		"LOCATION_MODE_GPS_DISABLED_WHEN_SCREEN_OFF":      2,
		"LOCATION_MODE_ALL_DISABLED_WHEN_SCREEN_OFF":      3,
		"LOCATION_MODE_FOREGROUND_ONLY":                   4,
		"LOCATION_MODE_THROTTLE_REQUESTS_WHEN_SCREEN_OFF": 5,
	}
)

func (x LocationPowerSaveMode) Enum() *LocationPowerSaveMode {
	p := new(LocationPowerSaveMode)
	*p = x
	return p
}

func (x LocationPowerSaveMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LocationPowerSaveMode) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_v1_vehicles_proto_enumTypes[1].Descriptor()
}

func (LocationPowerSaveMode) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_v1_vehicles_proto_enumTypes[1]
}

func (x LocationPowerSaveMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LocationPowerSaveMode.Descriptor instead.
func (LocationPowerSaveMode) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicles_proto_rawDescGZIP(), []int{1}
}

// Status of the battery, whether full or charging etc.
type BatteryStatus int32

const (
	// Battery status unknown.
	BatteryStatus_UNKNOWN_BATTERY_STATUS BatteryStatus = 0
	// Battery is being charged.
	BatteryStatus_BATTERY_STATUS_CHARGING BatteryStatus = 1
	// Battery is discharging.
	BatteryStatus_BATTERY_STATUS_DISCHARGING BatteryStatus = 2
	// Battery is full.
	BatteryStatus_BATTERY_STATUS_FULL BatteryStatus = 3
	// Battery is not charging.
	BatteryStatus_BATTERY_STATUS_NOT_CHARGING BatteryStatus = 4
	// Battery is low on power.
	BatteryStatus_BATTERY_STATUS_POWER_LOW BatteryStatus = 5
)

// Enum value maps for BatteryStatus.
var (
	BatteryStatus_name = map[int32]string{
		0: "UNKNOWN_BATTERY_STATUS",
		1: "BATTERY_STATUS_CHARGING",
		2: "BATTERY_STATUS_DISCHARGING",
		3: "BATTERY_STATUS_FULL",
		4: "BATTERY_STATUS_NOT_CHARGING",
		5: "BATTERY_STATUS_POWER_LOW",
	}
	BatteryStatus_value = map[string]int32{
		"UNKNOWN_BATTERY_STATUS":      0,
		"BATTERY_STATUS_CHARGING":     1,
		"BATTERY_STATUS_DISCHARGING":  2,
		"BATTERY_STATUS_FULL":         3,
		"BATTERY_STATUS_NOT_CHARGING": 4,
		"BATTERY_STATUS_POWER_LOW":    5,
	}
)

func (x BatteryStatus) Enum() *BatteryStatus {
	p := new(BatteryStatus)
	*p = x
	return p
}

func (x BatteryStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BatteryStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_v1_vehicles_proto_enumTypes[2].Descriptor()
}

func (BatteryStatus) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_v1_vehicles_proto_enumTypes[2]
}

func (x BatteryStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BatteryStatus.Descriptor instead.
func (BatteryStatus) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicles_proto_rawDescGZIP(), []int{2}
}

// Type of the charger being used to charge the battery.
type PowerSource int32

const (
	// Power source unknown.
	PowerSource_UNKNOWN_POWER_SOURCE PowerSource = 0
	// Power source is an AC charger.
	PowerSource_POWER_SOURCE_AC PowerSource = 1
	// Power source is a USB port.
	PowerSource_POWER_SOURCE_USB PowerSource = 2
	// Power source is wireless.
	PowerSource_POWER_SOURCE_WIRELESS PowerSource = 3
	// Battery is unplugged.
	PowerSource_POWER_SOURCE_UNPLUGGED PowerSource = 4
)

// Enum value maps for PowerSource.
var (
	PowerSource_name = map[int32]string{
		0: "UNKNOWN_POWER_SOURCE",
		1: "POWER_SOURCE_AC",
		2: "POWER_SOURCE_USB",
		3: "POWER_SOURCE_WIRELESS",
		4: "POWER_SOURCE_UNPLUGGED",
	}
	PowerSource_value = map[string]int32{
		"UNKNOWN_POWER_SOURCE":   0,
		"POWER_SOURCE_AC":        1,
		"POWER_SOURCE_USB":       2,
		"POWER_SOURCE_WIRELESS":  3,
		"POWER_SOURCE_UNPLUGGED": 4,
	}
)

func (x PowerSource) Enum() *PowerSource {
	p := new(PowerSource)
	*p = x
	return p
}

func (x PowerSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PowerSource) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_v1_vehicles_proto_enumTypes[3].Descriptor()
}

func (PowerSource) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_v1_vehicles_proto_enumTypes[3]
}

func (x PowerSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PowerSource.Descriptor instead.
func (PowerSource) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicles_proto_rawDescGZIP(), []int{3}
}

// Vehicle type categories
type Vehicle_VehicleType_Category int32

const (
	// Default, used for unspecified or unrecognized vehicle categories.
	Vehicle_VehicleType_UNKNOWN Vehicle_VehicleType_Category = 0
	// An automobile.
	Vehicle_VehicleType_AUTO Vehicle_VehicleType_Category = 1
	// Any vehicle that acts as a taxi (typically licensed or regulated).
	Vehicle_VehicleType_TAXI Vehicle_VehicleType_Category = 2
	// Generally, a vehicle with a large storage capacity.
	Vehicle_VehicleType_TRUCK Vehicle_VehicleType_Category = 3
	// A motorcycle, moped, or other two-wheeled vehicle
	Vehicle_VehicleType_TWO_WHEELER Vehicle_VehicleType_Category = 4
)

// Enum value maps for Vehicle_VehicleType_Category.
var (
	Vehicle_VehicleType_Category_name = map[int32]string{
		0: "UNKNOWN",
		1: "AUTO",
		2: "TAXI",
		3: "TRUCK",
		4: "TWO_WHEELER",
	}
	Vehicle_VehicleType_Category_value = map[string]int32{
		"UNKNOWN":     0,
		"AUTO":        1,
		"TAXI":        2,
		"TRUCK":       3,
		"TWO_WHEELER": 4,
	}
)

func (x Vehicle_VehicleType_Category) Enum() *Vehicle_VehicleType_Category {
	p := new(Vehicle_VehicleType_Category)
	*p = x
	return p
}

func (x Vehicle_VehicleType_Category) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Vehicle_VehicleType_Category) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_v1_vehicles_proto_enumTypes[4].Descriptor()
}

func (Vehicle_VehicleType_Category) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_v1_vehicles_proto_enumTypes[4]
}

func (x Vehicle_VehicleType_Category) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Vehicle_VehicleType_Category.Descriptor instead.
func (Vehicle_VehicleType_Category) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicles_proto_rawDescGZIP(), []int{0, 0, 0}
}

// The traffic style, indicating traffic speed.
type VisualTrafficReportPolylineRendering_RoadStretch_Style int32

const (
	// No style selected.
	VisualTrafficReportPolylineRendering_RoadStretch_STYLE_UNSPECIFIED VisualTrafficReportPolylineRendering_RoadStretch_Style = 0
	// Traffic is slowing down.
	VisualTrafficReportPolylineRendering_RoadStretch_SLOWER_TRAFFIC VisualTrafficReportPolylineRendering_RoadStretch_Style = 1
	// There is a traffic jam.
	VisualTrafficReportPolylineRendering_RoadStretch_TRAFFIC_JAM VisualTrafficReportPolylineRendering_RoadStretch_Style = 2
)

// Enum value maps for VisualTrafficReportPolylineRendering_RoadStretch_Style.
var (
	VisualTrafficReportPolylineRendering_RoadStretch_Style_name = map[int32]string{
		0: "STYLE_UNSPECIFIED",
		1: "SLOWER_TRAFFIC",
		2: "TRAFFIC_JAM",
	}
	VisualTrafficReportPolylineRendering_RoadStretch_Style_value = map[string]int32{
		"STYLE_UNSPECIFIED": 0,
		"SLOWER_TRAFFIC":    1,
		"TRAFFIC_JAM":       2,
	}
)

func (x VisualTrafficReportPolylineRendering_RoadStretch_Style) Enum() *VisualTrafficReportPolylineRendering_RoadStretch_Style {
	p := new(VisualTrafficReportPolylineRendering_RoadStretch_Style)
	*p = x
	return p
}

func (x VisualTrafficReportPolylineRendering_RoadStretch_Style) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VisualTrafficReportPolylineRendering_RoadStretch_Style) Descriptor() protoreflect.EnumDescriptor {
	return file_google_maps_fleetengine_v1_vehicles_proto_enumTypes[5].Descriptor()
}

func (VisualTrafficReportPolylineRendering_RoadStretch_Style) Type() protoreflect.EnumType {
	return &file_google_maps_fleetengine_v1_vehicles_proto_enumTypes[5]
}

func (x VisualTrafficReportPolylineRendering_RoadStretch_Style) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VisualTrafficReportPolylineRendering_RoadStretch_Style.Descriptor instead.
func (VisualTrafficReportPolylineRendering_RoadStretch_Style) EnumDescriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicles_proto_rawDescGZIP(), []int{4, 0, 0}
}

// Vehicle metadata.
type Vehicle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Output only. The unique name for this vehicle.
	// The format is `providers/{provider}/vehicles/{vehicle}`.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The vehicle state.
	VehicleState VehicleState `protobuf:"varint,2,opt,name=vehicle_state,json=vehicleState,proto3,enum=maps.fleetengine.v1.VehicleState" json:"vehicle_state,omitempty"`
	// Trip types supported by this vehicle.
	SupportedTripTypes []TripType `protobuf:"varint,3,rep,packed,name=supported_trip_types,json=supportedTripTypes,proto3,enum=maps.fleetengine.v1.TripType" json:"supported_trip_types,omitempty"`
	// Output only. List of `trip_id`'s for trips currently assigned to this
	// vehicle.
	CurrentTrips []string `protobuf:"bytes,4,rep,name=current_trips,json=currentTrips,proto3" json:"current_trips,omitempty"`
	// Last reported location of the vehicle.
	LastLocation *VehicleLocation `protobuf:"bytes,5,opt,name=last_location,json=lastLocation,proto3" json:"last_location,omitempty"`
	// The total numbers of riders this vehicle can carry.  The driver is not
	// considered in this value. This value must be greater than or equal to one.
	MaximumCapacity int32 `protobuf:"varint,6,opt,name=maximum_capacity,json=maximumCapacity,proto3" json:"maximum_capacity,omitempty"`
	// List of vehicle attributes. A vehicle can have at most 50
	// attributes, and each attribute must have a unique key.
	Attributes []*VehicleAttribute `protobuf:"bytes,8,rep,name=attributes,proto3" json:"attributes,omitempty"`
	// The type of this vehicle.  Can be used to filter vehicles in
	// `SearchVehicles` results.  Also influences ETA and route calculations.
	VehicleType *Vehicle_VehicleType `protobuf:"bytes,9,opt,name=vehicle_type,json=vehicleType,proto3" json:"vehicle_type,omitempty"`
	// License plate information for the vehicle.
	LicensePlate *LicensePlate `protobuf:"bytes,10,opt,name=license_plate,json=licensePlate,proto3" json:"license_plate,omitempty"`
	// Deprecated: Use `Vehicle.waypoints` instead.
	//
	// Deprecated: Do not use.
	Route []*TerminalLocation `protobuf:"bytes,12,rep,name=route,proto3" json:"route,omitempty"`
	// The polyline specifying the route the driver app intends to take to
	// the next waypoint. This list is also returned in
	// `Trip.current_route_segment` for all active trips assigned to the vehicle.
	//
	// Note: This field is intended only for use by the Driver SDK. Decoding is
	// not yet supported.
	CurrentRouteSegment string `protobuf:"bytes,20,opt,name=current_route_segment,json=currentRouteSegment,proto3" json:"current_route_segment,omitempty"`
	// Input only. Fleet Engine uses this information to improve Journey Sharing.
	CurrentRouteSegmentTraffic *TrafficPolylineData `protobuf:"bytes,28,opt,name=current_route_segment_traffic,json=currentRouteSegmentTraffic,proto3" json:"current_route_segment_traffic,omitempty"`
	// Output only. Time when `current_route_segment` was set. It can be stored by
	// the client and passed in future `GetVehicle` requests to prevent returning
	// routes that haven't changed.
	CurrentRouteSegmentVersion *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=current_route_segment_version,json=currentRouteSegmentVersion,proto3" json:"current_route_segment_version,omitempty"`
	// The waypoint where `current_route_segment` ends. This can be supplied by
	// drivers on `UpdateVehicle` calls either as a full trip waypoint, a waypoint
	// `LatLng`, or as the last `LatLng` of the `current_route_segment`. Fleet
	// Engine will then do its best to interpolate to an actual waypoint if it is
	// not fully specified. This field is ignored in `UpdateVehicle` calls unless
	// `current_route_segment` is also specified.
	CurrentRouteSegmentEndPoint *TripWaypoint `protobuf:"bytes,24,opt,name=current_route_segment_end_point,json=currentRouteSegmentEndPoint,proto3" json:"current_route_segment_end_point,omitempty"`
	// The remaining driving distance for the `current_route_segment`.
	// This value is also returned in `Trip.remaining_distance_meters` for all
	// active trips assigned to the vehicle. The value is unspecified if the
	// `current_route_segment` field is empty.
	RemainingDistanceMeters *wrapperspb.Int32Value `protobuf:"bytes,18,opt,name=remaining_distance_meters,json=remainingDistanceMeters,proto3" json:"remaining_distance_meters,omitempty"`
	// The ETA to the first entry in the `waypoints` field.  The value is
	// unspecified if the `waypoints` field is empty or the
	// `Vehicle.current_route_segment` field is empty.
	//
	// When updating a vehicle, `remaining_time_seconds` takes precedence over
	// `eta_to_first_waypoint` in the same request.
	EtaToFirstWaypoint *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=eta_to_first_waypoint,json=etaToFirstWaypoint,proto3" json:"eta_to_first_waypoint,omitempty"`
	// Input only. The remaining driving time for the `current_route_segment`. The
	// value is unspecified if the `waypoints` field is empty or the
	// `Vehicle.current_route_segment` field is empty. This value should match
	// `eta_to_first_waypoint` - `current_time` if all parties are using the same
	// clock.
	//
	// When updating a vehicle, `remaining_time_seconds` takes precedence over
	// `eta_to_first_waypoint` in the same request.
	RemainingTimeSeconds *wrapperspb.Int32Value `protobuf:"bytes,25,opt,name=remaining_time_seconds,json=remainingTimeSeconds,proto3" json:"remaining_time_seconds,omitempty"`
	// The remaining waypoints assigned to this Vehicle.
	Waypoints []*TripWaypoint `protobuf:"bytes,22,rep,name=waypoints,proto3" json:"waypoints,omitempty"`
	// Output only. Last time the `waypoints` field was updated. Clients should
	// cache this value and pass it in `GetVehicleRequest` to ensure the
	// `waypoints` field is only returned if it is updated.
	WaypointsVersion *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=waypoints_version,json=waypointsVersion,proto3" json:"waypoints_version,omitempty"`
	// Indicates if the driver accepts back-to-back trips. If `true`,
	// `SearchVehicles` may include the vehicle even if it is currently assigned
	// to a trip. The default value is `false`.
	BackToBackEnabled bool `protobuf:"varint,23,opt,name=back_to_back_enabled,json=backToBackEnabled,proto3" json:"back_to_back_enabled,omitempty"`
	// The vehicle's navigation status.
	NavigationStatus NavigationStatus `protobuf:"varint,26,opt,name=navigation_status,json=navigationStatus,proto3,enum=maps.fleetengine.v1.NavigationStatus" json:"navigation_status,omitempty"`
	// Input only. Information about settings in the mobile device being used by
	// the driver.
	DeviceSettings *DeviceSettings `protobuf:"bytes,27,opt,name=device_settings,json=deviceSettings,proto3" json:"device_settings,omitempty"`
}

func (x *Vehicle) Reset() {
	*x = Vehicle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Vehicle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Vehicle) ProtoMessage() {}

func (x *Vehicle) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Vehicle.ProtoReflect.Descriptor instead.
func (*Vehicle) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicles_proto_rawDescGZIP(), []int{0}
}

func (x *Vehicle) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Vehicle) GetVehicleState() VehicleState {
	if x != nil {
		return x.VehicleState
	}
	return VehicleState_UNKNOWN_VEHICLE_STATE
}

func (x *Vehicle) GetSupportedTripTypes() []TripType {
	if x != nil {
		return x.SupportedTripTypes
	}
	return nil
}

func (x *Vehicle) GetCurrentTrips() []string {
	if x != nil {
		return x.CurrentTrips
	}
	return nil
}

func (x *Vehicle) GetLastLocation() *VehicleLocation {
	if x != nil {
		return x.LastLocation
	}
	return nil
}

func (x *Vehicle) GetMaximumCapacity() int32 {
	if x != nil {
		return x.MaximumCapacity
	}
	return 0
}

func (x *Vehicle) GetAttributes() []*VehicleAttribute {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *Vehicle) GetVehicleType() *Vehicle_VehicleType {
	if x != nil {
		return x.VehicleType
	}
	return nil
}

func (x *Vehicle) GetLicensePlate() *LicensePlate {
	if x != nil {
		return x.LicensePlate
	}
	return nil
}

// Deprecated: Do not use.
func (x *Vehicle) GetRoute() []*TerminalLocation {
	if x != nil {
		return x.Route
	}
	return nil
}

func (x *Vehicle) GetCurrentRouteSegment() string {
	if x != nil {
		return x.CurrentRouteSegment
	}
	return ""
}

func (x *Vehicle) GetCurrentRouteSegmentTraffic() *TrafficPolylineData {
	if x != nil {
		return x.CurrentRouteSegmentTraffic
	}
	return nil
}

func (x *Vehicle) GetCurrentRouteSegmentVersion() *timestamppb.Timestamp {
	if x != nil {
		return x.CurrentRouteSegmentVersion
	}
	return nil
}

func (x *Vehicle) GetCurrentRouteSegmentEndPoint() *TripWaypoint {
	if x != nil {
		return x.CurrentRouteSegmentEndPoint
	}
	return nil
}

func (x *Vehicle) GetRemainingDistanceMeters() *wrapperspb.Int32Value {
	if x != nil {
		return x.RemainingDistanceMeters
	}
	return nil
}

func (x *Vehicle) GetEtaToFirstWaypoint() *timestamppb.Timestamp {
	if x != nil {
		return x.EtaToFirstWaypoint
	}
	return nil
}

func (x *Vehicle) GetRemainingTimeSeconds() *wrapperspb.Int32Value {
	if x != nil {
		return x.RemainingTimeSeconds
	}
	return nil
}

func (x *Vehicle) GetWaypoints() []*TripWaypoint {
	if x != nil {
		return x.Waypoints
	}
	return nil
}

func (x *Vehicle) GetWaypointsVersion() *timestamppb.Timestamp {
	if x != nil {
		return x.WaypointsVersion
	}
	return nil
}

func (x *Vehicle) GetBackToBackEnabled() bool {
	if x != nil {
		return x.BackToBackEnabled
	}
	return false
}

func (x *Vehicle) GetNavigationStatus() NavigationStatus {
	if x != nil {
		return x.NavigationStatus
	}
	return NavigationStatus_UNKNOWN_NAVIGATION_STATUS
}

func (x *Vehicle) GetDeviceSettings() *DeviceSettings {
	if x != nil {
		return x.DeviceSettings
	}
	return nil
}

// Information about the device's battery.
type BatteryInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the battery, whether full or charging etc.
	BatteryStatus BatteryStatus `protobuf:"varint,1,opt,name=battery_status,json=batteryStatus,proto3,enum=maps.fleetengine.v1.BatteryStatus" json:"battery_status,omitempty"`
	// Status of battery power source.
	PowerSource PowerSource `protobuf:"varint,2,opt,name=power_source,json=powerSource,proto3,enum=maps.fleetengine.v1.PowerSource" json:"power_source,omitempty"`
	// Current battery percentage [0-100].
	BatteryPercentage float32 `protobuf:"fixed32,3,opt,name=battery_percentage,json=batteryPercentage,proto3" json:"battery_percentage,omitempty"`
}

func (x *BatteryInfo) Reset() {
	*x = BatteryInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatteryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatteryInfo) ProtoMessage() {}

func (x *BatteryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatteryInfo.ProtoReflect.Descriptor instead.
func (*BatteryInfo) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicles_proto_rawDescGZIP(), []int{1}
}

func (x *BatteryInfo) GetBatteryStatus() BatteryStatus {
	if x != nil {
		return x.BatteryStatus
	}
	return BatteryStatus_UNKNOWN_BATTERY_STATUS
}

func (x *BatteryInfo) GetPowerSource() PowerSource {
	if x != nil {
		return x.PowerSource
	}
	return PowerSource_UNKNOWN_POWER_SOURCE
}

func (x *BatteryInfo) GetBatteryPercentage() float32 {
	if x != nil {
		return x.BatteryPercentage
	}
	return 0
}

// Information about various settings on the mobile device.
type DeviceSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// How location features are set to behave on the device when battery saver is
	// on.
	LocationPowerSaveMode LocationPowerSaveMode `protobuf:"varint,1,opt,name=location_power_save_mode,json=locationPowerSaveMode,proto3,enum=maps.fleetengine.v1.LocationPowerSaveMode" json:"location_power_save_mode,omitempty"`
	// Whether the device is currently in power save mode.
	IsPowerSaveMode bool `protobuf:"varint,2,opt,name=is_power_save_mode,json=isPowerSaveMode,proto3" json:"is_power_save_mode,omitempty"`
	// Whether the device is in an interactive state.
	IsInteractive bool `protobuf:"varint,3,opt,name=is_interactive,json=isInteractive,proto3" json:"is_interactive,omitempty"`
	// Information about the battery state.
	BatteryInfo *BatteryInfo `protobuf:"bytes,4,opt,name=battery_info,json=batteryInfo,proto3" json:"battery_info,omitempty"`
}

func (x *DeviceSettings) Reset() {
	*x = DeviceSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceSettings) ProtoMessage() {}

func (x *DeviceSettings) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceSettings.ProtoReflect.Descriptor instead.
func (*DeviceSettings) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicles_proto_rawDescGZIP(), []int{2}
}

func (x *DeviceSettings) GetLocationPowerSaveMode() LocationPowerSaveMode {
	if x != nil {
		return x.LocationPowerSaveMode
	}
	return LocationPowerSaveMode_UNKNOWN_LOCATION_POWER_SAVE_MODE
}

func (x *DeviceSettings) GetIsPowerSaveMode() bool {
	if x != nil {
		return x.IsPowerSaveMode
	}
	return false
}

func (x *DeviceSettings) GetIsInteractive() bool {
	if x != nil {
		return x.IsInteractive
	}
	return false
}

func (x *DeviceSettings) GetBatteryInfo() *BatteryInfo {
	if x != nil {
		return x.BatteryInfo
	}
	return nil
}

// The license plate information of the Vehicle.  To avoid storing
// personally-identifiable information, only the minimum information
// about the license plate is stored as part of the entity.
type LicensePlate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. CLDR Country/Region Code.  For example, `US` for United States,
	// or `IN` for India.
	CountryCode string `protobuf:"bytes,1,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	// The last digit of the license plate or "-1" to denote no numeric value
	// is present in the license plate.
	//
	// * "ABC 1234" -> "4"
	// * "AB 123 CD" -> "3"
	// * "ABCDEF" -> "-1"
	LastCharacter string `protobuf:"bytes,2,opt,name=last_character,json=lastCharacter,proto3" json:"last_character,omitempty"`
}

func (x *LicensePlate) Reset() {
	*x = LicensePlate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LicensePlate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LicensePlate) ProtoMessage() {}

func (x *LicensePlate) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LicensePlate.ProtoReflect.Descriptor instead.
func (*LicensePlate) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicles_proto_rawDescGZIP(), []int{3}
}

func (x *LicensePlate) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *LicensePlate) GetLastCharacter() string {
	if x != nil {
		return x.LastCharacter
	}
	return ""
}

// Describes how clients should color one portion of the polyline along the
// route.
type VisualTrafficReportPolylineRendering struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Optional. Road stretches that should be rendered along the polyline.
	// Stretches are guaranteed to not overlap, and do not necessarily span the
	// full route.
	//
	// In the absence of a road stretch to style, the client should apply the
	// default for the route.
	RoadStretch []*VisualTrafficReportPolylineRendering_RoadStretch `protobuf:"bytes,1,rep,name=road_stretch,json=roadStretch,proto3" json:"road_stretch,omitempty"`
}

func (x *VisualTrafficReportPolylineRendering) Reset() {
	*x = VisualTrafficReportPolylineRendering{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VisualTrafficReportPolylineRendering) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VisualTrafficReportPolylineRendering) ProtoMessage() {}

func (x *VisualTrafficReportPolylineRendering) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VisualTrafficReportPolylineRendering.ProtoReflect.Descriptor instead.
func (*VisualTrafficReportPolylineRendering) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicles_proto_rawDescGZIP(), []int{4}
}

func (x *VisualTrafficReportPolylineRendering) GetRoadStretch() []*VisualTrafficReportPolylineRendering_RoadStretch {
	if x != nil {
		return x.RoadStretch
	}
	return nil
}

// Traffic conditions along the expected vehicle route.
type TrafficPolylineData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A polyline rendering of how fast traffic is for all regions along
	// one stretch of a customer ride.
	TrafficRendering *VisualTrafficReportPolylineRendering `protobuf:"bytes,1,opt,name=traffic_rendering,json=trafficRendering,proto3" json:"traffic_rendering,omitempty"`
}

func (x *TrafficPolylineData) Reset() {
	*x = TrafficPolylineData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrafficPolylineData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrafficPolylineData) ProtoMessage() {}

func (x *TrafficPolylineData) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrafficPolylineData.ProtoReflect.Descriptor instead.
func (*TrafficPolylineData) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicles_proto_rawDescGZIP(), []int{5}
}

func (x *TrafficPolylineData) GetTrafficRendering() *VisualTrafficReportPolylineRendering {
	if x != nil {
		return x.TrafficRendering
	}
	return nil
}

// The type of vehicle.
type Vehicle_VehicleType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Vehicle type category
	Category Vehicle_VehicleType_Category `protobuf:"varint,1,opt,name=category,proto3,enum=maps.fleetengine.v1.Vehicle_VehicleType_Category" json:"category,omitempty"`
}

func (x *Vehicle_VehicleType) Reset() {
	*x = Vehicle_VehicleType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Vehicle_VehicleType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Vehicle_VehicleType) ProtoMessage() {}

func (x *Vehicle_VehicleType) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Vehicle_VehicleType.ProtoReflect.Descriptor instead.
func (*Vehicle_VehicleType) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicles_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Vehicle_VehicleType) GetCategory() Vehicle_VehicleType_Category {
	if x != nil {
		return x.Category
	}
	return Vehicle_VehicleType_UNKNOWN
}

// One road stretch that should be rendered.
type VisualTrafficReportPolylineRendering_RoadStretch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The style to apply.
	Style VisualTrafficReportPolylineRendering_RoadStretch_Style `protobuf:"varint,1,opt,name=style,proto3,enum=maps.fleetengine.v1.VisualTrafficReportPolylineRendering_RoadStretch_Style" json:"style,omitempty"`
	// Required. The style should be applied between `[offset_meters,
	// offset_meters + length_meters)`.
	OffsetMeters int32 `protobuf:"varint,2,opt,name=offset_meters,json=offsetMeters,proto3" json:"offset_meters,omitempty"`
	// Required. The length of the path where to apply the style.
	LengthMeters int32 `protobuf:"varint,3,opt,name=length_meters,json=lengthMeters,proto3" json:"length_meters,omitempty"`
}

func (x *VisualTrafficReportPolylineRendering_RoadStretch) Reset() {
	*x = VisualTrafficReportPolylineRendering_RoadStretch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VisualTrafficReportPolylineRendering_RoadStretch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VisualTrafficReportPolylineRendering_RoadStretch) ProtoMessage() {}

func (x *VisualTrafficReportPolylineRendering_RoadStretch) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VisualTrafficReportPolylineRendering_RoadStretch.ProtoReflect.Descriptor instead.
func (*VisualTrafficReportPolylineRendering_RoadStretch) Descriptor() ([]byte, []int) {
	return file_google_maps_fleetengine_v1_vehicles_proto_rawDescGZIP(), []int{4, 0}
}

func (x *VisualTrafficReportPolylineRendering_RoadStretch) GetStyle() VisualTrafficReportPolylineRendering_RoadStretch_Style {
	if x != nil {
		return x.Style
	}
	return VisualTrafficReportPolylineRendering_RoadStretch_STYLE_UNSPECIFIED
}

func (x *VisualTrafficReportPolylineRendering_RoadStretch) GetOffsetMeters() int32 {
	if x != nil {
		return x.OffsetMeters
	}
	return 0
}

func (x *VisualTrafficReportPolylineRendering_RoadStretch) GetLengthMeters() int32 {
	if x != nil {
		return x.LengthMeters
	}
	return 0
}

var File_google_maps_fleetengine_v1_vehicles_proto protoreflect.FileDescriptor

var file_google_maps_fleetengine_v1_vehicles_proto_rawDesc = []byte{
	0x0a, 0x29, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x6d, 0x61, 0x70,
	0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61,
	0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcc, 0x0e, 0x0a, 0x07,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x46, 0x0a, 0x0d, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66,
	0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x76, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x4f, 0x0a, 0x14, 0x73, 0x75, 0x70, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x69, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x69,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64,
	0x54, 0x72, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x28, 0x0a, 0x0d, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x69, 0x70, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09,
	0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x72,
	0x69, 0x70, 0x73, 0x12, 0x49, 0x0a, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x61, 0x70,
	0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0c, 0x6c, 0x61, 0x73, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29,
	0x0a, 0x10, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75, 0x6d, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69,
	0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6d, 0x61, 0x78, 0x69, 0x6d, 0x75,
	0x6d, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x45, 0x0a, 0x0a, 0x61, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x65, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73,
	0x12, 0x4b, 0x0a, 0x0c, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0b, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x46, 0x0a,
	0x0d, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65,
	0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x63, 0x65, 0x6e,
	0x73, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65,
	0x50, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x3f, 0x0a, 0x05, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x18, 0x0c,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65,
	0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x05, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x6f,
	0x75, 0x74, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x70, 0x0a, 0x1d, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x18, 0x1c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x50,
	0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61, 0x74, 0x61, 0x42, 0x03, 0xe0, 0x41, 0x04,
	0x52, 0x1a, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x53, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x12, 0x62, 0x0a, 0x1d,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42,
	0x03, 0xe0, 0x41, 0x03, 0x52, 0x1a, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x6f, 0x75,
	0x74, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x67, 0x0a, 0x1f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x6f, 0x75, 0x74,
	0x65, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x61, 0x70, 0x73,
	0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x72, 0x69, 0x70, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x1b, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x45, 0x6e, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x57, 0x0a, 0x19, 0x72, 0x65, 0x6d,
	0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49,
	0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x17, 0x72, 0x65, 0x6d, 0x61, 0x69,
	0x6e, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x12, 0x4d, 0x0a, 0x15, 0x65, 0x74, 0x61, 0x5f, 0x74, 0x6f, 0x5f, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x5f, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x12, 0x65,
	0x74, 0x61, 0x54, 0x6f, 0x46, 0x69, 0x72, 0x73, 0x74, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x12, 0x56, 0x0a, 0x16, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x03,
	0xe0, 0x41, 0x04, 0x52, 0x14, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x54, 0x69,
	0x6d, 0x65, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x3f, 0x0a, 0x09, 0x77, 0x61, 0x79,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d,
	0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x72, 0x69, 0x70, 0x57, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52,
	0x09, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x4c, 0x0a, 0x11, 0x77, 0x61,
	0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x10, 0x77, 0x61, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x14, 0x62, 0x61, 0x63, 0x6b,
	0x5f, 0x74, 0x6f, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x6f, 0x42, 0x61,
	0x63, 0x6b, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x52, 0x0a, 0x11, 0x6e, 0x61, 0x76,
	0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1a,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65,
	0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x61, 0x76, 0x69, 0x67,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x6e, 0x61, 0x76,
	0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x51, 0x0a,
	0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c,
	0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x42, 0x03, 0xe0, 0x41, 0x04,
	0x52, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x1a, 0xa5, 0x01, 0x0a, 0x0b, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x4d, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x2e, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22,
	0x47, 0x0a, 0x08, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x0b, 0x0a, 0x07, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x41, 0x55, 0x54, 0x4f,
	0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x41, 0x58, 0x49, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05,
	0x54, 0x52, 0x55, 0x43, 0x4b, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x57, 0x4f, 0x5f, 0x57,
	0x48, 0x45, 0x45, 0x4c, 0x45, 0x52, 0x10, 0x04, 0x3a, 0x50, 0xea, 0x41, 0x4d, 0x0a, 0x22, 0x66,
	0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x12, 0x27, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x7d, 0x2f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x73,
	0x2f, 0x7b, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x7d, 0x22, 0xcc, 0x01, 0x0a, 0x0b, 0x42,
	0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x49, 0x0a, 0x0e, 0x62, 0x61,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x43, 0x0a, 0x0c, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x6d, 0x61,
	0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0b, 0x70,
	0x6f, 0x77, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x62, 0x61,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x22, 0x8e, 0x02, 0x0a, 0x0e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x63, 0x0a, 0x18,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x73,
	0x61, 0x76, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a,
	0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x77,
	0x65, 0x72, 0x53, 0x61, 0x76, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x15, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x53, 0x61, 0x76, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x12, 0x2b, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x73, 0x61,
	0x76, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69,
	0x73, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x53, 0x61, 0x76, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x43, 0x0a, 0x0c, 0x62, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x61,
	0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x62,
	0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x5d, 0x0a, 0x0c, 0x4c, 0x69,
	0x63, 0x65, 0x6e, 0x73, 0x65, 0x50, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x26, 0x0a, 0x0c, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x61,
	0x63, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74,
	0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x22, 0xa6, 0x03, 0x0a, 0x24, 0x56, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x50, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x12, 0x6d, 0x0a, 0x0c, 0x72, 0x6f, 0x61, 0x64, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x74,
	0x63, 0x68, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e,
	0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x50, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x52, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x72, 0x65, 0x74, 0x63, 0x68, 0x42,
	0x03, 0xe0, 0x41, 0x01, 0x52, 0x0b, 0x72, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x72, 0x65, 0x74, 0x63,
	0x68, 0x1a, 0x8e, 0x02, 0x0a, 0x0b, 0x52, 0x6f, 0x61, 0x64, 0x53, 0x74, 0x72, 0x65, 0x74, 0x63,
	0x68, 0x12, 0x66, 0x0a, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x4b, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x54, 0x72, 0x61,
	0x66, 0x66, 0x69, 0x63, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x6f, 0x6c, 0x79, 0x6c, 0x69,
	0x6e, 0x65, 0x52, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x52, 0x6f, 0x61, 0x64,
	0x53, 0x74, 0x72, 0x65, 0x74, 0x63, 0x68, 0x2e, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x05, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x28, 0x0a, 0x0d, 0x6f, 0x66, 0x66,
	0x73, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0c, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x4d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x12, 0x28, 0x0a, 0x0d, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x5f, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x0c, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x4d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x22, 0x43, 0x0a,
	0x05, 0x53, 0x74, 0x79, 0x6c, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a,
	0x0e, 0x53, 0x4c, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x54, 0x52, 0x41, 0x46, 0x46, 0x49, 0x43, 0x10,
	0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x52, 0x41, 0x46, 0x46, 0x49, 0x43, 0x5f, 0x4a, 0x41, 0x4d,
	0x10, 0x02, 0x22, 0x7d, 0x0a, 0x13, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x50, 0x6f, 0x6c,
	0x79, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x66, 0x0a, 0x11, 0x74, 0x72, 0x61,
	0x66, 0x66, 0x69, 0x63, 0x5f, 0x72, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65,
	0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x54, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x6f,
	0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x52,
	0x10, 0x74, 0x72, 0x61, 0x66, 0x66, 0x69, 0x63, 0x52, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2a, 0x42, 0x0a, 0x0c, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x19, 0x0a, 0x15, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x56, 0x45, 0x48,
	0x49, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07,
	0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x4f, 0x4e, 0x4c,
	0x49, 0x4e, 0x45, 0x10, 0x02, 0x2a, 0x92, 0x02, 0x0a, 0x15, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x53, 0x61, 0x76, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12,
	0x24, 0x0a, 0x20, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x41, 0x56, 0x45, 0x5f, 0x4d,
	0x4f, 0x44, 0x45, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x4e, 0x4f, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45,
	0x10, 0x01, 0x12, 0x2e, 0x0a, 0x2a, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d,
	0x4f, 0x44, 0x45, 0x5f, 0x47, 0x50, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x44,
	0x5f, 0x57, 0x48, 0x45, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x4f, 0x46, 0x46,
	0x10, 0x02, 0x12, 0x2e, 0x0a, 0x2a, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d,
	0x4f, 0x44, 0x45, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x44,
	0x5f, 0x57, 0x48, 0x45, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x4f, 0x46, 0x46,
	0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d,
	0x4f, 0x44, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x47, 0x52, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x4f,
	0x4e, 0x4c, 0x59, 0x10, 0x04, 0x12, 0x33, 0x0a, 0x2f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x54, 0x48, 0x52, 0x4f, 0x54, 0x54, 0x4c, 0x45, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x53, 0x5f, 0x57, 0x48, 0x45, 0x4e, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x10, 0x05, 0x2a, 0xc0, 0x01, 0x0a, 0x0d, 0x42,
	0x61, 0x74, 0x74, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x16,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x42, 0x41, 0x54, 0x54, 0x45, 0x52, 0x59, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x42, 0x41, 0x54, 0x54,
	0x45, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47,
	0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x42, 0x41, 0x54, 0x54, 0x45, 0x52, 0x59,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x48, 0x41, 0x52, 0x47,
	0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x42, 0x41, 0x54, 0x54, 0x45, 0x52, 0x59,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x10, 0x03, 0x12, 0x1f,
	0x0a, 0x1b, 0x42, 0x41, 0x54, 0x54, 0x45, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12,
	0x1c, 0x0a, 0x18, 0x42, 0x41, 0x54, 0x54, 0x45, 0x52, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x4c, 0x4f, 0x57, 0x10, 0x05, 0x2a, 0x89, 0x01,
	0x0a, 0x0b, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x18, 0x0a,
	0x14, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53,
	0x4f, 0x55, 0x52, 0x43, 0x45, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x4f, 0x57, 0x45, 0x52,
	0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x41, 0x43, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10,
	0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x53, 0x42,
	0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x4f, 0x55, 0x52,
	0x43, 0x45, 0x5f, 0x57, 0x49, 0x52, 0x45, 0x4c, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x1a, 0x0a,
	0x16, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e,
	0x50, 0x4c, 0x55, 0x47, 0x47, 0x45, 0x44, 0x10, 0x04, 0x42, 0x75, 0x0a, 0x1a, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x2e, 0x76, 0x31, 0x42, 0x08, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x73, 0x50, 0x01, 0x5a, 0x45, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61,
	0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f,
	0x66, 0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x66,
	0x6c, 0x65, 0x65, 0x74, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0xa2, 0x02, 0x03, 0x43, 0x46, 0x45,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_maps_fleetengine_v1_vehicles_proto_rawDescOnce sync.Once
	file_google_maps_fleetengine_v1_vehicles_proto_rawDescData = file_google_maps_fleetengine_v1_vehicles_proto_rawDesc
)

func file_google_maps_fleetengine_v1_vehicles_proto_rawDescGZIP() []byte {
	file_google_maps_fleetengine_v1_vehicles_proto_rawDescOnce.Do(func() {
		file_google_maps_fleetengine_v1_vehicles_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_maps_fleetengine_v1_vehicles_proto_rawDescData)
	})
	return file_google_maps_fleetengine_v1_vehicles_proto_rawDescData
}

var file_google_maps_fleetengine_v1_vehicles_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_google_maps_fleetengine_v1_vehicles_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_google_maps_fleetengine_v1_vehicles_proto_goTypes = []interface{}{
	(VehicleState)(0),                 // 0: maps.fleetengine.v1.VehicleState
	(LocationPowerSaveMode)(0),        // 1: maps.fleetengine.v1.LocationPowerSaveMode
	(BatteryStatus)(0),                // 2: maps.fleetengine.v1.BatteryStatus
	(PowerSource)(0),                  // 3: maps.fleetengine.v1.PowerSource
	(Vehicle_VehicleType_Category)(0), // 4: maps.fleetengine.v1.Vehicle.VehicleType.Category
	(VisualTrafficReportPolylineRendering_RoadStretch_Style)(0), // 5: maps.fleetengine.v1.VisualTrafficReportPolylineRendering.RoadStretch.Style
	(*Vehicle)(nil),                                          // 6: maps.fleetengine.v1.Vehicle
	(*BatteryInfo)(nil),                                      // 7: maps.fleetengine.v1.BatteryInfo
	(*DeviceSettings)(nil),                                   // 8: maps.fleetengine.v1.DeviceSettings
	(*LicensePlate)(nil),                                     // 9: maps.fleetengine.v1.LicensePlate
	(*VisualTrafficReportPolylineRendering)(nil),             // 10: maps.fleetengine.v1.VisualTrafficReportPolylineRendering
	(*TrafficPolylineData)(nil),                              // 11: maps.fleetengine.v1.TrafficPolylineData
	(*Vehicle_VehicleType)(nil),                              // 12: maps.fleetengine.v1.Vehicle.VehicleType
	(*VisualTrafficReportPolylineRendering_RoadStretch)(nil), // 13: maps.fleetengine.v1.VisualTrafficReportPolylineRendering.RoadStretch
	(TripType)(0),                                            // 14: maps.fleetengine.v1.TripType
	(*VehicleLocation)(nil),                                  // 15: maps.fleetengine.v1.VehicleLocation
	(*VehicleAttribute)(nil),                                 // 16: maps.fleetengine.v1.VehicleAttribute
	(*TerminalLocation)(nil),                                 // 17: maps.fleetengine.v1.TerminalLocation
	(*timestamppb.Timestamp)(nil),                            // 18: google.protobuf.Timestamp
	(*TripWaypoint)(nil),                                     // 19: maps.fleetengine.v1.TripWaypoint
	(*wrapperspb.Int32Value)(nil),                            // 20: google.protobuf.Int32Value
	(NavigationStatus)(0),                                    // 21: maps.fleetengine.v1.NavigationStatus
}
var file_google_maps_fleetengine_v1_vehicles_proto_depIdxs = []int32{
	0,  // 0: maps.fleetengine.v1.Vehicle.vehicle_state:type_name -> maps.fleetengine.v1.VehicleState
	14, // 1: maps.fleetengine.v1.Vehicle.supported_trip_types:type_name -> maps.fleetengine.v1.TripType
	15, // 2: maps.fleetengine.v1.Vehicle.last_location:type_name -> maps.fleetengine.v1.VehicleLocation
	16, // 3: maps.fleetengine.v1.Vehicle.attributes:type_name -> maps.fleetengine.v1.VehicleAttribute
	12, // 4: maps.fleetengine.v1.Vehicle.vehicle_type:type_name -> maps.fleetengine.v1.Vehicle.VehicleType
	9,  // 5: maps.fleetengine.v1.Vehicle.license_plate:type_name -> maps.fleetengine.v1.LicensePlate
	17, // 6: maps.fleetengine.v1.Vehicle.route:type_name -> maps.fleetengine.v1.TerminalLocation
	11, // 7: maps.fleetengine.v1.Vehicle.current_route_segment_traffic:type_name -> maps.fleetengine.v1.TrafficPolylineData
	18, // 8: maps.fleetengine.v1.Vehicle.current_route_segment_version:type_name -> google.protobuf.Timestamp
	19, // 9: maps.fleetengine.v1.Vehicle.current_route_segment_end_point:type_name -> maps.fleetengine.v1.TripWaypoint
	20, // 10: maps.fleetengine.v1.Vehicle.remaining_distance_meters:type_name -> google.protobuf.Int32Value
	18, // 11: maps.fleetengine.v1.Vehicle.eta_to_first_waypoint:type_name -> google.protobuf.Timestamp
	20, // 12: maps.fleetengine.v1.Vehicle.remaining_time_seconds:type_name -> google.protobuf.Int32Value
	19, // 13: maps.fleetengine.v1.Vehicle.waypoints:type_name -> maps.fleetengine.v1.TripWaypoint
	18, // 14: maps.fleetengine.v1.Vehicle.waypoints_version:type_name -> google.protobuf.Timestamp
	21, // 15: maps.fleetengine.v1.Vehicle.navigation_status:type_name -> maps.fleetengine.v1.NavigationStatus
	8,  // 16: maps.fleetengine.v1.Vehicle.device_settings:type_name -> maps.fleetengine.v1.DeviceSettings
	2,  // 17: maps.fleetengine.v1.BatteryInfo.battery_status:type_name -> maps.fleetengine.v1.BatteryStatus
	3,  // 18: maps.fleetengine.v1.BatteryInfo.power_source:type_name -> maps.fleetengine.v1.PowerSource
	1,  // 19: maps.fleetengine.v1.DeviceSettings.location_power_save_mode:type_name -> maps.fleetengine.v1.LocationPowerSaveMode
	7,  // 20: maps.fleetengine.v1.DeviceSettings.battery_info:type_name -> maps.fleetengine.v1.BatteryInfo
	13, // 21: maps.fleetengine.v1.VisualTrafficReportPolylineRendering.road_stretch:type_name -> maps.fleetengine.v1.VisualTrafficReportPolylineRendering.RoadStretch
	10, // 22: maps.fleetengine.v1.TrafficPolylineData.traffic_rendering:type_name -> maps.fleetengine.v1.VisualTrafficReportPolylineRendering
	4,  // 23: maps.fleetengine.v1.Vehicle.VehicleType.category:type_name -> maps.fleetengine.v1.Vehicle.VehicleType.Category
	5,  // 24: maps.fleetengine.v1.VisualTrafficReportPolylineRendering.RoadStretch.style:type_name -> maps.fleetengine.v1.VisualTrafficReportPolylineRendering.RoadStretch.Style
	25, // [25:25] is the sub-list for method output_type
	25, // [25:25] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_google_maps_fleetengine_v1_vehicles_proto_init() }
func file_google_maps_fleetengine_v1_vehicles_proto_init() {
	if File_google_maps_fleetengine_v1_vehicles_proto != nil {
		return
	}
	file_google_maps_fleetengine_v1_fleetengine_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Vehicle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatteryInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LicensePlate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VisualTrafficReportPolylineRendering); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrafficPolylineData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Vehicle_VehicleType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_fleetengine_v1_vehicles_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VisualTrafficReportPolylineRendering_RoadStretch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_maps_fleetengine_v1_vehicles_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_maps_fleetengine_v1_vehicles_proto_goTypes,
		DependencyIndexes: file_google_maps_fleetengine_v1_vehicles_proto_depIdxs,
		EnumInfos:         file_google_maps_fleetengine_v1_vehicles_proto_enumTypes,
		MessageInfos:      file_google_maps_fleetengine_v1_vehicles_proto_msgTypes,
	}.Build()
	File_google_maps_fleetengine_v1_vehicles_proto = out.File
	file_google_maps_fleetengine_v1_vehicles_proto_rawDesc = nil
	file_google_maps_fleetengine_v1_vehicles_proto_goTypes = nil
	file_google_maps_fleetengine_v1_vehicles_proto_depIdxs = nil
}
