package com.fuck.fuckinggooo.core

import android.util.Log
import java.net.ServerSocket
import java.net.Socket
import java.util.concurrent.Executors

object EnhancedFallbackProxy {
    private const val TAG = "EnhancedFallbackProxy"
    private var socksServer: ServerSocket? = null
    private var isRunning = false
    private val executor = Executors.newCachedThreadPool()

    fun start(proxyConfig: Map<String, Any>): Boolean {
        return try {
            if (isRunning) {
                Log.w(TAG, "Fallback proxy already running")
                return true
            }

            socksServer = ServerSocket(1080)
            isRunning = true

            executor.submit {
                while (isRunning && socksServer?.isClosed == false) {
                    try {
                        val clientSocket = socksServer?.accept()
                        if (clientSocket != null) {
                            executor.submit {
                                handleSocksConnection(clientSocket, proxyConfig)
                            }
                        }
                    } catch (e: Exception) {
                        if (isRunning) {
                            Log.e(TAG, "Error accepting connection", e)
                        }
                    }
                }
            }

            Log.d(TAG, "Enhanced fallback proxy started on port 1080")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start fallback proxy", e)
            false
        }
    }

    fun stop(): Boolean {
        return try {
            isRunning = false
            socksServer?.close()
            socksServer = null
            executor.shutdown()
            Log.d(TAG, "Enhanced fallback proxy stopped")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop fallback proxy", e)
            false
        }
    }

    fun isRunning(): Boolean = isRunning

    private fun handleSocksConnection(clientSocket: Socket, proxyConfig: Map<String, Any>) {
        try {
            val input = clientSocket.getInputStream()
            val output = clientSocket.getOutputStream()
            
            // 实现SOCKS5握手
            if (performSocks5Handshake(input, output)) {
                // 建立到实际代理服务器的连接
                val proxySocket = connectToProxy(proxyConfig)
                if (proxySocket != null) {
                    // 转发数据
                    forwardData(clientSocket, proxySocket)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling SOCKS connection", e)
        } finally {
            try {
                clientSocket.close()
            } catch (e: Exception) {
                // Ignore
            }
        }
    }

    private fun performSocks5Handshake(input: java.io.InputStream, output: java.io.OutputStream): Boolean {
        return try {
            // 简化的SOCKS5握手实现
            val buffer = ByteArray(1024)
            input.read(buffer)

            // 发送认证方法选择响应
            output.write(byteArrayOf(0x05, 0x00))
            output.flush()

            // 读取连接请求
            input.read(buffer)

            // 发送连接成功响应
            output.write(byteArrayOf(0x05, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00))
            output.flush()

            true
        } catch (e: Exception) {
            Log.e(TAG, "SOCKS5 handshake failed", e)
            false
        }
    }

    private fun connectToProxy(proxyConfig: Map<String, Any>): Socket? {
        return try {
            val host = proxyConfig["host"] as? String ?: "127.0.0.1"
            val port = proxyConfig["port"] as? Int ?: 1080
            Socket(host, port)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to connect to proxy", e)
            null
        }
    }

    private fun forwardData(clientSocket: Socket, proxySocket: Socket) {
        try {
            val clientToProxy = Thread {
                try {
                    clientSocket.getInputStream().copyTo(proxySocket.getOutputStream())
                } catch (e: Exception) {
                    Log.d(TAG, "Client to proxy forwarding ended")
                }
            }

            val proxyToClient = Thread {
                try {
                    proxySocket.getInputStream().copyTo(clientSocket.getOutputStream())
                } catch (e: Exception) {
                    Log.d(TAG, "Proxy to client forwarding ended")
                }
            }

            clientToProxy.start()
            proxyToClient.start()

            clientToProxy.join()
            proxyToClient.join()
        } catch (e: Exception) {
            Log.e(TAG, "Data forwarding failed", e)
        } finally {
            try {
                proxySocket.close()
            } catch (e: Exception) {
                // Ignore
            }
        }
    }
}