// Copyright 2019 Google LLC.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.12.2
// source: google/cloud/websecurityscanner/v1alpha/finding.proto

package websecurityscanner

import (
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Types of Findings.
type Finding_FindingType int32

const (
	// The invalid finding type.
	Finding_FINDING_TYPE_UNSPECIFIED Finding_FindingType = 0
	// A page that was served over HTTPS also resources over HTTP. A
	// man-in-the-middle attacker could tamper with the HTTP resource and gain
	// full access to the website that loads the resource or to monitor the
	// actions taken by the user.
	Finding_MIXED_CONTENT Finding_FindingType = 1
	// The version of an included library is known to contain a security issue.
	// The scanner checks the version of library in use against a known list of
	// vulnerable libraries. False positives are possible if the version
	// detection fails or if the library has been manually patched.
	Finding_OUTDATED_LIBRARY Finding_FindingType = 2
	// This type of vulnerability occurs when the value of a request parameter
	// is reflected at the beginning of the response, for example, in requests
	// using JSONP. Under certain circumstances, an attacker may be able to
	// supply an alphanumeric-only Flash file in the vulnerable parameter
	// causing the browser to execute the Flash file as if it originated on the
	// vulnerable server.
	Finding_ROSETTA_FLASH Finding_FindingType = 5
	// A cross-site scripting (XSS) bug is found via JavaScript callback. For
	// detailed explanations on XSS, see
	// https://www.google.com/about/appsecurity/learning/xss/.
	Finding_XSS_CALLBACK Finding_FindingType = 3
	// A potential cross-site scripting (XSS) bug due to JavaScript breakage.
	// In some circumstances, the application under test might modify the test
	// string before it is parsed by the browser. When the browser attempts to
	// runs this modified test string, it will likely break and throw a
	// JavaScript execution error, thus an injection issue is occurring.
	// However, it may not be exploitable. Manual verification is needed to see
	// if the test string modifications can be evaded and confirm that the issue
	// is in fact an XSS vulnerability. For detailed explanations on XSS, see
	// https://www.google.com/about/appsecurity/learning/xss/.
	Finding_XSS_ERROR Finding_FindingType = 4
	// An application appears to be transmitting a password field in clear text.
	// An attacker can eavesdrop network traffic and sniff the password field.
	Finding_CLEAR_TEXT_PASSWORD Finding_FindingType = 6
	// An application returns sensitive content with an invalid content type,
	// or without an 'X-Content-Type-Options: nosniff' header.
	Finding_INVALID_CONTENT_TYPE Finding_FindingType = 7
	// A cross-site scripting (XSS) vulnerability in AngularJS module that
	// occurs when a user-provided string is interpolated by Angular.
	Finding_XSS_ANGULAR_CALLBACK Finding_FindingType = 8
	// A malformed or invalid valued header.
	Finding_INVALID_HEADER Finding_FindingType = 9
	// Misspelled security header name.
	Finding_MISSPELLED_SECURITY_HEADER_NAME Finding_FindingType = 10
	// Mismatching values in a duplicate security header.
	Finding_MISMATCHING_SECURITY_HEADER_VALUES Finding_FindingType = 11
)

// Enum value maps for Finding_FindingType.
var (
	Finding_FindingType_name = map[int32]string{
		0:  "FINDING_TYPE_UNSPECIFIED",
		1:  "MIXED_CONTENT",
		2:  "OUTDATED_LIBRARY",
		5:  "ROSETTA_FLASH",
		3:  "XSS_CALLBACK",
		4:  "XSS_ERROR",
		6:  "CLEAR_TEXT_PASSWORD",
		7:  "INVALID_CONTENT_TYPE",
		8:  "XSS_ANGULAR_CALLBACK",
		9:  "INVALID_HEADER",
		10: "MISSPELLED_SECURITY_HEADER_NAME",
		11: "MISMATCHING_SECURITY_HEADER_VALUES",
	}
	Finding_FindingType_value = map[string]int32{
		"FINDING_TYPE_UNSPECIFIED":           0,
		"MIXED_CONTENT":                      1,
		"OUTDATED_LIBRARY":                   2,
		"ROSETTA_FLASH":                      5,
		"XSS_CALLBACK":                       3,
		"XSS_ERROR":                          4,
		"CLEAR_TEXT_PASSWORD":                6,
		"INVALID_CONTENT_TYPE":               7,
		"XSS_ANGULAR_CALLBACK":               8,
		"INVALID_HEADER":                     9,
		"MISSPELLED_SECURITY_HEADER_NAME":    10,
		"MISMATCHING_SECURITY_HEADER_VALUES": 11,
	}
)

func (x Finding_FindingType) Enum() *Finding_FindingType {
	p := new(Finding_FindingType)
	*p = x
	return p
}

func (x Finding_FindingType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Finding_FindingType) Descriptor() protoreflect.EnumDescriptor {
	return file_google_cloud_websecurityscanner_v1alpha_finding_proto_enumTypes[0].Descriptor()
}

func (Finding_FindingType) Type() protoreflect.EnumType {
	return &file_google_cloud_websecurityscanner_v1alpha_finding_proto_enumTypes[0]
}

func (x Finding_FindingType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Finding_FindingType.Descriptor instead.
func (Finding_FindingType) EnumDescriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1alpha_finding_proto_rawDescGZIP(), []int{0, 0}
}

// A Finding resource represents a vulnerability instance identified during a
// ScanRun.
type Finding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The resource name of the Finding. The name follows the format of
	// 'projects/{projectId}/scanConfigs/{scanConfigId}/scanruns/{scanRunId}/findings/{findingId}'.
	// The finding IDs are generated by the system.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The type of the Finding.
	FindingType Finding_FindingType `protobuf:"varint,2,opt,name=finding_type,json=findingType,proto3,enum=google.cloud.websecurityscanner.v1alpha.Finding_FindingType" json:"finding_type,omitempty"`
	// The http method of the request that triggered the vulnerability, in
	// uppercase.
	HttpMethod string `protobuf:"bytes,3,opt,name=http_method,json=httpMethod,proto3" json:"http_method,omitempty"`
	// The URL produced by the server-side fuzzer and used in the request that
	// triggered the vulnerability.
	FuzzedUrl string `protobuf:"bytes,4,opt,name=fuzzed_url,json=fuzzedUrl,proto3" json:"fuzzed_url,omitempty"`
	// The body of the request that triggered the vulnerability.
	Body string `protobuf:"bytes,5,opt,name=body,proto3" json:"body,omitempty"`
	// The description of the vulnerability.
	Description string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	// The URL containing human-readable payload that user can leverage to
	// reproduce the vulnerability.
	ReproductionUrl string `protobuf:"bytes,7,opt,name=reproduction_url,json=reproductionUrl,proto3" json:"reproduction_url,omitempty"`
	// If the vulnerability was originated from nested IFrame, the immediate
	// parent IFrame is reported.
	FrameUrl string `protobuf:"bytes,8,opt,name=frame_url,json=frameUrl,proto3" json:"frame_url,omitempty"`
	// The URL where the browser lands when the vulnerability is detected.
	FinalUrl string `protobuf:"bytes,9,opt,name=final_url,json=finalUrl,proto3" json:"final_url,omitempty"`
	// The tracking ID uniquely identifies a vulnerability instance across
	// multiple ScanRuns.
	TrackingId string `protobuf:"bytes,10,opt,name=tracking_id,json=trackingId,proto3" json:"tracking_id,omitempty"`
	// An addon containing information about outdated libraries.
	OutdatedLibrary *OutdatedLibrary `protobuf:"bytes,11,opt,name=outdated_library,json=outdatedLibrary,proto3" json:"outdated_library,omitempty"`
	// An addon containing detailed information regarding any resource causing the
	// vulnerability such as JavaScript sources, image, audio files, etc.
	ViolatingResource *ViolatingResource `protobuf:"bytes,12,opt,name=violating_resource,json=violatingResource,proto3" json:"violating_resource,omitempty"`
	// An addon containing information about vulnerable or missing HTTP headers.
	VulnerableHeaders *VulnerableHeaders `protobuf:"bytes,15,opt,name=vulnerable_headers,json=vulnerableHeaders,proto3" json:"vulnerable_headers,omitempty"`
	// An addon containing information about request parameters which were found
	// to be vulnerable.
	VulnerableParameters *VulnerableParameters `protobuf:"bytes,13,opt,name=vulnerable_parameters,json=vulnerableParameters,proto3" json:"vulnerable_parameters,omitempty"`
	// An addon containing information reported for an XSS, if any.
	Xss *Xss `protobuf:"bytes,14,opt,name=xss,proto3" json:"xss,omitempty"`
}

func (x *Finding) Reset() {
	*x = Finding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_websecurityscanner_v1alpha_finding_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Finding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Finding) ProtoMessage() {}

func (x *Finding) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_websecurityscanner_v1alpha_finding_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Finding.ProtoReflect.Descriptor instead.
func (*Finding) Descriptor() ([]byte, []int) {
	return file_google_cloud_websecurityscanner_v1alpha_finding_proto_rawDescGZIP(), []int{0}
}

func (x *Finding) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Finding) GetFindingType() Finding_FindingType {
	if x != nil {
		return x.FindingType
	}
	return Finding_FINDING_TYPE_UNSPECIFIED
}

func (x *Finding) GetHttpMethod() string {
	if x != nil {
		return x.HttpMethod
	}
	return ""
}

func (x *Finding) GetFuzzedUrl() string {
	if x != nil {
		return x.FuzzedUrl
	}
	return ""
}

func (x *Finding) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *Finding) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Finding) GetReproductionUrl() string {
	if x != nil {
		return x.ReproductionUrl
	}
	return ""
}

func (x *Finding) GetFrameUrl() string {
	if x != nil {
		return x.FrameUrl
	}
	return ""
}

func (x *Finding) GetFinalUrl() string {
	if x != nil {
		return x.FinalUrl
	}
	return ""
}

func (x *Finding) GetTrackingId() string {
	if x != nil {
		return x.TrackingId
	}
	return ""
}

func (x *Finding) GetOutdatedLibrary() *OutdatedLibrary {
	if x != nil {
		return x.OutdatedLibrary
	}
	return nil
}

func (x *Finding) GetViolatingResource() *ViolatingResource {
	if x != nil {
		return x.ViolatingResource
	}
	return nil
}

func (x *Finding) GetVulnerableHeaders() *VulnerableHeaders {
	if x != nil {
		return x.VulnerableHeaders
	}
	return nil
}

func (x *Finding) GetVulnerableParameters() *VulnerableParameters {
	if x != nil {
		return x.VulnerableParameters
	}
	return nil
}

func (x *Finding) GetXss() *Xss {
	if x != nil {
		return x.Xss
	}
	return nil
}

var File_google_cloud_websecurityscanner_v1alpha_finding_proto protoreflect.FileDescriptor

var file_google_cloud_websecurityscanner_v1alpha_finding_proto_rawDesc = []byte{
	0x0a, 0x35, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77,
	0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2f, 0x66, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x27, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x61,
	0x6c, 0x70, 0x68, 0x61, 0x2f, 0x66, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa9, 0x0a, 0x0a, 0x07, 0x46, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x5f, 0x0a, 0x0c, 0x66, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65,
	0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x46, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x66, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x68, 0x74, 0x74,
	0x70, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x68, 0x74, 0x74, 0x70, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x75,
	0x7a, 0x7a, 0x65, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x66, 0x75, 0x7a, 0x7a, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x64,
	0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x6f, 0x64, 0x79, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x29, 0x0a, 0x10, 0x72, 0x65, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x72,
	0x61, 0x6d, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66,
	0x72, 0x61, 0x6d, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6e, 0x61, 0x6c,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6e, 0x61,
	0x6c, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x63, 0x0a, 0x10, 0x6f, 0x75, 0x74, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x38, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77,
	0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x4f, 0x75, 0x74, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x52, 0x0f, 0x6f, 0x75, 0x74, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x12, 0x69, 0x0a, 0x12, 0x76, 0x69,
	0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x2e, 0x56, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x52, 0x11, 0x76, 0x69, 0x6f, 0x6c, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x69, 0x0a, 0x12, 0x76, 0x75, 0x6c, 0x6e, 0x65, 0x72, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x56, 0x75, 0x6c, 0x6e,
	0x65, 0x72, 0x61, 0x62, 0x6c, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x11, 0x76,
	0x75, 0x6c, 0x6e, 0x65, 0x72, 0x61, 0x62, 0x6c, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x12, 0x72, 0x0a, 0x15, 0x76, 0x75, 0x6c, 0x6e, 0x65, 0x72, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x3d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77,
	0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x56, 0x75, 0x6c, 0x6e, 0x65, 0x72,
	0x61, 0x62, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x14,
	0x76, 0x75, 0x6c, 0x6e, 0x65, 0x72, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x12, 0x3e, 0x0a, 0x03, 0x78, 0x73, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x2e, 0x58, 0x73, 0x73, 0x52,
	0x03, 0x78, 0x73, 0x73, 0x22, 0xb6, 0x02, 0x0a, 0x0b, 0x46, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x46, 0x49, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x54,
	0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x4f, 0x55, 0x54, 0x44, 0x41, 0x54, 0x45,
	0x44, 0x5f, 0x4c, 0x49, 0x42, 0x52, 0x41, 0x52, 0x59, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x52,
	0x4f, 0x53, 0x45, 0x54, 0x54, 0x41, 0x5f, 0x46, 0x4c, 0x41, 0x53, 0x48, 0x10, 0x05, 0x12, 0x10,
	0x0a, 0x0c, 0x58, 0x53, 0x53, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x03,
	0x12, 0x0d, 0x0a, 0x09, 0x58, 0x53, 0x53, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x04, 0x12,
	0x17, 0x0a, 0x13, 0x43, 0x4c, 0x45, 0x41, 0x52, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x50, 0x41,
	0x53, 0x53, 0x57, 0x4f, 0x52, 0x44, 0x10, 0x06, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x10, 0x07, 0x12, 0x18, 0x0a, 0x14, 0x58, 0x53, 0x53, 0x5f, 0x41, 0x4e, 0x47, 0x55, 0x4c, 0x41,
	0x52, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x08, 0x12, 0x12, 0x0a, 0x0e,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x48, 0x45, 0x41, 0x44, 0x45, 0x52, 0x10, 0x09,
	0x12, 0x23, 0x0a, 0x1f, 0x4d, 0x49, 0x53, 0x53, 0x50, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x5f, 0x53,
	0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x45, 0x41, 0x44, 0x45, 0x52, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x10, 0x0a, 0x12, 0x26, 0x0a, 0x22, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43,
	0x48, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x45,
	0x41, 0x44, 0x45, 0x52, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x53, 0x10, 0x0b, 0x3a, 0x84, 0x01,
	0xea, 0x41, 0x80, 0x01, 0x0a, 0x29, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x46, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12,
	0x53, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x7d, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x2f,
	0x7b, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x7d, 0x2f, 0x73, 0x63,
	0x61, 0x6e, 0x52, 0x75, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x72, 0x75, 0x6e,
	0x7d, 0x2f, 0x66, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x7b, 0x66, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x7d, 0x42, 0x98, 0x01, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x61,
	0x6c, 0x70, 0x68, 0x61, 0x42, 0x0c, 0x46, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x50, 0x01, 0x5a, 0x59, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c,
	0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2f, 0x77, 0x65, 0x62, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x3b, 0x77, 0x65, 0x62,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_cloud_websecurityscanner_v1alpha_finding_proto_rawDescOnce sync.Once
	file_google_cloud_websecurityscanner_v1alpha_finding_proto_rawDescData = file_google_cloud_websecurityscanner_v1alpha_finding_proto_rawDesc
)

func file_google_cloud_websecurityscanner_v1alpha_finding_proto_rawDescGZIP() []byte {
	file_google_cloud_websecurityscanner_v1alpha_finding_proto_rawDescOnce.Do(func() {
		file_google_cloud_websecurityscanner_v1alpha_finding_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_cloud_websecurityscanner_v1alpha_finding_proto_rawDescData)
	})
	return file_google_cloud_websecurityscanner_v1alpha_finding_proto_rawDescData
}

var file_google_cloud_websecurityscanner_v1alpha_finding_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_google_cloud_websecurityscanner_v1alpha_finding_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_google_cloud_websecurityscanner_v1alpha_finding_proto_goTypes = []interface{}{
	(Finding_FindingType)(0),     // 0: google.cloud.websecurityscanner.v1alpha.Finding.FindingType
	(*Finding)(nil),              // 1: google.cloud.websecurityscanner.v1alpha.Finding
	(*OutdatedLibrary)(nil),      // 2: google.cloud.websecurityscanner.v1alpha.OutdatedLibrary
	(*ViolatingResource)(nil),    // 3: google.cloud.websecurityscanner.v1alpha.ViolatingResource
	(*VulnerableHeaders)(nil),    // 4: google.cloud.websecurityscanner.v1alpha.VulnerableHeaders
	(*VulnerableParameters)(nil), // 5: google.cloud.websecurityscanner.v1alpha.VulnerableParameters
	(*Xss)(nil),                  // 6: google.cloud.websecurityscanner.v1alpha.Xss
}
var file_google_cloud_websecurityscanner_v1alpha_finding_proto_depIdxs = []int32{
	0, // 0: google.cloud.websecurityscanner.v1alpha.Finding.finding_type:type_name -> google.cloud.websecurityscanner.v1alpha.Finding.FindingType
	2, // 1: google.cloud.websecurityscanner.v1alpha.Finding.outdated_library:type_name -> google.cloud.websecurityscanner.v1alpha.OutdatedLibrary
	3, // 2: google.cloud.websecurityscanner.v1alpha.Finding.violating_resource:type_name -> google.cloud.websecurityscanner.v1alpha.ViolatingResource
	4, // 3: google.cloud.websecurityscanner.v1alpha.Finding.vulnerable_headers:type_name -> google.cloud.websecurityscanner.v1alpha.VulnerableHeaders
	5, // 4: google.cloud.websecurityscanner.v1alpha.Finding.vulnerable_parameters:type_name -> google.cloud.websecurityscanner.v1alpha.VulnerableParameters
	6, // 5: google.cloud.websecurityscanner.v1alpha.Finding.xss:type_name -> google.cloud.websecurityscanner.v1alpha.Xss
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_google_cloud_websecurityscanner_v1alpha_finding_proto_init() }
func file_google_cloud_websecurityscanner_v1alpha_finding_proto_init() {
	if File_google_cloud_websecurityscanner_v1alpha_finding_proto != nil {
		return
	}
	file_google_cloud_websecurityscanner_v1alpha_finding_addon_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_cloud_websecurityscanner_v1alpha_finding_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Finding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_cloud_websecurityscanner_v1alpha_finding_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_cloud_websecurityscanner_v1alpha_finding_proto_goTypes,
		DependencyIndexes: file_google_cloud_websecurityscanner_v1alpha_finding_proto_depIdxs,
		EnumInfos:         file_google_cloud_websecurityscanner_v1alpha_finding_proto_enumTypes,
		MessageInfos:      file_google_cloud_websecurityscanner_v1alpha_finding_proto_msgTypes,
	}.Build()
	File_google_cloud_websecurityscanner_v1alpha_finding_proto = out.File
	file_google_cloud_websecurityscanner_v1alpha_finding_proto_rawDesc = nil
	file_google_cloud_websecurityscanner_v1alpha_finding_proto_goTypes = nil
	file_google_cloud_websecurityscanner_v1alpha_finding_proto_depIdxs = nil
}
