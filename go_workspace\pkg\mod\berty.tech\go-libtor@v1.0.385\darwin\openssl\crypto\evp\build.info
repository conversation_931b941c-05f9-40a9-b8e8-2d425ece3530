LIBS=../../libcrypto
SOURCE[../../libcrypto]=\
        encode.c digest.c evp_enc.c evp_key.c evp_cnf.c \
        e_des.c e_bf.c e_idea.c e_des3.c e_camellia.c\
        e_rc4.c e_aes.c names.c e_seed.c e_aria.c e_sm4.c \
        e_xcbc_d.c e_rc2.c e_cast.c e_rc5.c \
        m_null.c m_md2.c m_md4.c m_md5.c m_sha1.c m_wp.c \
        m_md5_sha1.c m_mdc2.c m_ripemd.c m_sha3.c \
        p_open.c p_seal.c p_sign.c p_verify.c p_lib.c p_enc.c p_dec.c \
        bio_md.c bio_b64.c bio_enc.c evp_err.c e_null.c \
        c_allc.c c_alld.c evp_lib.c bio_ok.c \
        evp_pkey.c evp_pbe.c p5_crpt.c p5_crpt2.c pbe_scrypt.c \
        e_old.c pmeth_lib.c pmeth_fn.c pmeth_gn.c m_sigver.c \
        e_aes_cbc_hmac_sha1.c e_aes_cbc_hmac_sha256.c e_rc4_hmac_md5.c \
        e_chacha20_poly1305.c cmeth_lib.c

INCLUDE[e_aes.o]=.. ../modes
INCLUDE[e_aes_cbc_hmac_sha1.o]=../modes
INCLUDE[e_aes_cbc_hmac_sha256.o]=../modes
INCLUDE[e_aria.o]=.. ../modes
INCLUDE[e_camellia.o]=.. ../modes
INCLUDE[e_sm4.o]=.. ../modes
INCLUDE[e_des.o]=..
INCLUDE[e_des3.o]=..
INCLUDE[m_sha3.o]=..
