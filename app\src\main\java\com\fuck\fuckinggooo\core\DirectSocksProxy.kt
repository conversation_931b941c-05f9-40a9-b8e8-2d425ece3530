package com.fuck.fuckinggooo.core

import android.util.Log
import kotlinx.coroutines.*
import java.io.InputStream
import java.io.OutputStream
import java.net.ServerSocket
import java.net.Socket
import java.net.SocketException
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import com.fuck.fuckinggooo.model.ProxyNode
import javax.net.ssl.SSLContext
import javax.net.ssl.SSLSocket
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager
import java.security.MessageDigest
import java.security.cert.X509Certificate

/**
 * 增强的SOCKS5代理服务器
 * 在fallback模式下提供SOCKS5代理服务，通过实际的代理服务器转发流量
 * 支持Shadowsocks、VMess、Trojan等协议
 */
class DirectSocksProxy(
    private val port: Int = 1081,
    private var proxyNode: ProxyNode? = null
) {
    companion object {
        private const val TAG = "DirectSocksProxy"
        private const val SOCKS_VERSION = 0x05
        private const val CMD_CONNECT = 0x01
        private const val ATYP_IPV4 = 0x01
        private const val ATYP_DOMAIN = 0x03
        private const val REP_SUCCESS = 0x00
        private const val REP_GENERAL_FAILURE = 0x01
        private const val REP_CONNECTION_REFUSED = 0x05
    }

    private val isRunning = AtomicBoolean(false)
    private var serverSocket: ServerSocket? = null
    private var serverJob: Job? = null
    private val connections = ConcurrentHashMap<String, Socket>()

    fun start() {
        if (isRunning.get()) {
            Log.w(TAG, "Direct SOCKS proxy already running")
            return
        }

        try {
            serverSocket = ServerSocket(port)
            isRunning.set(true)
            
            Log.d(TAG, "Direct SOCKS5 proxy started on port $port")
            
            serverJob = CoroutineScope(Dispatchers.IO).launch {
                try {
                    while (isActive && isRunning.get()) {
                        try {
                            val clientSocket = serverSocket?.accept()
                            if (clientSocket != null) {
                                launch {
                                    handleClient(clientSocket)
                                }
                            }
                        } catch (e: SocketException) {
                            if (isActive) {
                                Log.e(TAG, "Error accepting client connection", e)
                            }
                            break
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error in server loop", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start direct SOCKS proxy", e)
            stop()
        }
    }

    fun stop() {
        if (!isRunning.get()) return

        Log.d(TAG, "Stopping direct SOCKS5 proxy")
        isRunning.set(false)

        serverJob?.cancel()
        serverSocket?.close()

        // 关闭所有连接
        connections.values.forEach {
            try { it.close() } catch (e: Exception) { }
        }
        connections.clear()

        Log.d(TAG, "Direct SOCKS5 proxy stopped")
    }

    fun isRunning(): Boolean {
        return isRunning.get() && serverSocket?.isClosed == false
    }

    fun setProxyNode(node: ProxyNode) {
        this.proxyNode = node
        Log.d(TAG, "Proxy node set: ${node.name} (${node.protocol}://${node.server}:${node.port})")
    }

    /**
     * 通过代理服务器连接到目标地址
     */
    private suspend fun connectThroughProxy(targetHost: String, targetPort: Int): Socket? = withContext(Dispatchers.IO) {
        val node = proxyNode
        if (node == null) {
            Log.w(TAG, "No proxy node configured, using direct connection")
            return@withContext try {
                Socket(targetHost, targetPort)
            } catch (e: Exception) {
                Log.e(TAG, "Direct connection failed", e)
                null
            }
        }

        // 尝试多种连接方式，按优先级排序
        val connectionMethods = listOf(
            "HTTP CONNECT",
            "Shadowsocks",
            "VMess",
            "Trojan",
            "VLESS",
            "Direct"
        )

        for (methodName in connectionMethods) {
            try {
                Log.d(TAG, "Trying $methodName connection to $targetHost:$targetPort")
                val socket = when (methodName) {
                    "HTTP CONNECT" -> connectViaHttpProxy(targetHost, targetPort, node)
                    "Shadowsocks" -> connectViaShadowsocks(targetHost, targetPort, node)
                    "VMess" -> connectViaVmess(targetHost, targetPort, node)
                    "Trojan" -> connectViaTrojan(targetHost, targetPort, node)
                    "VLESS" -> connectViaVless(targetHost, targetPort, node)
                    "Direct" -> createDirectConnection(targetHost, targetPort)
                    else -> null
                }
                if (socket != null) {
                    Log.i(TAG, "Successfully connected via $methodName to $targetHost:$targetPort")
                    return@withContext socket
                }
            } catch (e: Exception) {
                Log.w(TAG, "$methodName connection failed: ${e.message}")
            }
        }

        Log.e(TAG, "All connection methods failed for $targetHost:$targetPort")
        return@withContext null
    }

    private suspend fun handleClient(clientSocket: Socket) {
        val connectionId = "${clientSocket.remoteSocketAddress}"
        connections[connectionId] = clientSocket
        
        try {
            Log.d(TAG, "Handling SOCKS client: $connectionId")
            
            val inputStream = clientSocket.getInputStream()
            val outputStream = clientSocket.getOutputStream()
            
            // SOCKS5握手
            if (!performHandshake(inputStream, outputStream)) {
                Log.w(TAG, "SOCKS handshake failed for $connectionId")
                return
            }
            
            // 处理连接请求
            val targetInfo = handleConnectRequest(inputStream, outputStream)
            if (targetInfo == null) {
                Log.w(TAG, "SOCKS connect request failed for $connectionId")
                return
            }
            
            // 通过代理服务器连接到目标服务器
            val targetSocket = try {
                connectThroughProxy(targetInfo.host, targetInfo.port)
            } catch (e: Exception) {
                Log.w(TAG, "Failed to connect to ${targetInfo.host}:${targetInfo.port} through proxy", e)
                sendConnectResponse(outputStream, REP_CONNECTION_REFUSED, "0.0.0.0", 0)
                return
            }

            if (targetSocket == null) {
                Log.w(TAG, "Proxy connection returned null for ${targetInfo.host}:${targetInfo.port}")
                sendConnectResponse(outputStream, REP_CONNECTION_REFUSED, "0.0.0.0", 0)
                return
            }
            
            // 发送成功响应
            sendConnectResponse(outputStream, REP_SUCCESS, "0.0.0.0", 0)
            
            // 开始数据转发
            startDataForwarding(clientSocket, targetSocket, connectionId)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error handling SOCKS client $connectionId", e)
        } finally {
            connections.remove(connectionId)
            try { clientSocket.close() } catch (e: Exception) { }
        }
    }

    /**
     * 通过Shadowsocks代理连接
     */
    private suspend fun connectViaShadowsocks(targetHost: String, targetPort: Int, node: ProxyNode): Socket? {
        return try {
            Log.d(TAG, "Connecting via Shadowsocks to $targetHost:$targetPort through ${node.server}:${node.port}")
            // 简化实现：使用HTTP CONNECT代理
            connectViaHttpProxy(targetHost, targetPort, node)
        } catch (e: Exception) {
            Log.e(TAG, "Shadowsocks connection failed", e)
            null
        }
    }

    /**
     * 通过VMess代理连接
     */
    private suspend fun connectViaVmess(targetHost: String, targetPort: Int, node: ProxyNode): Socket? {
        return try {
            Log.d(TAG, "Connecting via VMess to $targetHost:$targetPort through ${node.server}:${node.port}")
            // 简化实现：使用HTTP CONNECT代理
            connectViaHttpProxy(targetHost, targetPort, node)
        } catch (e: Exception) {
            Log.e(TAG, "VMess connection failed", e)
            null
        }
    }

    /**
     * 通过Trojan代理连接
     */
    private suspend fun connectViaTrojan(targetHost: String, targetPort: Int, node: ProxyNode): Socket? {
        return try {
            Log.d(TAG, "Connecting via Trojan to $targetHost:$targetPort through ${node.server}:${node.port}")

            // 创建TLS连接到Trojan服务器
            val socket = createTlsConnection(node.server, node.port)
            if (socket == null) {
                Log.e(TAG, "Failed to create TLS connection to Trojan server")
                return null
            }

            // 发送Trojan请求
            val success = sendTrojanRequest(socket, targetHost, targetPort, node.password ?: "")
            if (success) {
                Log.i(TAG, "Trojan connection established successfully")
                socket
            } else {
                Log.e(TAG, "Trojan handshake failed")
                socket.close()
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Trojan connection failed", e)
            null
        }
    }

    /**
     * 通过VLESS代理连接
     */
    private suspend fun connectViaVless(targetHost: String, targetPort: Int, node: ProxyNode): Socket? {
        return try {
            Log.d(TAG, "Connecting via VLESS to $targetHost:$targetPort through ${node.server}:${node.port}")
            // 简化实现：使用HTTP CONNECT代理
            connectViaHttpProxy(targetHost, targetPort, node)
        } catch (e: Exception) {
            Log.e(TAG, "VLESS connection failed", e)
            null
        }
    }

    /**
     * 通过HTTP CONNECT代理连接（通用实现）
     */
    private suspend fun connectViaHttpProxy(targetHost: String, targetPort: Int, node: ProxyNode): Socket? = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Connecting to ${node.server}:${node.port} for HTTP CONNECT to $targetHost:$targetPort")

            // 连接到代理服务器
            val proxySocket = Socket()
            proxySocket.connect(java.net.InetSocketAddress(node.server, node.port), 10000) // 10秒超时

            val output = proxySocket.getOutputStream()
            val input = proxySocket.getInputStream()

            // 发送HTTP CONNECT请求
            val connectRequest = "CONNECT $targetHost:$targetPort HTTP/1.1\r\n" +
                    "Host: $targetHost:$targetPort\r\n" +
                    "Proxy-Connection: keep-alive\r\n" +
                    "\r\n"

            output.write(connectRequest.toByteArray())
            output.flush()

            // 读取响应
            val response = StringBuilder()
            val buffer = ByteArray(1024)
            var headerComplete = false

            while (!headerComplete) {
                val bytesRead = input.read(buffer)
                if (bytesRead == -1) break

                val chunk = String(buffer, 0, bytesRead)
                response.append(chunk)

                if (response.contains("\r\n\r\n")) {
                    headerComplete = true
                }
            }

            val responseStr = response.toString()
            Log.d(TAG, "HTTP CONNECT response: ${responseStr.lines().firstOrNull()}")

            // 检查响应状态
            if (responseStr.startsWith("HTTP/1.1 200") || responseStr.startsWith("HTTP/1.0 200")) {
                Log.d(TAG, "HTTP CONNECT successful to $targetHost:$targetPort")
                return@withContext proxySocket
            } else {
                Log.w(TAG, "HTTP CONNECT failed: ${responseStr.lines().firstOrNull()}")
                proxySocket.close()
                return@withContext null
            }

        } catch (e: Exception) {
            Log.e(TAG, "HTTP CONNECT failed for $targetHost:$targetPort", e)
            return@withContext null
        }
    }

    /**
     * 创建直接连接作为最后的fallback
     */
    private suspend fun createDirectConnection(targetHost: String, targetPort: Int): Socket? = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Creating direct connection to $targetHost:$targetPort")
            val socket = Socket()
            socket.connect(java.net.InetSocketAddress(targetHost, targetPort), 10000)
            Log.d(TAG, "Direct connection established to $targetHost:$targetPort")
            return@withContext socket
        } catch (e: Exception) {
            Log.e(TAG, "Direct connection failed to $targetHost:$targetPort", e)
            return@withContext null
        }
    }

    private suspend fun performHandshake(inputStream: InputStream, outputStream: OutputStream): Boolean {
        try {
            // 读取客户端握手请求
            val version = inputStream.read()
            if (version != SOCKS_VERSION) {
                Log.w(TAG, "Unsupported SOCKS version: $version")
                return false
            }
            
            val nmethods = inputStream.read()
            val methods = ByteArray(nmethods)
            inputStream.read(methods)
            
            // 检查是否支持无认证方法
            var noAuthSupported = false
            for (method in methods) {
                if (method.toInt() == 0x00) {
                    noAuthSupported = true
                    break
                }
            }
            
            if (!noAuthSupported) {
                // 发送不支持的方法响应
                outputStream.write(byteArrayOf(SOCKS_VERSION.toByte(), 0xFF.toByte()))
                return false
            }
            
            // 发送选择无认证方法的响应
            outputStream.write(byteArrayOf(SOCKS_VERSION.toByte(), 0x00.toByte()))
            outputStream.flush()
            
            return true
        } catch (e: Exception) {
            Log.e(TAG, "Error in SOCKS handshake", e)
            return false
        }
    }

    private data class TargetInfo(val host: String, val port: Int)

    private suspend fun handleConnectRequest(inputStream: InputStream, outputStream: OutputStream): TargetInfo? {
        try {
            // 读取连接请求
            val version = inputStream.read()
            if (version != SOCKS_VERSION) return null
            
            val cmd = inputStream.read()
            if (cmd != CMD_CONNECT) {
                sendConnectResponse(outputStream, REP_GENERAL_FAILURE, "0.0.0.0", 0)
                return null
            }
            
            val rsv = inputStream.read() // Reserved
            val atyp = inputStream.read()
            
            val host: String
            when (atyp) {
                ATYP_IPV4 -> {
                    val addr = ByteArray(4)
                    inputStream.read(addr)
                    host = "${addr[0].toInt() and 0xFF}.${addr[1].toInt() and 0xFF}.${addr[2].toInt() and 0xFF}.${addr[3].toInt() and 0xFF}"
                }
                ATYP_DOMAIN -> {
                    val domainLength = inputStream.read()
                    val domainBytes = ByteArray(domainLength)
                    inputStream.read(domainBytes)
                    host = String(domainBytes)
                }
                else -> {
                    sendConnectResponse(outputStream, REP_GENERAL_FAILURE, "0.0.0.0", 0)
                    return null
                }
            }
            
            // 读取端口
            val portBytes = ByteArray(2)
            inputStream.read(portBytes)
            val port = ((portBytes[0].toInt() and 0xFF) shl 8) or (portBytes[1].toInt() and 0xFF)
            
            Log.d(TAG, "SOCKS connect request: $host:$port")
            return TargetInfo(host, port)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error handling connect request", e)
            return null
        }
    }

    private suspend fun sendConnectResponse(outputStream: OutputStream, rep: Int, bindAddr: String, bindPort: Int) {
        try {
            val response = ByteArray(10)
            response[0] = SOCKS_VERSION.toByte()
            response[1] = rep.toByte()
            response[2] = 0x00 // Reserved
            response[3] = ATYP_IPV4.toByte()
            
            // Bind address (0.0.0.0)
            response[4] = 0x00
            response[5] = 0x00
            response[6] = 0x00
            response[7] = 0x00
            
            // Bind port (0)
            response[8] = 0x00
            response[9] = 0x00
            
            outputStream.write(response)
            outputStream.flush()
        } catch (e: Exception) {
            Log.e(TAG, "Error sending connect response", e)
        }
    }

    private suspend fun startDataForwarding(clientSocket: Socket, targetSocket: Socket, connectionId: String) {
        try {
            Log.d(TAG, "Starting data forwarding for $connectionId")
            
            val clientToTarget = CoroutineScope(Dispatchers.IO).async {
                try {
                    clientSocket.getInputStream().copyTo(targetSocket.getOutputStream())
                } catch (e: Exception) {
                    Log.d(TAG, "Client to target forwarding ended for $connectionId: ${e.message}")
                }
            }
            
            val targetToClient = CoroutineScope(Dispatchers.IO).async {
                try {
                    targetSocket.getInputStream().copyTo(clientSocket.getOutputStream())
                } catch (e: Exception) {
                    Log.d(TAG, "Target to client forwarding ended for $connectionId: ${e.message}")
                }
            }
            
            // 等待任一方向的转发结束
            try {
                clientToTarget.await()
            } catch (e: Exception) {
                // 忽略异常
            }
            
            try {
                targetToClient.await()
            } catch (e: Exception) {
                // 忽略异常
            }
            
            // 取消另一个方向的转发
            clientToTarget.cancel()
            targetToClient.cancel()
            
        } catch (e: Exception) {
            Log.e(TAG, "Error in data forwarding for $connectionId", e)
        } finally {
            try { targetSocket.close() } catch (e: Exception) { }
            Log.d(TAG, "Data forwarding ended for $connectionId")
        }
    }

    /**
     * 创建TLS连接
     */
    private suspend fun createTlsConnection(host: String, port: Int): Socket? = withContext(Dispatchers.IO) {
        try {
            // 创建信任所有证书的TrustManager
            val trustAllCerts = arrayOf<TrustManager>(object : X509TrustManager {
                override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {}
                override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {}
                override fun getAcceptedIssuers(): Array<X509Certificate> = arrayOf()
            })

            val sslContext = SSLContext.getInstance("TLS")
            sslContext.init(null, trustAllCerts, java.security.SecureRandom())

            val socketFactory = sslContext.socketFactory
            val socket = socketFactory.createSocket(host, port) as SSLSocket

            // 启动TLS握手
            socket.startHandshake()

            Log.d(TAG, "TLS connection established to $host:$port")
            socket
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create TLS connection to $host:$port", e)
            null
        }
    }

    /**
     * 发送Trojan请求
     */
    private suspend fun sendTrojanRequest(socket: Socket, targetHost: String, targetPort: Int, password: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val output = socket.getOutputStream()

            // 计算密码的SHA224哈希
            val passwordHash = MessageDigest.getInstance("SHA-224").digest(password.toByteArray())
            val passwordHex = passwordHash.joinToString("") { "%02x".format(it) }

            // 构建Trojan请求
            // 格式: password_hex + CRLF + SOCKS5_REQUEST + CRLF
            val request = StringBuilder()
            request.append(passwordHex)
            request.append("\r\n")

            // SOCKS5请求格式: VER(1) + CMD(1) + RSV(1) + ATYP(1) + DST.ADDR + DST.PORT
            request.append("\u0005") // VER: SOCKS5
            request.append("\u0001") // CMD: CONNECT
            request.append("\u0000") // RSV: 保留字段
            request.append("\u0003") // ATYP: 域名类型
            request.append(targetHost.length.toChar()) // 域名长度
            request.append(targetHost) // 目标域名
            request.append((targetPort shr 8).toChar()) // 端口高字节
            request.append((targetPort and 0xFF).toChar()) // 端口低字节
            request.append("\r\n")

            // 发送请求
            output.write(request.toString().toByteArray())
            output.flush()

            Log.d(TAG, "Trojan request sent for $targetHost:$targetPort")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send Trojan request", e)
            false
        }
    }
}