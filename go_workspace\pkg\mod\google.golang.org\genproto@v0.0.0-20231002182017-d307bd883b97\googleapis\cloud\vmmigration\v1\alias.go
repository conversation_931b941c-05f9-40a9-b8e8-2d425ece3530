// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by aliasgen. DO NOT EDIT.

// Package vmmigration aliases all exported identifiers in package
// "cloud.google.com/go/vmmigration/apiv1/vmmigrationpb".
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb.
// Please read https://github.com/googleapis/google-cloud-go/blob/main/migration.md
// for more details.
package vmmigration

import (
	src "cloud.google.com/go/vmmigration/apiv1/vmmigrationpb"
	grpc "google.golang.org/grpc"
)

// Deprecated: Please use consts in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
const (
	AppliedLicense_BYOL                                            = src.AppliedLicense_BYOL
	AppliedLicense_NONE                                            = src.AppliedLicense_NONE
	AppliedLicense_PAYG                                            = src.AppliedLicense_PAYG
	AppliedLicense_TYPE_UNSPECIFIED                                = src.AppliedLicense_TYPE_UNSPECIFIED
	CloneJob_ACTIVE                                                = src.CloneJob_ACTIVE
	CloneJob_ADAPTING_OS                                           = src.CloneJob_ADAPTING_OS
	CloneJob_CANCELLED                                             = src.CloneJob_CANCELLED
	CloneJob_CANCELLING                                            = src.CloneJob_CANCELLING
	CloneJob_FAILED                                                = src.CloneJob_FAILED
	CloneJob_PENDING                                               = src.CloneJob_PENDING
	CloneJob_STATE_UNSPECIFIED                                     = src.CloneJob_STATE_UNSPECIFIED
	CloneJob_SUCCEEDED                                             = src.CloneJob_SUCCEEDED
	ComputeEngineBootOption_COMPUTE_ENGINE_BOOT_OPTION_BIOS        = src.ComputeEngineBootOption_COMPUTE_ENGINE_BOOT_OPTION_BIOS
	ComputeEngineBootOption_COMPUTE_ENGINE_BOOT_OPTION_EFI         = src.ComputeEngineBootOption_COMPUTE_ENGINE_BOOT_OPTION_EFI
	ComputeEngineBootOption_COMPUTE_ENGINE_BOOT_OPTION_UNSPECIFIED = src.ComputeEngineBootOption_COMPUTE_ENGINE_BOOT_OPTION_UNSPECIFIED
	ComputeEngineDiskType_COMPUTE_ENGINE_DISK_TYPE_BALANCED        = src.ComputeEngineDiskType_COMPUTE_ENGINE_DISK_TYPE_BALANCED
	ComputeEngineDiskType_COMPUTE_ENGINE_DISK_TYPE_SSD             = src.ComputeEngineDiskType_COMPUTE_ENGINE_DISK_TYPE_SSD
	ComputeEngineDiskType_COMPUTE_ENGINE_DISK_TYPE_STANDARD        = src.ComputeEngineDiskType_COMPUTE_ENGINE_DISK_TYPE_STANDARD
	ComputeEngineDiskType_COMPUTE_ENGINE_DISK_TYPE_UNSPECIFIED     = src.ComputeEngineDiskType_COMPUTE_ENGINE_DISK_TYPE_UNSPECIFIED
	ComputeEngineLicenseType_COMPUTE_ENGINE_LICENSE_TYPE_BYOL      = src.ComputeEngineLicenseType_COMPUTE_ENGINE_LICENSE_TYPE_BYOL
	ComputeEngineLicenseType_COMPUTE_ENGINE_LICENSE_TYPE_DEFAULT   = src.ComputeEngineLicenseType_COMPUTE_ENGINE_LICENSE_TYPE_DEFAULT
	ComputeEngineLicenseType_COMPUTE_ENGINE_LICENSE_TYPE_PAYG      = src.ComputeEngineLicenseType_COMPUTE_ENGINE_LICENSE_TYPE_PAYG
	ComputeScheduling_AUTOMATIC_RESTART                            = src.ComputeScheduling_AUTOMATIC_RESTART
	ComputeScheduling_MIGRATE                                      = src.ComputeScheduling_MIGRATE
	ComputeScheduling_NO_AUTOMATIC_RESTART                         = src.ComputeScheduling_NO_AUTOMATIC_RESTART
	ComputeScheduling_ON_HOST_MAINTENANCE_UNSPECIFIED              = src.ComputeScheduling_ON_HOST_MAINTENANCE_UNSPECIFIED
	ComputeScheduling_RESTART_TYPE_UNSPECIFIED                     = src.ComputeScheduling_RESTART_TYPE_UNSPECIFIED
	ComputeScheduling_TERMINATE                                    = src.ComputeScheduling_TERMINATE
	CutoverJob_ACTIVE                                              = src.CutoverJob_ACTIVE
	CutoverJob_ADAPTING_OS                                         = src.CutoverJob_ADAPTING_OS
	CutoverJob_CANCELLED                                           = src.CutoverJob_CANCELLED
	CutoverJob_CANCELLING                                          = src.CutoverJob_CANCELLING
	CutoverJob_FAILED                                              = src.CutoverJob_FAILED
	CutoverJob_PENDING                                             = src.CutoverJob_PENDING
	CutoverJob_STATE_UNSPECIFIED                                   = src.CutoverJob_STATE_UNSPECIFIED
	CutoverJob_SUCCEEDED                                           = src.CutoverJob_SUCCEEDED
	DatacenterConnector_ACTIVE                                     = src.DatacenterConnector_ACTIVE
	DatacenterConnector_FAILED                                     = src.DatacenterConnector_FAILED
	DatacenterConnector_OFFLINE                                    = src.DatacenterConnector_OFFLINE
	DatacenterConnector_PENDING                                    = src.DatacenterConnector_PENDING
	DatacenterConnector_STATE_UNSPECIFIED                          = src.DatacenterConnector_STATE_UNSPECIFIED
	MigratingVmView_MIGRATING_VM_VIEW_BASIC                        = src.MigratingVmView_MIGRATING_VM_VIEW_BASIC
	MigratingVmView_MIGRATING_VM_VIEW_FULL                         = src.MigratingVmView_MIGRATING_VM_VIEW_FULL
	MigratingVmView_MIGRATING_VM_VIEW_UNSPECIFIED                  = src.MigratingVmView_MIGRATING_VM_VIEW_UNSPECIFIED
	MigratingVm_ACTIVE                                             = src.MigratingVm_ACTIVE
	MigratingVm_CUTOVER                                            = src.MigratingVm_CUTOVER
	MigratingVm_CUTTING_OVER                                       = src.MigratingVm_CUTTING_OVER
	MigratingVm_ERROR                                              = src.MigratingVm_ERROR
	MigratingVm_FINALIZED                                          = src.MigratingVm_FINALIZED
	MigratingVm_FINALIZING                                         = src.MigratingVm_FINALIZING
	MigratingVm_FINAL_SYNC                                         = src.MigratingVm_FINAL_SYNC
	MigratingVm_FIRST_SYNC                                         = src.MigratingVm_FIRST_SYNC
	MigratingVm_PAUSED                                             = src.MigratingVm_PAUSED
	MigratingVm_PENDING                                            = src.MigratingVm_PENDING
	MigratingVm_READY                                              = src.MigratingVm_READY
	MigratingVm_STATE_UNSPECIFIED                                  = src.MigratingVm_STATE_UNSPECIFIED
	MigrationError_APPLIANCE_UPGRADE_ERROR                         = src.MigrationError_APPLIANCE_UPGRADE_ERROR
	MigrationError_CLONE_ERROR                                     = src.MigrationError_CLONE_ERROR
	MigrationError_CUTOVER_ERROR                                   = src.MigrationError_CUTOVER_ERROR
	MigrationError_ERROR_CODE_UNSPECIFIED                          = src.MigrationError_ERROR_CODE_UNSPECIFIED
	MigrationError_OS_ADAPTATION_ERROR                             = src.MigrationError_OS_ADAPTATION_ERROR
	MigrationError_SOURCE_REPLICATION_ERROR                        = src.MigrationError_SOURCE_REPLICATION_ERROR
	MigrationError_SOURCE_VALIDATION_ERROR                         = src.MigrationError_SOURCE_VALIDATION_ERROR
	MigrationError_TARGET_REPLICATION_ERROR                        = src.MigrationError_TARGET_REPLICATION_ERROR
	MigrationError_UNKNOWN_ERROR                                   = src.MigrationError_UNKNOWN_ERROR
	MigrationError_UTILIZATION_REPORT_ERROR                        = src.MigrationError_UTILIZATION_REPORT_ERROR
	SchedulingNodeAffinity_IN                                      = src.SchedulingNodeAffinity_IN
	SchedulingNodeAffinity_NOT_IN                                  = src.SchedulingNodeAffinity_NOT_IN
	SchedulingNodeAffinity_OPERATOR_UNSPECIFIED                    = src.SchedulingNodeAffinity_OPERATOR_UNSPECIFIED
	UpgradeStatus_FAILED                                           = src.UpgradeStatus_FAILED
	UpgradeStatus_RUNNING                                          = src.UpgradeStatus_RUNNING
	UpgradeStatus_STATE_UNSPECIFIED                                = src.UpgradeStatus_STATE_UNSPECIFIED
	UpgradeStatus_SUCCEEDED                                        = src.UpgradeStatus_SUCCEEDED
	UtilizationReportView_BASIC                                    = src.UtilizationReportView_BASIC
	UtilizationReportView_FULL                                     = src.UtilizationReportView_FULL
	UtilizationReportView_UTILIZATION_REPORT_VIEW_UNSPECIFIED      = src.UtilizationReportView_UTILIZATION_REPORT_VIEW_UNSPECIFIED
	UtilizationReport_CREATING                                     = src.UtilizationReport_CREATING
	UtilizationReport_FAILED                                       = src.UtilizationReport_FAILED
	UtilizationReport_MONTH                                        = src.UtilizationReport_MONTH
	UtilizationReport_STATE_UNSPECIFIED                            = src.UtilizationReport_STATE_UNSPECIFIED
	UtilizationReport_SUCCEEDED                                    = src.UtilizationReport_SUCCEEDED
	UtilizationReport_TIME_FRAME_UNSPECIFIED                       = src.UtilizationReport_TIME_FRAME_UNSPECIFIED
	UtilizationReport_WEEK                                         = src.UtilizationReport_WEEK
	UtilizationReport_YEAR                                         = src.UtilizationReport_YEAR
	VmwareVmDetails_BIOS                                           = src.VmwareVmDetails_BIOS
	VmwareVmDetails_BOOT_OPTION_UNSPECIFIED                        = src.VmwareVmDetails_BOOT_OPTION_UNSPECIFIED
	VmwareVmDetails_EFI                                            = src.VmwareVmDetails_EFI
	VmwareVmDetails_OFF                                            = src.VmwareVmDetails_OFF
	VmwareVmDetails_ON                                             = src.VmwareVmDetails_ON
	VmwareVmDetails_POWER_STATE_UNSPECIFIED                        = src.VmwareVmDetails_POWER_STATE_UNSPECIFIED
	VmwareVmDetails_SUSPENDED                                      = src.VmwareVmDetails_SUSPENDED
)

// Deprecated: Please use vars in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
var (
	AppliedLicense_Type_name                           = src.AppliedLicense_Type_name
	AppliedLicense_Type_value                          = src.AppliedLicense_Type_value
	CloneJob_State_name                                = src.CloneJob_State_name
	CloneJob_State_value                               = src.CloneJob_State_value
	ComputeEngineBootOption_name                       = src.ComputeEngineBootOption_name
	ComputeEngineBootOption_value                      = src.ComputeEngineBootOption_value
	ComputeEngineDiskType_name                         = src.ComputeEngineDiskType_name
	ComputeEngineDiskType_value                        = src.ComputeEngineDiskType_value
	ComputeEngineLicenseType_name                      = src.ComputeEngineLicenseType_name
	ComputeEngineLicenseType_value                     = src.ComputeEngineLicenseType_value
	ComputeScheduling_OnHostMaintenance_name           = src.ComputeScheduling_OnHostMaintenance_name
	ComputeScheduling_OnHostMaintenance_value          = src.ComputeScheduling_OnHostMaintenance_value
	ComputeScheduling_RestartType_name                 = src.ComputeScheduling_RestartType_name
	ComputeScheduling_RestartType_value                = src.ComputeScheduling_RestartType_value
	CutoverJob_State_name                              = src.CutoverJob_State_name
	CutoverJob_State_value                             = src.CutoverJob_State_value
	DatacenterConnector_State_name                     = src.DatacenterConnector_State_name
	DatacenterConnector_State_value                    = src.DatacenterConnector_State_value
	File_google_cloud_vmmigration_v1_vmmigration_proto = src.File_google_cloud_vmmigration_v1_vmmigration_proto
	MigratingVmView_name                               = src.MigratingVmView_name
	MigratingVmView_value                              = src.MigratingVmView_value
	MigratingVm_State_name                             = src.MigratingVm_State_name
	MigratingVm_State_value                            = src.MigratingVm_State_value
	MigrationError_ErrorCode_name                      = src.MigrationError_ErrorCode_name
	MigrationError_ErrorCode_value                     = src.MigrationError_ErrorCode_value
	SchedulingNodeAffinity_Operator_name               = src.SchedulingNodeAffinity_Operator_name
	SchedulingNodeAffinity_Operator_value              = src.SchedulingNodeAffinity_Operator_value
	UpgradeStatus_State_name                           = src.UpgradeStatus_State_name
	UpgradeStatus_State_value                          = src.UpgradeStatus_State_value
	UtilizationReportView_name                         = src.UtilizationReportView_name
	UtilizationReportView_value                        = src.UtilizationReportView_value
	UtilizationReport_State_name                       = src.UtilizationReport_State_name
	UtilizationReport_State_value                      = src.UtilizationReport_State_value
	UtilizationReport_TimeFrame_name                   = src.UtilizationReport_TimeFrame_name
	UtilizationReport_TimeFrame_value                  = src.UtilizationReport_TimeFrame_value
	VmwareVmDetails_BootOption_name                    = src.VmwareVmDetails_BootOption_name
	VmwareVmDetails_BootOption_value                   = src.VmwareVmDetails_BootOption_value
	VmwareVmDetails_PowerState_name                    = src.VmwareVmDetails_PowerState_name
	VmwareVmDetails_PowerState_value                   = src.VmwareVmDetails_PowerState_value
)

// Request message for 'AddGroupMigration' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type AddGroupMigrationRequest = src.AddGroupMigrationRequest

// Response message for 'AddGroupMigration' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type AddGroupMigrationResponse = src.AddGroupMigrationResponse

// Describes an appliance version.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ApplianceVersion = src.ApplianceVersion

// AppliedLicense holds the license data returned by adaptation module report.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type AppliedLicense = src.AppliedLicense

// License types used in OS adaptation.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type AppliedLicense_Type = src.AppliedLicense_Type

// Holds informatiom about the available versions for upgrade.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type AvailableUpdates = src.AvailableUpdates

// Request message for 'CancelCloneJob' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type CancelCloneJobRequest = src.CancelCloneJobRequest

// Response message for 'CancelCloneJob' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type CancelCloneJobResponse = src.CancelCloneJobResponse

// Request message for 'CancelCutoverJob' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type CancelCutoverJobRequest = src.CancelCutoverJobRequest

// Response message for 'CancelCutoverJob' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type CancelCutoverJobResponse = src.CancelCutoverJobResponse

// CloneJob describes the process of creating a clone of a
// [MigratingVM][google.cloud.vmmigration.v1.MigratingVm] to the requested
// target based on the latest successful uploaded snapshots. While the
// migration cycles of a MigratingVm take place, it is possible to verify the
// uploaded VM can be started in the cloud, by creating a clone. The clone can
// be created without any downtime, and it is created using the latest
// snapshots which are already in the cloud. The cloneJob is only responsible
// for its work, not its products, which means once it is finished, it will
// never touch the instance it created. It will only delete it in case of the
// CloneJob being cancelled or upon failure to clone.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type CloneJob = src.CloneJob
type CloneJob_ComputeEngineTargetDetails = src.CloneJob_ComputeEngineTargetDetails

// Possible states of the clone job.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type CloneJob_State = src.CloneJob_State

// Possible values for vm boot option.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ComputeEngineBootOption = src.ComputeEngineBootOption

// Types of disks supported for Compute Engine VM.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ComputeEngineDiskType = src.ComputeEngineDiskType

// Types of licenses used in OS adaptation.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ComputeEngineLicenseType = src.ComputeEngineLicenseType

// ComputeEngineTargetDefaults is a collection of details for creating a VM in
// a target Compute Engine project.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ComputeEngineTargetDefaults = src.ComputeEngineTargetDefaults

// ComputeEngineTargetDetails is a collection of details for creating a VM in
// a target Compute Engine project.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ComputeEngineTargetDetails = src.ComputeEngineTargetDetails

// Scheduling information for VM on maintenance/restart behaviour and node
// allocation in sole tenant nodes.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ComputeScheduling = src.ComputeScheduling
type ComputeScheduling_OnHostMaintenance = src.ComputeScheduling_OnHostMaintenance

// Defines whether the Instance should be automatically restarted whenever it
// is terminated by Compute Engine (not terminated by user).
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ComputeScheduling_RestartType = src.ComputeScheduling_RestartType

// Request message for 'CreateCloneJob' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type CreateCloneJobRequest = src.CreateCloneJobRequest

// Request message for 'CreateCutoverJob' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type CreateCutoverJobRequest = src.CreateCutoverJobRequest

// Request message for 'CreateDatacenterConnector' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type CreateDatacenterConnectorRequest = src.CreateDatacenterConnectorRequest

// Request message for 'CreateGroup' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type CreateGroupRequest = src.CreateGroupRequest

// Request message for 'CreateMigratingVm' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type CreateMigratingVmRequest = src.CreateMigratingVmRequest

// Request message for 'CreateSource' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type CreateSourceRequest = src.CreateSourceRequest

// Request message for 'CreateTargetProject' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type CreateTargetProjectRequest = src.CreateTargetProjectRequest

// Request message for 'CreateUtilizationReport' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type CreateUtilizationReportRequest = src.CreateUtilizationReportRequest

// CutoverJob message describes a cutover of a migrating VM. The CutoverJob is
// the operation of shutting down the VM, creating a snapshot and clonning the
// VM using the replicated snapshot.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type CutoverJob = src.CutoverJob
type CutoverJob_ComputeEngineTargetDetails = src.CutoverJob_ComputeEngineTargetDetails

// Possible states of the cutover job.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type CutoverJob_State = src.CutoverJob_State

// DatacenterConnector message describes a connector between the Source and
// GCP, which is installed on a vmware datacenter (an OVA vm installed by the
// user) to connect the Datacenter to GCP and support vm migration data
// transfer.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type DatacenterConnector = src.DatacenterConnector

// The possible values of the state.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type DatacenterConnector_State = src.DatacenterConnector_State

// Request message for 'DeleteDatacenterConnector' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type DeleteDatacenterConnectorRequest = src.DeleteDatacenterConnectorRequest

// Request message for 'DeleteGroup' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type DeleteGroupRequest = src.DeleteGroupRequest

// Request message for 'DeleteMigratingVm' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type DeleteMigratingVmRequest = src.DeleteMigratingVmRequest

// Request message for 'DeleteSource' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type DeleteSourceRequest = src.DeleteSourceRequest

// Request message for 'DeleteTargetProject' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type DeleteTargetProjectRequest = src.DeleteTargetProjectRequest

// Request message for 'DeleteUtilizationReport' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type DeleteUtilizationReportRequest = src.DeleteUtilizationReportRequest

// Request message for
// [fetchInventory][google.cloud.vmmigration.v1.VmMigration.FetchInventory].
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type FetchInventoryRequest = src.FetchInventoryRequest

// Response message for
// [fetchInventory][google.cloud.vmmigration.v1.VmMigration.FetchInventory].
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type FetchInventoryResponse = src.FetchInventoryResponse
type FetchInventoryResponse_VmwareVms = src.FetchInventoryResponse_VmwareVms

// Request message for 'FinalizeMigration' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type FinalizeMigrationRequest = src.FinalizeMigrationRequest

// Response message for 'FinalizeMigration' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type FinalizeMigrationResponse = src.FinalizeMigrationResponse

// Request message for 'GetCloneJob' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type GetCloneJobRequest = src.GetCloneJobRequest

// Request message for 'GetCutoverJob' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type GetCutoverJobRequest = src.GetCutoverJobRequest

// Request message for 'GetDatacenterConnector' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type GetDatacenterConnectorRequest = src.GetDatacenterConnectorRequest

// Request message for 'GetGroup' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type GetGroupRequest = src.GetGroupRequest

// Request message for 'GetMigratingVm' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type GetMigratingVmRequest = src.GetMigratingVmRequest

// Request message for 'GetSource' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type GetSourceRequest = src.GetSourceRequest

// Request message for 'GetTargetProject' call.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type GetTargetProjectRequest = src.GetTargetProjectRequest

// Request message for 'GetUtilizationReport' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type GetUtilizationReportRequest = src.GetUtilizationReportRequest

// Describes message for 'Group' resource. The Group is a collections of
// several MigratingVms.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type Group = src.Group

// Request message for 'ListCloneJobsRequest' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ListCloneJobsRequest = src.ListCloneJobsRequest

// Response message for 'ListCloneJobs' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ListCloneJobsResponse = src.ListCloneJobsResponse

// Request message for 'ListCutoverJobsRequest' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ListCutoverJobsRequest = src.ListCutoverJobsRequest

// Response message for 'ListCutoverJobs' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ListCutoverJobsResponse = src.ListCutoverJobsResponse

// Request message for 'ListDatacenterConnectors' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ListDatacenterConnectorsRequest = src.ListDatacenterConnectorsRequest

// Response message for 'ListDatacenterConnectors' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ListDatacenterConnectorsResponse = src.ListDatacenterConnectorsResponse

// Request message for 'ListGroups' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ListGroupsRequest = src.ListGroupsRequest

// Response message for 'ListGroups' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ListGroupsResponse = src.ListGroupsResponse

// Request message for 'LisMigratingVmsRequest' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ListMigratingVmsRequest = src.ListMigratingVmsRequest

// Response message for 'ListMigratingVms' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ListMigratingVmsResponse = src.ListMigratingVmsResponse

// Request message for 'ListSources' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ListSourcesRequest = src.ListSourcesRequest

// Response message for 'ListSources' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ListSourcesResponse = src.ListSourcesResponse

// Request message for 'ListTargetProjects' call.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ListTargetProjectsRequest = src.ListTargetProjectsRequest

// Response message for 'ListTargetProjects' call.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ListTargetProjectsResponse = src.ListTargetProjectsResponse

// Request message for 'ListUtilizationReports' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ListUtilizationReportsRequest = src.ListUtilizationReportsRequest

// Response message for 'ListUtilizationReports' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ListUtilizationReportsResponse = src.ListUtilizationReportsResponse

// MigratingVm describes the VM that will be migrated from a Source
// environment and its replication state.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type MigratingVm = src.MigratingVm

// Controls the level of details of a Migrating VM.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type MigratingVmView = src.MigratingVmView
type MigratingVm_ComputeEngineTargetDefaults = src.MigratingVm_ComputeEngineTargetDefaults

// The possible values of the state/health of source VM.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type MigratingVm_State = src.MigratingVm_State

// Represents migration resource error information that can be used with
// google.rpc.Status message. MigrationError is used to present the user with
// error information in migration operations.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type MigrationError = src.MigrationError

// Represents resource error codes.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type MigrationError_ErrorCode = src.MigrationError_ErrorCode

// NetworkInterface represents a NIC of a VM.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type NetworkInterface = src.NetworkInterface

// Represents the metadata of the long-running operation.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type OperationMetadata = src.OperationMetadata

// Request message for 'PauseMigration' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type PauseMigrationRequest = src.PauseMigrationRequest

// Response message for 'PauseMigration' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type PauseMigrationResponse = src.PauseMigrationResponse

// Request message for 'RemoveMigration' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type RemoveGroupMigrationRequest = src.RemoveGroupMigrationRequest

// Response message for 'RemoveMigration' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type RemoveGroupMigrationResponse = src.RemoveGroupMigrationResponse

// ReplicationCycle contains information about the current replication cycle
// status.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ReplicationCycle = src.ReplicationCycle

// ReplicationSync contain information about the last replica sync to the
// cloud.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ReplicationSync = src.ReplicationSync

// Request message for 'ResumeMigration' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ResumeMigrationRequest = src.ResumeMigrationRequest

// Response message for 'ResumeMigration' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type ResumeMigrationResponse = src.ResumeMigrationResponse

// A policy for scheduling replications.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type SchedulePolicy = src.SchedulePolicy

// Node Affinity: the configuration of desired nodes onto which this Instance
// could be scheduled. Based on
// https://cloud.google.com/compute/docs/reference/rest/v1/instances/setScheduling
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type SchedulingNodeAffinity = src.SchedulingNodeAffinity

// Possible types of node selection operators. Valid operators are IN for
// affinity and NOT_IN for anti-affinity.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type SchedulingNodeAffinity_Operator = src.SchedulingNodeAffinity_Operator

// Source message describes a specific vm migration Source resource. It
// contains the source environment information.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type Source = src.Source
type Source_Vmware = src.Source_Vmware

// Request message for 'StartMigrationRequest' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type StartMigrationRequest = src.StartMigrationRequest

// Response message for 'StartMigration' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type StartMigrationResponse = src.StartMigrationResponse

// TargetProject message represents a target Compute Engine project for a
// migration or a clone.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type TargetProject = src.TargetProject

// UnimplementedVmMigrationServer can be embedded to have forward compatible
// implementations.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type UnimplementedVmMigrationServer = src.UnimplementedVmMigrationServer

// Update message for 'UpdateGroups' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type UpdateGroupRequest = src.UpdateGroupRequest

// Request message for 'UpdateMigratingVm' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type UpdateMigratingVmRequest = src.UpdateMigratingVmRequest

// Update message for 'UpdateSources' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type UpdateSourceRequest = src.UpdateSourceRequest

// Update message for 'UpdateTargetProject' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type UpdateTargetProjectRequest = src.UpdateTargetProjectRequest

// Request message for 'UpgradeAppliance' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type UpgradeApplianceRequest = src.UpgradeApplianceRequest

// Response message for 'UpgradeAppliance' request.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type UpgradeApplianceResponse = src.UpgradeApplianceResponse

// UpgradeStatus contains information about upgradeAppliance operation.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type UpgradeStatus = src.UpgradeStatus

// The possible values of the state.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type UpgradeStatus_State = src.UpgradeStatus_State

// Utilization report details the utilization (CPU, memory, etc.) of selected
// source VMs.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type UtilizationReport = src.UtilizationReport

// Controls the level of details of a Utilization Report.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type UtilizationReportView = src.UtilizationReportView

// Utilization report state.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type UtilizationReport_State = src.UtilizationReport_State

// Report time frame options.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type UtilizationReport_TimeFrame = src.UtilizationReport_TimeFrame

// VmMigrationClient is the client API for VmMigration service. For semantics
// around ctx use and closing/ending streaming RPCs, please refer to
// https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type VmMigrationClient = src.VmMigrationClient

// VmMigrationServer is the server API for VmMigration service.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type VmMigrationServer = src.VmMigrationServer

// Utilization information of a single VM.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type VmUtilizationInfo = src.VmUtilizationInfo
type VmUtilizationInfo_VmwareVmDetails = src.VmUtilizationInfo_VmwareVmDetails

// Utilization metrics values for a single VM.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type VmUtilizationMetrics = src.VmUtilizationMetrics

// VmwareSourceDetails message describes a specific source details for the
// vmware source type.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type VmwareSourceDetails = src.VmwareSourceDetails

// VmwareVmDetails describes a VM in vCenter.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type VmwareVmDetails = src.VmwareVmDetails

// Possible values for vm boot option.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type VmwareVmDetails_BootOption = src.VmwareVmDetails_BootOption

// Possible values for the power state of the VM.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type VmwareVmDetails_PowerState = src.VmwareVmDetails_PowerState

// VmwareVmsDetails describes VMs in vCenter.
//
// Deprecated: Please use types in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
type VmwareVmsDetails = src.VmwareVmsDetails

// Deprecated: Please use funcs in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
func NewVmMigrationClient(cc grpc.ClientConnInterface) VmMigrationClient {
	return src.NewVmMigrationClient(cc)
}

// Deprecated: Please use funcs in: cloud.google.com/go/vmmigration/apiv1/vmmigrationpb
func RegisterVmMigrationServer(s *grpc.Server, srv VmMigrationServer) {
	src.RegisterVmMigrationServer(s, srv)
}
