// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0
// 	protoc        v3.13.0
// source: google/cloud/visualinspection/v1beta1/metrics.proto

package visualinspection

import (
	reflect "reflect"
	sync "sync"

	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

// All metrics for the solution.
type Metrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Output only. Metrics for the solution at different slices.
	MetricsSlice map[string]*MetricsSlice `protobuf:"bytes,1,rep,name=metrics_slice,json=metricsSlice,proto3" json:"metrics_slice,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Metrics) Reset() {
	*x = Metrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Metrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metrics) ProtoMessage() {}

func (x *Metrics) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metrics.ProtoReflect.Descriptor instead.
func (*Metrics) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_metrics_proto_rawDescGZIP(), []int{0}
}

func (x *Metrics) GetMetricsSlice() map[string]*MetricsSlice {
	if x != nil {
		return x.MetricsSlice
	}
	return nil
}

// Metrics at a particular slice.
// A slice can be "pixel" level, "image" level, "component" level, "unit" level,
// and/or "product" level depending on the solution and the input data.
type MetricsSlice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Output only. The AnnotationSpec which this MetricSlice refers to. It is
	// empty if this MetricSlice aggregates across AnnotationSpecs.
	AnnotationSpec string `protobuf:"bytes,8,opt,name=annotation_spec,json=annotationSpec,proto3" json:"annotation_spec,omitempty"`
	// Output only. The AnnotationSpec display name which this MetricSlice refers
	// to. It is empty if this MetricSlice aggregates across AnnotationSpecs.
	DisplayName string `protobuf:"bytes,9,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// Output only. The number of examples used to create this evaluation metrics
	// instance.
	EvaluatedExampleCount int32 `protobuf:"varint,10,opt,name=evaluated_example_count,json=evaluatedExampleCount,proto3" json:"evaluated_example_count,omitempty"`
	// Output only. Model metrics across confidence thresholds.
	ConfidenceLevelModelMetrics []*MetricsSlice_ConfidenceLevelModelMetrics `protobuf:"bytes,5,rep,name=confidence_level_model_metrics,json=confidenceLevelModelMetrics,proto3" json:"confidence_level_model_metrics,omitempty"`
	// Output only. Average model metrics.
	AverageModelMetrics *ModelMetrics `protobuf:"bytes,7,opt,name=average_model_metrics,json=averageModelMetrics,proto3" json:"average_model_metrics,omitempty"`
}

func (x *MetricsSlice) Reset() {
	*x = MetricsSlice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MetricsSlice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetricsSlice) ProtoMessage() {}

func (x *MetricsSlice) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetricsSlice.ProtoReflect.Descriptor instead.
func (*MetricsSlice) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_metrics_proto_rawDescGZIP(), []int{1}
}

func (x *MetricsSlice) GetAnnotationSpec() string {
	if x != nil {
		return x.AnnotationSpec
	}
	return ""
}

func (x *MetricsSlice) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *MetricsSlice) GetEvaluatedExampleCount() int32 {
	if x != nil {
		return x.EvaluatedExampleCount
	}
	return 0
}

func (x *MetricsSlice) GetConfidenceLevelModelMetrics() []*MetricsSlice_ConfidenceLevelModelMetrics {
	if x != nil {
		return x.ConfidenceLevelModelMetrics
	}
	return nil
}

func (x *MetricsSlice) GetAverageModelMetrics() *ModelMetrics {
	if x != nil {
		return x.AverageModelMetrics
	}
	return nil
}

// Model metrics which can vary depending on the model. These are not assumed to
// refer to defect states in particular.
type ModelMetrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Output only. The false negative rate in the range [0, 1]. This is the rate
	// with which the model incorrectly classifies examples as negative when they
	// are positive. This is consistent with the following definition:
	//
	// https:
	// //en.wikipedia.org/wiki/False_positives_
	// // and_false_negatives#False_positive_and_false_negative_rates.
	FalseNegativeRate float32 `protobuf:"fixed32,1,opt,name=false_negative_rate,json=falseNegativeRate,proto3" json:"false_negative_rate,omitempty"`
	// Output only. The false positive rate in the range [0, 1]. This is the rate
	// with which the model  incorrectly classifies examples as positive when they
	// are negative. This is consistent with the following definition:
	//
	// https:
	// //en.wikipedia.org/wiki/False_positives_
	// // and_false_negatives#False_positive_and_false_negative_rates.
	FalsePositiveRate float32 `protobuf:"fixed32,2,opt,name=false_positive_rate,json=falsePositiveRate,proto3" json:"false_positive_rate,omitempty"`
	// Output only. The precision in the range [0, 1]. Precision measures the
	// ratio of correctly classified examples by the model against all the
	// predictions made by the model. Please see
	// https://en.wikipedia.org/wiki/Precision_and_recall for its definition.
	Precision float32 `protobuf:"fixed32,3,opt,name=precision,proto3" json:"precision,omitempty"`
	// Output only. The recall in the range [0, 1]. Recall measures the ratio of
	// correctly predicted labels by the model against all true examples of that
	// label. Please see https://en.wikipedia.org/wiki/Precision_and_recall for
	// its definition.
	Recall float32 `protobuf:"fixed32,4,opt,name=recall,proto3" json:"recall,omitempty"`
	// Output only. The Intersection over union (IOU) in the range [0, 1]. IOU
	// measures the proportion of label regions that are common between ground
	// truth and model predictions versus their union. Please see
	// https://en.wikipedia.org/wiki/Jaccard_index for its definition.
	Iou float32 `protobuf:"fixed32,5,opt,name=iou,proto3" json:"iou,omitempty"`
	// Output only. The Dice similarity coefficient in the range [0, 1]. Dice
	// similarity coefficient is similar to IOU except that the common label
	// region is weighted twice in the calculation. Please see its definition
	// here: https://en.wikipedia.org/wiki/S%C3%B8rensen%E2%80%93Dice_coefficient
	DiceSimilarityCoefficient float32 `protobuf:"fixed32,6,opt,name=dice_similarity_coefficient,json=diceSimilarityCoefficient,proto3" json:"dice_similarity_coefficient,omitempty"`
	// Output only. Confusion matrix for the classifications.
	// Only present for aggregated metrics.
	ConfusionMatrix *ConfusionMatrix `protobuf:"bytes,7,opt,name=confusion_matrix,json=confusionMatrix,proto3" json:"confusion_matrix,omitempty"`
	// Output only. The average precision in the range [0, 1].
	AveragePrecision float32 `protobuf:"fixed32,8,opt,name=average_precision,json=averagePrecision,proto3" json:"average_precision,omitempty"`
}

func (x *ModelMetrics) Reset() {
	*x = ModelMetrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelMetrics) ProtoMessage() {}

func (x *ModelMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelMetrics.ProtoReflect.Descriptor instead.
func (*ModelMetrics) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_metrics_proto_rawDescGZIP(), []int{2}
}

func (x *ModelMetrics) GetFalseNegativeRate() float32 {
	if x != nil {
		return x.FalseNegativeRate
	}
	return 0
}

func (x *ModelMetrics) GetFalsePositiveRate() float32 {
	if x != nil {
		return x.FalsePositiveRate
	}
	return 0
}

func (x *ModelMetrics) GetPrecision() float32 {
	if x != nil {
		return x.Precision
	}
	return 0
}

func (x *ModelMetrics) GetRecall() float32 {
	if x != nil {
		return x.Recall
	}
	return 0
}

func (x *ModelMetrics) GetIou() float32 {
	if x != nil {
		return x.Iou
	}
	return 0
}

func (x *ModelMetrics) GetDiceSimilarityCoefficient() float32 {
	if x != nil {
		return x.DiceSimilarityCoefficient
	}
	return 0
}

func (x *ModelMetrics) GetConfusionMatrix() *ConfusionMatrix {
	if x != nil {
		return x.ConfusionMatrix
	}
	return nil
}

func (x *ModelMetrics) GetAveragePrecision() float32 {
	if x != nil {
		return x.AveragePrecision
	}
	return 0
}

// Confusion matrix across different class labels (if present).
type ConfusionMatrix struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Output only. Counts of items across slices.
	// For example, rows[i][j].count indicate the count for class_labels[i] as
	// present in ground truth versus class_labels[j] as predicted by the model.
	// The number of rows is the same as individual number of "counts" in a Row as
	// well as the number of class_labels. So if the number of class_labels is n,
	// then rows count is n as well as each rows.count has also count n.
	Rows []*ConfusionMatrix_Row `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
	// Output only. Class labels as the rows/columns of the confusion matrix.
	ClassLabels []string `protobuf:"bytes,2,rep,name=class_labels,json=classLabels,proto3" json:"class_labels,omitempty"`
	// Output only. Class AnnotationSpecs as the rows/columns of the confusion
	// matrix.
	AnnotationSpecs []string `protobuf:"bytes,3,rep,name=annotation_specs,json=annotationSpecs,proto3" json:"annotation_specs,omitempty"`
}

func (x *ConfusionMatrix) Reset() {
	*x = ConfusionMatrix{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfusionMatrix) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfusionMatrix) ProtoMessage() {}

func (x *ConfusionMatrix) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfusionMatrix.ProtoReflect.Descriptor instead.
func (*ConfusionMatrix) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_metrics_proto_rawDescGZIP(), []int{3}
}

func (x *ConfusionMatrix) GetRows() []*ConfusionMatrix_Row {
	if x != nil {
		return x.Rows
	}
	return nil
}

func (x *ConfusionMatrix) GetClassLabels() []string {
	if x != nil {
		return x.ClassLabels
	}
	return nil
}

func (x *ConfusionMatrix) GetAnnotationSpecs() []string {
	if x != nil {
		return x.AnnotationSpecs
	}
	return nil
}

// Model metrics at a specific threshold.
type MetricsSlice_ConfidenceLevelModelMetrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Output only. Model metrics.
	ModelMetrics *ModelMetrics `protobuf:"bytes,1,opt,name=model_metrics,json=modelMetrics,proto3" json:"model_metrics,omitempty"`
	// Output only. Confidence threshold in the range [0, 1].
	ConfidenceThreshold float32 `protobuf:"fixed32,2,opt,name=confidence_threshold,json=confidenceThreshold,proto3" json:"confidence_threshold,omitempty"`
	// Output only. Pixel level confidence threshold in the range [0, 1].
	PixelConfidenceThreshold float32 `protobuf:"fixed32,3,opt,name=pixel_confidence_threshold,json=pixelConfidenceThreshold,proto3" json:"pixel_confidence_threshold,omitempty"`
	// Output only. Cluster distance threshold in the range [0, 1] normalized by
	// the diagonal length of the image.
	ClusterDistanceThreshold float32 `protobuf:"fixed32,4,opt,name=cluster_distance_threshold,json=clusterDistanceThreshold,proto3" json:"cluster_distance_threshold,omitempty"`
}

func (x *MetricsSlice_ConfidenceLevelModelMetrics) Reset() {
	*x = MetricsSlice_ConfidenceLevelModelMetrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MetricsSlice_ConfidenceLevelModelMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetricsSlice_ConfidenceLevelModelMetrics) ProtoMessage() {}

func (x *MetricsSlice_ConfidenceLevelModelMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetricsSlice_ConfidenceLevelModelMetrics.ProtoReflect.Descriptor instead.
func (*MetricsSlice_ConfidenceLevelModelMetrics) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_metrics_proto_rawDescGZIP(), []int{1, 0}
}

func (x *MetricsSlice_ConfidenceLevelModelMetrics) GetModelMetrics() *ModelMetrics {
	if x != nil {
		return x.ModelMetrics
	}
	return nil
}

func (x *MetricsSlice_ConfidenceLevelModelMetrics) GetConfidenceThreshold() float32 {
	if x != nil {
		return x.ConfidenceThreshold
	}
	return 0
}

func (x *MetricsSlice_ConfidenceLevelModelMetrics) GetPixelConfidenceThreshold() float32 {
	if x != nil {
		return x.PixelConfidenceThreshold
	}
	return 0
}

func (x *MetricsSlice_ConfidenceLevelModelMetrics) GetClusterDistanceThreshold() float32 {
	if x != nil {
		return x.ClusterDistanceThreshold
	}
	return 0
}

// Individual row for the confusion matrix.
type ConfusionMatrix_Row struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Output only. Count of items in a "slice" classified at a particular
	// class_label in the ground truth versus that classified by the solution.
	Count []int64 `protobuf:"varint,1,rep,packed,name=count,proto3" json:"count,omitempty"`
}

func (x *ConfusionMatrix_Row) Reset() {
	*x = ConfusionMatrix_Row{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfusionMatrix_Row) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfusionMatrix_Row) ProtoMessage() {}

func (x *ConfusionMatrix_Row) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfusionMatrix_Row.ProtoReflect.Descriptor instead.
func (*ConfusionMatrix_Row) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_metrics_proto_rawDescGZIP(), []int{3, 0}
}

func (x *ConfusionMatrix_Row) GetCount() []int64 {
	if x != nil {
		return x.Count
	}
	return nil
}

var File_google_cloud_visualinspection_v1beta1_metrics_proto protoreflect.FileDescriptor

var file_google_cloud_visualinspection_v1beta1_metrics_proto_rawDesc = []byte{
	0x0a, 0x33, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x76,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x25, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68,
	0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xeb, 0x01, 0x0a, 0x07,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x6a, 0x0a, 0x0d, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x5f, 0x73, 0x6c, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x2e, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x53, 0x6c, 0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0c, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x53, 0x6c,
	0x69, 0x63, 0x65, 0x1a, 0x74, 0x0a, 0x11, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x53, 0x6c,
	0x69, 0x63, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x49, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69,
	0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61,
	0x31, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x53, 0x6c, 0x69, 0x63, 0x65, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xe8, 0x05, 0x0a, 0x0c, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x53, 0x6c, 0x69, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x0f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0e, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x70, 0x65, 0x63, 0x12, 0x26, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03,
	0xe0, 0x41, 0x03, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x3b, 0x0a, 0x17, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x78,
	0x61, 0x6d, 0x70, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x15, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65,
	0x64, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x99, 0x01,
	0x0a, 0x1e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x53, 0x6c, 0x69, 0x63, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x1b, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x6c, 0x0a, 0x15, 0x61, 0x76, 0x65,
	0x72, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e,
	0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31,
	0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x42, 0x03, 0xe0,
	0x41, 0x03, 0x52, 0x13, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x1a, 0xba, 0x02, 0x0a, 0x1b, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x5d, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x36, 0x0a, 0x14, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x02, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x13, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x41,
	0x0a, 0x1a, 0x70, 0x69, 0x78, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x02, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x18, 0x70, 0x69, 0x78, 0x65, 0x6c, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c,
	0x64, 0x12, 0x41, 0x0a, 0x1a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x64, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x02, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x18, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x68, 0x72, 0x65, 0x73,
	0x68, 0x6f, 0x6c, 0x64, 0x22, 0xae, 0x03, 0x0a, 0x0c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x33, 0x0a, 0x13, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x5f, 0x6e,
	0x65, 0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x02, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x11, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x4e, 0x65,
	0x67, 0x61, 0x74, 0x69, 0x76, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x66, 0x61,
	0x6c, 0x73, 0x65, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x72, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x11, 0x66, 0x61,
	0x6c, 0x73, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12,
	0x21, 0x0a, 0x09, 0x70, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x02, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x09, 0x70, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x06, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x02, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x06, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x12,
	0x15, 0x0a, 0x03, 0x69, 0x6f, 0x75, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x42, 0x03, 0xe0, 0x41,
	0x03, 0x52, 0x03, 0x69, 0x6f, 0x75, 0x12, 0x43, 0x0a, 0x1b, 0x64, 0x69, 0x63, 0x65, 0x5f, 0x73,
	0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x63, 0x6f, 0x65, 0x66, 0x66, 0x69,
	0x63, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x42, 0x03, 0xe0, 0x41, 0x03,
	0x52, 0x19, 0x64, 0x69, 0x63, 0x65, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79,
	0x43, 0x6f, 0x65, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x66, 0x0a, 0x10, 0x63,
	0x6f, 0x6e, 0x66, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x43, 0x6f,
	0x6e, 0x66, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x42, 0x03, 0xe0,
	0x41, 0x03, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x66, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x74,
	0x72, 0x69, 0x78, 0x12, 0x30, 0x0a, 0x11, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x70,
	0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02, 0x42, 0x03,
	0xe0, 0x41, 0x03, 0x52, 0x10, 0x61, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x50, 0x72, 0x65, 0x63,
	0x69, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xe0, 0x01, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x66, 0x75, 0x73,
	0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x12, 0x53, 0x0a, 0x04, 0x72, 0x6f, 0x77,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73,
	0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e,
	0x43, 0x6f, 0x6e, 0x66, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x2e,
	0x52, 0x6f, 0x77, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x12, 0x26,
	0x0a, 0x0c, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0b, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x10, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x70, 0x65, 0x63, 0x73, 0x1a, 0x20, 0x0a, 0x03, 0x52, 0x6f, 0x77, 0x12, 0x19, 0x0a,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x03, 0xe0, 0x41,
	0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0xff, 0x01, 0x0a, 0x29, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x50, 0x01, 0x5a, 0x55, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x3b, 0x76,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0xaa,
	0x02, 0x25, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x56, 0x31, 0x42, 0x65, 0x74, 0x61, 0x31, 0xca, 0x02, 0x25, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x5c, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x5c, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x49, 0x6e, 0x73,
	0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5c, 0x56, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0xea,
	0x02, 0x28, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x3a,
	0x3a, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x3a, 0x3a, 0x56, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_google_cloud_visualinspection_v1beta1_metrics_proto_rawDescOnce sync.Once
	file_google_cloud_visualinspection_v1beta1_metrics_proto_rawDescData = file_google_cloud_visualinspection_v1beta1_metrics_proto_rawDesc
)

func file_google_cloud_visualinspection_v1beta1_metrics_proto_rawDescGZIP() []byte {
	file_google_cloud_visualinspection_v1beta1_metrics_proto_rawDescOnce.Do(func() {
		file_google_cloud_visualinspection_v1beta1_metrics_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_cloud_visualinspection_v1beta1_metrics_proto_rawDescData)
	})
	return file_google_cloud_visualinspection_v1beta1_metrics_proto_rawDescData
}

var file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_google_cloud_visualinspection_v1beta1_metrics_proto_goTypes = []interface{}{
	(*Metrics)(nil),         // 0: google.cloud.visualinspection.v1beta1.Metrics
	(*MetricsSlice)(nil),    // 1: google.cloud.visualinspection.v1beta1.MetricsSlice
	(*ModelMetrics)(nil),    // 2: google.cloud.visualinspection.v1beta1.ModelMetrics
	(*ConfusionMatrix)(nil), // 3: google.cloud.visualinspection.v1beta1.ConfusionMatrix
	nil,                     // 4: google.cloud.visualinspection.v1beta1.Metrics.MetricsSliceEntry
	(*MetricsSlice_ConfidenceLevelModelMetrics)(nil), // 5: google.cloud.visualinspection.v1beta1.MetricsSlice.ConfidenceLevelModelMetrics
	(*ConfusionMatrix_Row)(nil),                      // 6: google.cloud.visualinspection.v1beta1.ConfusionMatrix.Row
}
var file_google_cloud_visualinspection_v1beta1_metrics_proto_depIdxs = []int32{
	4, // 0: google.cloud.visualinspection.v1beta1.Metrics.metrics_slice:type_name -> google.cloud.visualinspection.v1beta1.Metrics.MetricsSliceEntry
	5, // 1: google.cloud.visualinspection.v1beta1.MetricsSlice.confidence_level_model_metrics:type_name -> google.cloud.visualinspection.v1beta1.MetricsSlice.ConfidenceLevelModelMetrics
	2, // 2: google.cloud.visualinspection.v1beta1.MetricsSlice.average_model_metrics:type_name -> google.cloud.visualinspection.v1beta1.ModelMetrics
	3, // 3: google.cloud.visualinspection.v1beta1.ModelMetrics.confusion_matrix:type_name -> google.cloud.visualinspection.v1beta1.ConfusionMatrix
	6, // 4: google.cloud.visualinspection.v1beta1.ConfusionMatrix.rows:type_name -> google.cloud.visualinspection.v1beta1.ConfusionMatrix.Row
	1, // 5: google.cloud.visualinspection.v1beta1.Metrics.MetricsSliceEntry.value:type_name -> google.cloud.visualinspection.v1beta1.MetricsSlice
	2, // 6: google.cloud.visualinspection.v1beta1.MetricsSlice.ConfidenceLevelModelMetrics.model_metrics:type_name -> google.cloud.visualinspection.v1beta1.ModelMetrics
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_google_cloud_visualinspection_v1beta1_metrics_proto_init() }
func file_google_cloud_visualinspection_v1beta1_metrics_proto_init() {
	if File_google_cloud_visualinspection_v1beta1_metrics_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Metrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MetricsSlice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelMetrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfusionMatrix); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MetricsSlice_ConfidenceLevelModelMetrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfusionMatrix_Row); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_cloud_visualinspection_v1beta1_metrics_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_cloud_visualinspection_v1beta1_metrics_proto_goTypes,
		DependencyIndexes: file_google_cloud_visualinspection_v1beta1_metrics_proto_depIdxs,
		MessageInfos:      file_google_cloud_visualinspection_v1beta1_metrics_proto_msgTypes,
	}.Build()
	File_google_cloud_visualinspection_v1beta1_metrics_proto = out.File
	file_google_cloud_visualinspection_v1beta1_metrics_proto_rawDesc = nil
	file_google_cloud_visualinspection_v1beta1_metrics_proto_goTypes = nil
	file_google_cloud_visualinspection_v1beta1_metrics_proto_depIdxs = nil
}
