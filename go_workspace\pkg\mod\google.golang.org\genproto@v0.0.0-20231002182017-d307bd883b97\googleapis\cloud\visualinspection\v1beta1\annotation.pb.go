// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0
// 	protoc        v3.13.0
// source: google/cloud/visualinspection/v1beta1/annotation.proto

package visualinspection

import (
	reflect "reflect"
	sync "sync"

	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

// Source type of the Annotation.
type Annotation_Source_SourceType int32

const (
	// Unspecified source type.
	Annotation_Source_SOURCE_TYPE_UNSPECIFIED Annotation_Source_SourceType = 0
	// The Annotation is generated by a human user.
	Annotation_Source_HUMAN_PRODUCED Annotation_Source_SourceType = 1
	// The Annotation is generated by a ML model.
	Annotation_Source_MACHINE_PRODUCED Annotation_Source_SourceType = 2
	// The Annotation is generated by machine, and it has
	// child Annotation with HUMAN_PRODUCED.
	Annotation_Source_HUMAN_LABELED Annotation_Source_SourceType = 3
)

// Enum value maps for Annotation_Source_SourceType.
var (
	Annotation_Source_SourceType_name = map[int32]string{
		0: "SOURCE_TYPE_UNSPECIFIED",
		1: "HUMAN_PRODUCED",
		2: "MACHINE_PRODUCED",
		3: "HUMAN_LABELED",
	}
	Annotation_Source_SourceType_value = map[string]int32{
		"SOURCE_TYPE_UNSPECIFIED": 0,
		"HUMAN_PRODUCED":          1,
		"MACHINE_PRODUCED":        2,
		"HUMAN_LABELED":           3,
	}
)

func (x Annotation_Source_SourceType) Enum() *Annotation_Source_SourceType {
	p := new(Annotation_Source_SourceType)
	*p = x
	return p
}

func (x Annotation_Source_SourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Annotation_Source_SourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_google_cloud_visualinspection_v1beta1_annotation_proto_enumTypes[0].Descriptor()
}

func (Annotation_Source_SourceType) Type() protoreflect.EnumType {
	return &file_google_cloud_visualinspection_v1beta1_annotation_proto_enumTypes[0]
}

func (x Annotation_Source_SourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Annotation_Source_SourceType.Descriptor instead.
func (Annotation_Source_SourceType) EnumDescriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_annotation_proto_rawDescGZIP(), []int{0, 0, 0}
}

// Annotation is used to assign specific AnnotationSpec to a particular Image.
type Annotation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Annotation type of the Annotation.
	//
	// Types that are assignable to AnnotationType:
	//	*Annotation_Box
	//	*Annotation_Polygon
	//	*Annotation_ClassificationLabel
	//	*Annotation_Mask
	//	*Annotation_Polyline
	AnnotationType isAnnotation_AnnotationType `protobuf_oneof:"annotation_type"`
	// Output only. Resource name for Annotation generated by the system.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Output only. Timestamp when this Annotation was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// Output only. Timestamp when Annotation was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// The resource ID of the AnnotationSpec that this Annotation pertains to.
	// The AnnotationSpec must be in the same ancestor Dataset.
	// The field could be empty for a MaskAnnotation.
	AnnotationSpecId string `protobuf:"bytes,4,opt,name=annotation_spec_id,json=annotationSpecId,proto3" json:"annotation_spec_id,omitempty"`
	// Required. The resource ID of the AnnotationSet that this Annotation belongs
	// to.
	AnnotationSetId string `protobuf:"bytes,5,opt,name=annotation_set_id,json=annotationSetId,proto3" json:"annotation_set_id,omitempty"`
	// The resource ID of the parent Annotation. Parent annotation represents a
	// region in the original Image.
	ParentAnnotationId string `protobuf:"bytes,7,opt,name=parent_annotation_id,json=parentAnnotationId,proto3" json:"parent_annotation_id,omitempty"`
	// Output only. Source of the Annotation.
	Source *Annotation_Source `protobuf:"bytes,12,opt,name=source,proto3" json:"source,omitempty"`
	// The labels with user-defined metadata to organize your Annotations.
	//
	// Label keys and values can be no longer than 64 characters
	// (Unicode codepoints), can only contain lowercase letters, numeric
	// characters, underscores and dashes. International characters are allowed.
	// Label keys must start with a letter.
	//
	// See https://goo.gl/xmQnxf for more information on and examples of labels.
	Labels map[string]string `protobuf:"bytes,13,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Annotation) Reset() {
	*x = Annotation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Annotation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Annotation) ProtoMessage() {}

func (x *Annotation) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Annotation.ProtoReflect.Descriptor instead.
func (*Annotation) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_annotation_proto_rawDescGZIP(), []int{0}
}

func (m *Annotation) GetAnnotationType() isAnnotation_AnnotationType {
	if m != nil {
		return m.AnnotationType
	}
	return nil
}

func (x *Annotation) GetBox() *BoundingBoxAnnotation {
	if x, ok := x.GetAnnotationType().(*Annotation_Box); ok {
		return x.Box
	}
	return nil
}

func (x *Annotation) GetPolygon() *BoundingPolyAnnotation {
	if x, ok := x.GetAnnotationType().(*Annotation_Polygon); ok {
		return x.Polygon
	}
	return nil
}

func (x *Annotation) GetClassificationLabel() *ClassificationLabelAnnotation {
	if x, ok := x.GetAnnotationType().(*Annotation_ClassificationLabel); ok {
		return x.ClassificationLabel
	}
	return nil
}

func (x *Annotation) GetMask() *MaskAnnotation {
	if x, ok := x.GetAnnotationType().(*Annotation_Mask); ok {
		return x.Mask
	}
	return nil
}

func (x *Annotation) GetPolyline() *PolylineAnnotation {
	if x, ok := x.GetAnnotationType().(*Annotation_Polyline); ok {
		return x.Polyline
	}
	return nil
}

func (x *Annotation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Annotation) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Annotation) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Annotation) GetAnnotationSpecId() string {
	if x != nil {
		return x.AnnotationSpecId
	}
	return ""
}

func (x *Annotation) GetAnnotationSetId() string {
	if x != nil {
		return x.AnnotationSetId
	}
	return ""
}

func (x *Annotation) GetParentAnnotationId() string {
	if x != nil {
		return x.ParentAnnotationId
	}
	return ""
}

func (x *Annotation) GetSource() *Annotation_Source {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *Annotation) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type isAnnotation_AnnotationType interface {
	isAnnotation_AnnotationType()
}

type Annotation_Box struct {
	// A BoundingBox Annotation.
	Box *BoundingBoxAnnotation `protobuf:"bytes,8,opt,name=box,proto3,oneof"`
}

type Annotation_Polygon struct {
	// A BoundingPoly Annotation.
	Polygon *BoundingPolyAnnotation `protobuf:"bytes,9,opt,name=polygon,proto3,oneof"`
}

type Annotation_ClassificationLabel struct {
	// A Classification Label Annotation.
	ClassificationLabel *ClassificationLabelAnnotation `protobuf:"bytes,10,opt,name=classification_label,json=classificationLabel,proto3,oneof"`
}

type Annotation_Mask struct {
	// A Mask Annotation.
	Mask *MaskAnnotation `protobuf:"bytes,11,opt,name=mask,proto3,oneof"`
}

type Annotation_Polyline struct {
	// A Polyline Annotation,
	Polyline *PolylineAnnotation `protobuf:"bytes,14,opt,name=polyline,proto3,oneof"`
}

func (*Annotation_Box) isAnnotation_AnnotationType() {}

func (*Annotation_Polygon) isAnnotation_AnnotationType() {}

func (*Annotation_ClassificationLabel) isAnnotation_AnnotationType() {}

func (*Annotation_Mask) isAnnotation_AnnotationType() {}

func (*Annotation_Polyline) isAnnotation_AnnotationType() {}

// Detailed information for BoundingBox Annotation.
type BoundingBoxAnnotation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A box representing a rectangle region on the image.
	// A box is supposed to have exactly four vertices in clockwise order.
	// Vertices are represented in normalized format.
	NormalizedBoundingBox *NormalizedBoundingPoly `protobuf:"bytes,1,opt,name=normalized_bounding_box,json=normalizedBoundingBox,proto3" json:"normalized_bounding_box,omitempty"`
	// Output only. A confidence score between 0.0 and 1.0. A higher value
	// means greater confidence about the annotation.
	ConfidenceScore float32 `protobuf:"fixed32,2,opt,name=confidence_score,json=confidenceScore,proto3" json:"confidence_score,omitempty"`
}

func (x *BoundingBoxAnnotation) Reset() {
	*x = BoundingBoxAnnotation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoundingBoxAnnotation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoundingBoxAnnotation) ProtoMessage() {}

func (x *BoundingBoxAnnotation) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoundingBoxAnnotation.ProtoReflect.Descriptor instead.
func (*BoundingBoxAnnotation) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_annotation_proto_rawDescGZIP(), []int{1}
}

func (x *BoundingBoxAnnotation) GetNormalizedBoundingBox() *NormalizedBoundingPoly {
	if x != nil {
		return x.NormalizedBoundingBox
	}
	return nil
}

func (x *BoundingBoxAnnotation) GetConfidenceScore() float32 {
	if x != nil {
		return x.ConfidenceScore
	}
	return 0
}

// Detailed information for BoundingPoly Annotation.
type BoundingPolyAnnotation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A polygon region on the image.
	// A polygon is supposed to have N vertices in clockwise order.
	// Vertices are represented in normalized format.
	NormalizedBoundingPoly *NormalizedBoundingPoly `protobuf:"bytes,1,opt,name=normalized_bounding_poly,json=normalizedBoundingPoly,proto3" json:"normalized_bounding_poly,omitempty"`
	// Output only. A confidence score between 0.0 and 1.0. A higher value
	// means greater confidence that the annotation is positive.
	ConfidenceScore float32 `protobuf:"fixed32,2,opt,name=confidence_score,json=confidenceScore,proto3" json:"confidence_score,omitempty"`
}

func (x *BoundingPolyAnnotation) Reset() {
	*x = BoundingPolyAnnotation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoundingPolyAnnotation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoundingPolyAnnotation) ProtoMessage() {}

func (x *BoundingPolyAnnotation) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoundingPolyAnnotation.ProtoReflect.Descriptor instead.
func (*BoundingPolyAnnotation) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_annotation_proto_rawDescGZIP(), []int{2}
}

func (x *BoundingPolyAnnotation) GetNormalizedBoundingPoly() *NormalizedBoundingPoly {
	if x != nil {
		return x.NormalizedBoundingPoly
	}
	return nil
}

func (x *BoundingPolyAnnotation) GetConfidenceScore() float32 {
	if x != nil {
		return x.ConfidenceScore
	}
	return 0
}

// Detailed information for ClassificationLabel Annotation.
type ClassificationLabelAnnotation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Output only. A confidence score between 0.0 and 1.0. A higher value
	// means greater confidence that the annotation is positive.
	ConfidenceScore float32 `protobuf:"fixed32,1,opt,name=confidence_score,json=confidenceScore,proto3" json:"confidence_score,omitempty"`
}

func (x *ClassificationLabelAnnotation) Reset() {
	*x = ClassificationLabelAnnotation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClassificationLabelAnnotation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClassificationLabelAnnotation) ProtoMessage() {}

func (x *ClassificationLabelAnnotation) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClassificationLabelAnnotation.ProtoReflect.Descriptor instead.
func (*ClassificationLabelAnnotation) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_annotation_proto_rawDescGZIP(), []int{3}
}

func (x *ClassificationLabelAnnotation) GetConfidenceScore() float32 {
	if x != nil {
		return x.ConfidenceScore
	}
	return 0
}

// Detailed information for Mask Annotation.
type MaskAnnotation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Output only. One channel image which is encoded as an 8bit lossless PNG.
	// The size of the image will be the same as the original image. For a
	// specific pixel, darker color means less confidence in correctness of the
	// cateogry in the category_meask for the corresponding pixel. Black means no
	// confidence and white means full confidence.
	ConfidenceMask *Image `protobuf:"bytes,1,opt,name=confidence_mask,json=confidenceMask,proto3" json:"confidence_mask,omitempty"`
	// Three channel image which is encoded as an 8bit lossless PNG. Each pixel in
	// the image mask represents the category which the pixel in the original
	// image belong to. Each color is mapped to one AnnotationSpec based on
	// annotation_spec_colors.
	CategoryMask *Image `protobuf:"bytes,2,opt,name=category_mask,json=categoryMask,proto3" json:"category_mask,omitempty"`
}

func (x *MaskAnnotation) Reset() {
	*x = MaskAnnotation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaskAnnotation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaskAnnotation) ProtoMessage() {}

func (x *MaskAnnotation) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaskAnnotation.ProtoReflect.Descriptor instead.
func (*MaskAnnotation) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_annotation_proto_rawDescGZIP(), []int{4}
}

func (x *MaskAnnotation) GetConfidenceMask() *Image {
	if x != nil {
		return x.ConfidenceMask
	}
	return nil
}

func (x *MaskAnnotation) GetCategoryMask() *Image {
	if x != nil {
		return x.CategoryMask
	}
	return nil
}

// Detailed information for Polyline Annotation.
type PolylineAnnotation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A polyline consisting of connected straight-line segments.
	NormalizedPolyline *NormalizedPolyline `protobuf:"bytes,1,opt,name=normalized_polyline,json=normalizedPolyline,proto3" json:"normalized_polyline,omitempty"`
	// Output only. A confidence score between 0.0 and 1.0. A higher value
	// means greater confidence that the annotation is positive.
	ConfidenceScore float32 `protobuf:"fixed32,2,opt,name=confidence_score,json=confidenceScore,proto3" json:"confidence_score,omitempty"`
}

func (x *PolylineAnnotation) Reset() {
	*x = PolylineAnnotation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PolylineAnnotation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolylineAnnotation) ProtoMessage() {}

func (x *PolylineAnnotation) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolylineAnnotation.ProtoReflect.Descriptor instead.
func (*PolylineAnnotation) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_annotation_proto_rawDescGZIP(), []int{5}
}

func (x *PolylineAnnotation) GetNormalizedPolyline() *NormalizedPolyline {
	if x != nil {
		return x.NormalizedPolyline
	}
	return nil
}

func (x *PolylineAnnotation) GetConfidenceScore() float32 {
	if x != nil {
		return x.ConfidenceScore
	}
	return 0
}

// Source of the Annotation.
type Annotation_Source struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Source type.
	Type Annotation_Source_SourceType `protobuf:"varint,1,opt,name=type,proto3,enum=google.cloud.visualinspection.v1beta1.Annotation_Source_SourceType" json:"type,omitempty"`
	// Resource name of the source model when the Annotation is
	// MACHINE_PRODUCED.
	SourceModel string `protobuf:"bytes,2,opt,name=source_model,json=sourceModel,proto3" json:"source_model,omitempty"`
}

func (x *Annotation_Source) Reset() {
	*x = Annotation_Source{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Annotation_Source) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Annotation_Source) ProtoMessage() {}

func (x *Annotation_Source) ProtoReflect() protoreflect.Message {
	mi := &file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Annotation_Source.ProtoReflect.Descriptor instead.
func (*Annotation_Source) Descriptor() ([]byte, []int) {
	return file_google_cloud_visualinspection_v1beta1_annotation_proto_rawDescGZIP(), []int{0, 0}
}

func (x *Annotation_Source) GetType() Annotation_Source_SourceType {
	if x != nil {
		return x.Type
	}
	return Annotation_Source_SOURCE_TYPE_UNSPECIFIED
}

func (x *Annotation_Source) GetSourceModel() string {
	if x != nil {
		return x.SourceModel
	}
	return ""
}

var File_google_cloud_visualinspection_v1beta1_annotation_proto protoreflect.FileDescriptor

var file_google_cloud_visualinspection_v1beta1_annotation_proto_rawDesc = []byte{
	0x0a, 0x36, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x76,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x25, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73,
	0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e,
	0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31,
	0x2f, 0x67, 0x65, 0x6f, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x31, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x76, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xb6, 0x0b, 0x0a, 0x0a, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x03, 0x62, 0x6f, 0x78, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x3c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x42, 0x6f, 0x78, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52,
	0x03, 0x62, 0x6f, 0x78, 0x12, 0x59, 0x0a, 0x07, 0x70, 0x6f, 0x6c, 0x79, 0x67, 0x6f, 0x6e, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x42, 0x6f,
	0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x6f, 0x6c, 0x79, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x07, 0x70, 0x6f, 0x6c, 0x79, 0x67, 0x6f, 0x6e, 0x12,
	0x79, 0x0a, 0x14, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x13, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x4b, 0x0a, 0x04, 0x6d, 0x61,
	0x73, 0x6b, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e,
	0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31,
	0x2e, 0x4d, 0x61, 0x73, 0x6b, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48,
	0x00, 0x52, 0x04, 0x6d, 0x61, 0x73, 0x6b, 0x12, 0x57, 0x0a, 0x08, 0x70, 0x6f, 0x6c, 0x79, 0x6c,
	0x69, 0x6e, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69,
	0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61,
	0x31, 0x2e, 0x50, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x08, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65,
	0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03,
	0xe0, 0x41, 0x03, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41,
	0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a,
	0x12, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x63,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x70, 0x65, 0x63, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x11, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x55,
	0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x55, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18,
	0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x41, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x98, 0x02, 0x0a,
	0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x57, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x43, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x41, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x4d, 0x0a, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xfa, 0x41, 0x27, 0x0a, 0x25, 0x76, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22,
	0x66, 0x0a, 0x0a, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a,
	0x17, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x48, 0x55,
	0x4d, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x45, 0x44, 0x10, 0x01, 0x12, 0x14,
	0x0a, 0x10, 0x4d, 0x41, 0x43, 0x48, 0x49, 0x4e, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43,
	0x45, 0x44, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x48, 0x55, 0x4d, 0x41, 0x4e, 0x5f, 0x4c, 0x41,
	0x42, 0x45, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x3a, 0x94, 0x01, 0xea, 0x41, 0x90, 0x01, 0x0a, 0x2a, 0x76, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x41, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x62, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f,
	0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x7d, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x64,
	0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x73, 0x2f, 0x7b, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74,
	0x7d, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x2f, 0x7b, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x7d,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x42, 0x11, 0x0a, 0x0f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xbe, 0x01, 0x0a,
	0x15, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x42, 0x6f, 0x78, 0x41, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x75, 0x0a, 0x17, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c,
	0x69, 0x7a, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x6f,
	0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73,
	0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e,
	0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x50, 0x6f, 0x6c, 0x79, 0x52, 0x15, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x69, 0x7a,
	0x65, 0x64, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x42, 0x6f, 0x78, 0x12, 0x2e, 0x0a,
	0x10, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x22, 0xc1, 0x01,
	0x0a, 0x16, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x6f, 0x6c, 0x79, 0x41, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x77, 0x0a, 0x18, 0x6e, 0x6f, 0x72, 0x6d,
	0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x70, 0x6f, 0x6c, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74,
	0x61, 0x31, 0x2e, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x42, 0x6f, 0x75,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x6f, 0x6c, 0x79, 0x52, 0x16, 0x6e, 0x6f, 0x72, 0x6d, 0x61,
	0x6c, 0x69, 0x7a, 0x65, 0x64, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x6f, 0x6c,
	0x79, 0x12, 0x2e, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x42, 0x03, 0xe0, 0x41, 0x03,
	0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x22, 0x4f, 0x0a, 0x1d, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x42, 0x03, 0xe0, 0x41,
	0x03, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x22, 0xbf, 0x01, 0x0a, 0x0e, 0x4d, 0x61, 0x73, 0x6b, 0x41, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5a, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x03, 0xe0, 0x41,
	0x03, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x61, 0x73,
	0x6b, 0x12, 0x51, 0x0a, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6d, 0x61,
	0x73, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e,
	0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31,
	0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x4d, 0x61, 0x73, 0x6b, 0x22, 0xb0, 0x01, 0x0a, 0x12, 0x50, 0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e,
	0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x6a, 0x0a, 0x13, 0x6e,
	0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x6c, 0x79, 0x6c, 0x69,
	0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e,
	0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31,
	0x2e, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x50, 0x6f, 0x6c, 0x79, 0x6c,
	0x69, 0x6e, 0x65, 0x52, 0x12, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x50,
	0x6f, 0x6c, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x2e, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x02, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x42, 0xff, 0x01, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x76, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x62, 0x65, 0x74, 0x61, 0x31, 0x50, 0x01, 0x5a, 0x55, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x3b, 0x76, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0xaa, 0x02,
	0x25, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x56, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x56,
	0x31, 0x42, 0x65, 0x74, 0x61, 0x31, 0xca, 0x02, 0x25, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c,
	0x43, 0x6c, 0x6f, 0x75, 0x64, 0x5c, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x70,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5c, 0x56, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0xea, 0x02,
	0x28, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x3a, 0x3a,
	0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x3a, 0x3a, 0x56, 0x31, 0x62, 0x65, 0x74, 0x61, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_google_cloud_visualinspection_v1beta1_annotation_proto_rawDescOnce sync.Once
	file_google_cloud_visualinspection_v1beta1_annotation_proto_rawDescData = file_google_cloud_visualinspection_v1beta1_annotation_proto_rawDesc
)

func file_google_cloud_visualinspection_v1beta1_annotation_proto_rawDescGZIP() []byte {
	file_google_cloud_visualinspection_v1beta1_annotation_proto_rawDescOnce.Do(func() {
		file_google_cloud_visualinspection_v1beta1_annotation_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_cloud_visualinspection_v1beta1_annotation_proto_rawDescData)
	})
	return file_google_cloud_visualinspection_v1beta1_annotation_proto_rawDescData
}

var file_google_cloud_visualinspection_v1beta1_annotation_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_google_cloud_visualinspection_v1beta1_annotation_proto_goTypes = []interface{}{
	(Annotation_Source_SourceType)(0),     // 0: google.cloud.visualinspection.v1beta1.Annotation.Source.SourceType
	(*Annotation)(nil),                    // 1: google.cloud.visualinspection.v1beta1.Annotation
	(*BoundingBoxAnnotation)(nil),         // 2: google.cloud.visualinspection.v1beta1.BoundingBoxAnnotation
	(*BoundingPolyAnnotation)(nil),        // 3: google.cloud.visualinspection.v1beta1.BoundingPolyAnnotation
	(*ClassificationLabelAnnotation)(nil), // 4: google.cloud.visualinspection.v1beta1.ClassificationLabelAnnotation
	(*MaskAnnotation)(nil),                // 5: google.cloud.visualinspection.v1beta1.MaskAnnotation
	(*PolylineAnnotation)(nil),            // 6: google.cloud.visualinspection.v1beta1.PolylineAnnotation
	(*Annotation_Source)(nil),             // 7: google.cloud.visualinspection.v1beta1.Annotation.Source
	nil,                                   // 8: google.cloud.visualinspection.v1beta1.Annotation.LabelsEntry
	(*timestamppb.Timestamp)(nil),         // 9: google.protobuf.Timestamp
	(*NormalizedBoundingPoly)(nil),        // 10: google.cloud.visualinspection.v1beta1.NormalizedBoundingPoly
	(*Image)(nil),                         // 11: google.cloud.visualinspection.v1beta1.Image
	(*NormalizedPolyline)(nil),            // 12: google.cloud.visualinspection.v1beta1.NormalizedPolyline
}
var file_google_cloud_visualinspection_v1beta1_annotation_proto_depIdxs = []int32{
	2,  // 0: google.cloud.visualinspection.v1beta1.Annotation.box:type_name -> google.cloud.visualinspection.v1beta1.BoundingBoxAnnotation
	3,  // 1: google.cloud.visualinspection.v1beta1.Annotation.polygon:type_name -> google.cloud.visualinspection.v1beta1.BoundingPolyAnnotation
	4,  // 2: google.cloud.visualinspection.v1beta1.Annotation.classification_label:type_name -> google.cloud.visualinspection.v1beta1.ClassificationLabelAnnotation
	5,  // 3: google.cloud.visualinspection.v1beta1.Annotation.mask:type_name -> google.cloud.visualinspection.v1beta1.MaskAnnotation
	6,  // 4: google.cloud.visualinspection.v1beta1.Annotation.polyline:type_name -> google.cloud.visualinspection.v1beta1.PolylineAnnotation
	9,  // 5: google.cloud.visualinspection.v1beta1.Annotation.create_time:type_name -> google.protobuf.Timestamp
	9,  // 6: google.cloud.visualinspection.v1beta1.Annotation.update_time:type_name -> google.protobuf.Timestamp
	7,  // 7: google.cloud.visualinspection.v1beta1.Annotation.source:type_name -> google.cloud.visualinspection.v1beta1.Annotation.Source
	8,  // 8: google.cloud.visualinspection.v1beta1.Annotation.labels:type_name -> google.cloud.visualinspection.v1beta1.Annotation.LabelsEntry
	10, // 9: google.cloud.visualinspection.v1beta1.BoundingBoxAnnotation.normalized_bounding_box:type_name -> google.cloud.visualinspection.v1beta1.NormalizedBoundingPoly
	10, // 10: google.cloud.visualinspection.v1beta1.BoundingPolyAnnotation.normalized_bounding_poly:type_name -> google.cloud.visualinspection.v1beta1.NormalizedBoundingPoly
	11, // 11: google.cloud.visualinspection.v1beta1.MaskAnnotation.confidence_mask:type_name -> google.cloud.visualinspection.v1beta1.Image
	11, // 12: google.cloud.visualinspection.v1beta1.MaskAnnotation.category_mask:type_name -> google.cloud.visualinspection.v1beta1.Image
	12, // 13: google.cloud.visualinspection.v1beta1.PolylineAnnotation.normalized_polyline:type_name -> google.cloud.visualinspection.v1beta1.NormalizedPolyline
	0,  // 14: google.cloud.visualinspection.v1beta1.Annotation.Source.type:type_name -> google.cloud.visualinspection.v1beta1.Annotation.Source.SourceType
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_google_cloud_visualinspection_v1beta1_annotation_proto_init() }
func file_google_cloud_visualinspection_v1beta1_annotation_proto_init() {
	if File_google_cloud_visualinspection_v1beta1_annotation_proto != nil {
		return
	}
	file_google_cloud_visualinspection_v1beta1_geometry_proto_init()
	file_google_cloud_visualinspection_v1beta1_image_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Annotation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoundingBoxAnnotation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoundingPolyAnnotation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClassificationLabelAnnotation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaskAnnotation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PolylineAnnotation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Annotation_Source); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Annotation_Box)(nil),
		(*Annotation_Polygon)(nil),
		(*Annotation_ClassificationLabel)(nil),
		(*Annotation_Mask)(nil),
		(*Annotation_Polyline)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_cloud_visualinspection_v1beta1_annotation_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_cloud_visualinspection_v1beta1_annotation_proto_goTypes,
		DependencyIndexes: file_google_cloud_visualinspection_v1beta1_annotation_proto_depIdxs,
		EnumInfos:         file_google_cloud_visualinspection_v1beta1_annotation_proto_enumTypes,
		MessageInfos:      file_google_cloud_visualinspection_v1beta1_annotation_proto_msgTypes,
	}.Build()
	File_google_cloud_visualinspection_v1beta1_annotation_proto = out.File
	file_google_cloud_visualinspection_v1beta1_annotation_proto_rawDesc = nil
	file_google_cloud_visualinspection_v1beta1_annotation_proto_goTypes = nil
	file_google_cloud_visualinspection_v1beta1_annotation_proto_depIdxs = nil
}
