// Copyright 2020 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.12.2
// source: google/maps/playablelocations/v3/playablelocations.proto

package playablelocations

import (
	context "context"
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	sample "google.golang.org/genproto/googleapis/maps/playablelocations/v3/sample"
	unity "google.golang.org/genproto/googleapis/maps/unity"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Life of a query:
//
// - When a game starts in a new location, your game server issues a
// [SamplePlayableLocations][google.maps.playablelocations.v3.PlayableLocations.SamplePlayableLocations]
// request. The request specifies the S2 cell, and contains one or more
// "criteria" for filtering:
//
// - Criterion 0: i locations for long-lived bases, or level 0 monsters, or...
// - Criterion 1: j locations for short-lived bases, or level 1 monsters, ...
// - Criterion 2: k locations for random objects.
// - etc (up to 5 criterion may be specified).
//
// `PlayableLocationList` will then contain mutually
// exclusive lists of `PlayableLocation` objects that satisfy each of
// the criteria. Think of it as a collection of real-world locations that you
// can then associate with your game state.
//
// Note: These points are impermanent in nature. E.g, parks can close, and
// places can be removed.
//
// The response specifies how long you can expect the playable locations to
// last. Once they expire, you should query the `samplePlayableLocations` API
// again to get a fresh view of the real world.
type SamplePlayableLocationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. Specifies the area to search within for playable locations.
	AreaFilter *sample.AreaFilter `protobuf:"bytes,1,opt,name=area_filter,json=areaFilter,proto3" json:"area_filter,omitempty"`
	// Required. Specifies one or more (up to 5) criteria for filtering the
	// returned playable locations.
	Criteria []*sample.Criterion `protobuf:"bytes,2,rep,name=criteria,proto3" json:"criteria,omitempty"`
}

func (x *SamplePlayableLocationsRequest) Reset() {
	*x = SamplePlayableLocationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SamplePlayableLocationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SamplePlayableLocationsRequest) ProtoMessage() {}

func (x *SamplePlayableLocationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SamplePlayableLocationsRequest.ProtoReflect.Descriptor instead.
func (*SamplePlayableLocationsRequest) Descriptor() ([]byte, []int) {
	return file_google_maps_playablelocations_v3_playablelocations_proto_rawDescGZIP(), []int{0}
}

func (x *SamplePlayableLocationsRequest) GetAreaFilter() *sample.AreaFilter {
	if x != nil {
		return x.AreaFilter
	}
	return nil
}

func (x *SamplePlayableLocationsRequest) GetCriteria() []*sample.Criterion {
	if x != nil {
		return x.Criteria
	}
	return nil
}

// Response for the
// [SamplePlayableLocations][google.maps.playablelocations.v3.PlayableLocations.SamplePlayableLocations]
// method.
type SamplePlayableLocationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Each PlayableLocation object corresponds to a game_object_type specified
	// in the request.
	LocationsPerGameObjectType map[int32]*sample.PlayableLocationList `protobuf:"bytes,1,rep,name=locations_per_game_object_type,json=locationsPerGameObjectType,proto3" json:"locations_per_game_object_type,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Required. Specifies the "time-to-live" for the set of playable locations.
	// You can use this value to determine how long to cache the set of playable
	// locations. After this length of time, your back-end game server should
	// issue a new
	// [SamplePlayableLocations][google.maps.playablelocations.v3.PlayableLocations.SamplePlayableLocations]
	// request to get a fresh set of playable locations (because for example, they
	// might have been removed, a park might have closed for the day, a
	// business might have closed permanently).
	Ttl *durationpb.Duration `protobuf:"bytes,9,opt,name=ttl,proto3" json:"ttl,omitempty"`
}

func (x *SamplePlayableLocationsResponse) Reset() {
	*x = SamplePlayableLocationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SamplePlayableLocationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SamplePlayableLocationsResponse) ProtoMessage() {}

func (x *SamplePlayableLocationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SamplePlayableLocationsResponse.ProtoReflect.Descriptor instead.
func (*SamplePlayableLocationsResponse) Descriptor() ([]byte, []int) {
	return file_google_maps_playablelocations_v3_playablelocations_proto_rawDescGZIP(), []int{1}
}

func (x *SamplePlayableLocationsResponse) GetLocationsPerGameObjectType() map[int32]*sample.PlayableLocationList {
	if x != nil {
		return x.LocationsPerGameObjectType
	}
	return nil
}

func (x *SamplePlayableLocationsResponse) GetTtl() *durationpb.Duration {
	if x != nil {
		return x.Ttl
	}
	return nil
}

// A request for logging your player's bad location reports.
type LogPlayerReportsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. Player reports. The maximum number of player reports that you can
	// log at once is 50.
	PlayerReports []*PlayerReport `protobuf:"bytes,1,rep,name=player_reports,json=playerReports,proto3" json:"player_reports,omitempty"`
	// Required. A string that uniquely identifies the log player reports request.
	// This allows you to detect duplicate requests. We recommend that you use
	// UUIDs for this value. The value must not exceed 50 characters.
	//
	// You should reuse the `request_id` only when retrying a request in the case
	// of a failure. In that case, the request must be identical to the one that
	// failed.
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Required. Information about the client device (for example, device model
	// and operating system).
	ClientInfo *unity.ClientInfo `protobuf:"bytes,3,opt,name=client_info,json=clientInfo,proto3" json:"client_info,omitempty"`
}

func (x *LogPlayerReportsRequest) Reset() {
	*x = LogPlayerReportsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogPlayerReportsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogPlayerReportsRequest) ProtoMessage() {}

func (x *LogPlayerReportsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogPlayerReportsRequest.ProtoReflect.Descriptor instead.
func (*LogPlayerReportsRequest) Descriptor() ([]byte, []int) {
	return file_google_maps_playablelocations_v3_playablelocations_proto_rawDescGZIP(), []int{2}
}

func (x *LogPlayerReportsRequest) GetPlayerReports() []*PlayerReport {
	if x != nil {
		return x.PlayerReports
	}
	return nil
}

func (x *LogPlayerReportsRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *LogPlayerReportsRequest) GetClientInfo() *unity.ClientInfo {
	if x != nil {
		return x.ClientInfo
	}
	return nil
}

// A response for the
// [LogPlayerReports][google.maps.playablelocations.v3.PlayableLocations.LogPlayerReports]
// method.
//
// This method returns no data upon success.
type LogPlayerReportsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LogPlayerReportsResponse) Reset() {
	*x = LogPlayerReportsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogPlayerReportsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogPlayerReportsResponse) ProtoMessage() {}

func (x *LogPlayerReportsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogPlayerReportsResponse.ProtoReflect.Descriptor instead.
func (*LogPlayerReportsResponse) Descriptor() ([]byte, []int) {
	return file_google_maps_playablelocations_v3_playablelocations_proto_rawDescGZIP(), []int{3}
}

// A request for logging impressions.
type LogImpressionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. Impression event details. The maximum number of impression
	// reports that you can log at once is 50.
	Impressions []*Impression `protobuf:"bytes,1,rep,name=impressions,proto3" json:"impressions,omitempty"`
	// Required. A string that uniquely identifies the log impressions request.
	// This allows you to detect duplicate requests. We recommend that you use
	// UUIDs for this value. The value must not exceed 50 characters.
	//
	// You should reuse the `request_id` only when retrying a request in case of
	// failure. In this case, the request must be identical to the one that
	// failed.
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Required. Information about the client device. For example, device model
	// and operating system.
	ClientInfo *unity.ClientInfo `protobuf:"bytes,3,opt,name=client_info,json=clientInfo,proto3" json:"client_info,omitempty"`
}

func (x *LogImpressionsRequest) Reset() {
	*x = LogImpressionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogImpressionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogImpressionsRequest) ProtoMessage() {}

func (x *LogImpressionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogImpressionsRequest.ProtoReflect.Descriptor instead.
func (*LogImpressionsRequest) Descriptor() ([]byte, []int) {
	return file_google_maps_playablelocations_v3_playablelocations_proto_rawDescGZIP(), []int{4}
}

func (x *LogImpressionsRequest) GetImpressions() []*Impression {
	if x != nil {
		return x.Impressions
	}
	return nil
}

func (x *LogImpressionsRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *LogImpressionsRequest) GetClientInfo() *unity.ClientInfo {
	if x != nil {
		return x.ClientInfo
	}
	return nil
}

// A response for the
// [LogImpressions][google.maps.playablelocations.v3.PlayableLocations.LogImpressions]
// method. This method returns no data upon success.
type LogImpressionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LogImpressionsResponse) Reset() {
	*x = LogImpressionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogImpressionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogImpressionsResponse) ProtoMessage() {}

func (x *LogImpressionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogImpressionsResponse.ProtoReflect.Descriptor instead.
func (*LogImpressionsResponse) Descriptor() ([]byte, []int) {
	return file_google_maps_playablelocations_v3_playablelocations_proto_rawDescGZIP(), []int{5}
}

var File_google_maps_playablelocations_v3_playablelocations_proto protoreflect.FileDescriptor

var file_google_maps_playablelocations_v3_playablelocations_proto_rawDesc = []byte{
	0x0a, 0x38, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x70, 0x6c,
	0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x76, 0x33, 0x2f, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x20, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x33, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68,
	0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c,
	0x65, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x76, 0x33, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x73, 0x2f, 0x70, 0x6c, 0x61, 0x79, 0x61,
	0x62, 0x6c, 0x65, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x76, 0x33, 0x2f,
	0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6d,
	0x61, 0x70, 0x73, 0x2f, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xd0, 0x01, 0x0a, 0x1e, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c,
	0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x59, 0x0a, 0x0b, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62,
	0x6c, 0x65, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x33, 0x2e, 0x73,
	0x61, 0x6d, 0x70, 0x6c, 0x65, 0x2e, 0x41, 0x72, 0x65, 0x61, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0a, 0x61, 0x72, 0x65, 0x61, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x53, 0x0a, 0x08, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70,
	0x73, 0x2e, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x33, 0x2e, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x2e, 0x43, 0x72,
	0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x08, 0x63, 0x72,
	0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x22, 0x85, 0x03, 0x0a, 0x1f, 0x53, 0x61, 0x6d, 0x70, 0x6c,
	0x65, 0x50, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa5, 0x01, 0x0a, 0x1e, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x67, 0x61, 0x6d,
	0x65, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x61, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70,
	0x73, 0x2e, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x33, 0x2e, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c, 0x61,
	0x79, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x50, 0x65, 0x72, 0x47, 0x61, 0x6d, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x1a, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x50, 0x65, 0x72, 0x47, 0x61, 0x6d, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x2b, 0x0a, 0x03, 0x74, 0x74, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x03, 0x74, 0x74, 0x6c, 0x1a,
	0x8c, 0x01, 0x0a, 0x1f, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x65, 0x72,
	0x47, 0x61, 0x6d, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x53, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61,
	0x70, 0x73, 0x2e, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x33, 0x2e, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x2e, 0x50,
	0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xde,
	0x01, 0x0a, 0x17, 0x4c, 0x6f, 0x67, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5a, 0x0a, 0x0e, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73,
	0x2e, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x76, 0x33, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x22, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x0b, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x75, 0x6e,
	0x69, 0x74, 0x79, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x03,
	0xe0, 0x41, 0x02, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x1a, 0x0a, 0x18, 0x4c, 0x6f, 0x67, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xd5, 0x01, 0x0a, 0x15,
	0x4c, 0x6f, 0x67, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x53, 0x0a, 0x0b, 0x69, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c,
	0x65, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x33, 0x2e, 0x49, 0x6d,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0b, 0x69,
	0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x22, 0x0a, 0x0a, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03,
	0xe0, 0x41, 0x02, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x43,
	0x0a, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70,
	0x73, 0x2e, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0x18, 0x0a, 0x16, 0x4c, 0x6f, 0x67, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xd3, 0x04,
	0x0a, 0x11, 0x50, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0xc6, 0x01, 0x0a, 0x17, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c,
	0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x40, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x70, 0x6c,
	0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x76, 0x33, 0x2e, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c,
	0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x41, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e,
	0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x76, 0x33, 0x2e, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x61,
	0x62, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x22, 0x1b, 0x2f, 0x76,
	0x33, 0x3a, 0x73, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0xaa, 0x01, 0x0a,
	0x10, 0x4c, 0x6f, 0x67, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x12, 0x39, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e,
	0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x76, 0x33, 0x2e, 0x4c, 0x6f, 0x67, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x70, 0x6c, 0x61, 0x79, 0x61,
	0x62, 0x6c, 0x65, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x33, 0x2e,
	0x4c, 0x6f, 0x67, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19,
	0x22, 0x14, 0x2f, 0x76, 0x33, 0x3a, 0x6c, 0x6f, 0x67, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0xa2, 0x01, 0x0a, 0x0e, 0x4c, 0x6f,
	0x67, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x37, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x70, 0x6c, 0x61, 0x79, 0x61,
	0x62, 0x6c, 0x65, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x33, 0x2e,
	0x4c, 0x6f, 0x67, 0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6d,
	0x61, 0x70, 0x73, 0x2e, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x33, 0x2e, 0x4c, 0x6f, 0x67, 0x49, 0x6d, 0x70, 0x72,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x22, 0x12, 0x2f, 0x76, 0x33, 0x3a, 0x6c, 0x6f, 0x67,
	0x49, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x01, 0x2a, 0x1a, 0x23,
	0xca, 0x41, 0x20, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x63, 0x6f, 0x6d, 0x42, 0xe0, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x70, 0x73, 0x2e, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x76, 0x33, 0x42, 0x16, 0x50, 0x6c,
	0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x51, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67,
	0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x6d, 0x61,
	0x70, 0x73, 0x2f, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x76, 0x33, 0x3b, 0x70, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0xa2, 0x02, 0x04, 0x47, 0x4d, 0x50, 0x4c,
	0xaa, 0x02, 0x20, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x4d, 0x61, 0x70, 0x73, 0x2e, 0x50,
	0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x56, 0x33, 0xca, 0x02, 0x20, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x4d, 0x61, 0x70,
	0x73, 0x5c, 0x50, 0x6c, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x5c, 0x56, 0x33, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_maps_playablelocations_v3_playablelocations_proto_rawDescOnce sync.Once
	file_google_maps_playablelocations_v3_playablelocations_proto_rawDescData = file_google_maps_playablelocations_v3_playablelocations_proto_rawDesc
)

func file_google_maps_playablelocations_v3_playablelocations_proto_rawDescGZIP() []byte {
	file_google_maps_playablelocations_v3_playablelocations_proto_rawDescOnce.Do(func() {
		file_google_maps_playablelocations_v3_playablelocations_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_maps_playablelocations_v3_playablelocations_proto_rawDescData)
	})
	return file_google_maps_playablelocations_v3_playablelocations_proto_rawDescData
}

var file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_google_maps_playablelocations_v3_playablelocations_proto_goTypes = []interface{}{
	(*SamplePlayableLocationsRequest)(nil),  // 0: google.maps.playablelocations.v3.SamplePlayableLocationsRequest
	(*SamplePlayableLocationsResponse)(nil), // 1: google.maps.playablelocations.v3.SamplePlayableLocationsResponse
	(*LogPlayerReportsRequest)(nil),         // 2: google.maps.playablelocations.v3.LogPlayerReportsRequest
	(*LogPlayerReportsResponse)(nil),        // 3: google.maps.playablelocations.v3.LogPlayerReportsResponse
	(*LogImpressionsRequest)(nil),           // 4: google.maps.playablelocations.v3.LogImpressionsRequest
	(*LogImpressionsResponse)(nil),          // 5: google.maps.playablelocations.v3.LogImpressionsResponse
	nil,                                     // 6: google.maps.playablelocations.v3.SamplePlayableLocationsResponse.LocationsPerGameObjectTypeEntry
	(*sample.AreaFilter)(nil),               // 7: google.maps.playablelocations.v3.sample.AreaFilter
	(*sample.Criterion)(nil),                // 8: google.maps.playablelocations.v3.sample.Criterion
	(*durationpb.Duration)(nil),             // 9: google.protobuf.Duration
	(*PlayerReport)(nil),                    // 10: google.maps.playablelocations.v3.PlayerReport
	(*unity.ClientInfo)(nil),                // 11: google.maps.unity.ClientInfo
	(*Impression)(nil),                      // 12: google.maps.playablelocations.v3.Impression
	(*sample.PlayableLocationList)(nil),     // 13: google.maps.playablelocations.v3.sample.PlayableLocationList
}
var file_google_maps_playablelocations_v3_playablelocations_proto_depIdxs = []int32{
	7,  // 0: google.maps.playablelocations.v3.SamplePlayableLocationsRequest.area_filter:type_name -> google.maps.playablelocations.v3.sample.AreaFilter
	8,  // 1: google.maps.playablelocations.v3.SamplePlayableLocationsRequest.criteria:type_name -> google.maps.playablelocations.v3.sample.Criterion
	6,  // 2: google.maps.playablelocations.v3.SamplePlayableLocationsResponse.locations_per_game_object_type:type_name -> google.maps.playablelocations.v3.SamplePlayableLocationsResponse.LocationsPerGameObjectTypeEntry
	9,  // 3: google.maps.playablelocations.v3.SamplePlayableLocationsResponse.ttl:type_name -> google.protobuf.Duration
	10, // 4: google.maps.playablelocations.v3.LogPlayerReportsRequest.player_reports:type_name -> google.maps.playablelocations.v3.PlayerReport
	11, // 5: google.maps.playablelocations.v3.LogPlayerReportsRequest.client_info:type_name -> google.maps.unity.ClientInfo
	12, // 6: google.maps.playablelocations.v3.LogImpressionsRequest.impressions:type_name -> google.maps.playablelocations.v3.Impression
	11, // 7: google.maps.playablelocations.v3.LogImpressionsRequest.client_info:type_name -> google.maps.unity.ClientInfo
	13, // 8: google.maps.playablelocations.v3.SamplePlayableLocationsResponse.LocationsPerGameObjectTypeEntry.value:type_name -> google.maps.playablelocations.v3.sample.PlayableLocationList
	0,  // 9: google.maps.playablelocations.v3.PlayableLocations.SamplePlayableLocations:input_type -> google.maps.playablelocations.v3.SamplePlayableLocationsRequest
	2,  // 10: google.maps.playablelocations.v3.PlayableLocations.LogPlayerReports:input_type -> google.maps.playablelocations.v3.LogPlayerReportsRequest
	4,  // 11: google.maps.playablelocations.v3.PlayableLocations.LogImpressions:input_type -> google.maps.playablelocations.v3.LogImpressionsRequest
	1,  // 12: google.maps.playablelocations.v3.PlayableLocations.SamplePlayableLocations:output_type -> google.maps.playablelocations.v3.SamplePlayableLocationsResponse
	3,  // 13: google.maps.playablelocations.v3.PlayableLocations.LogPlayerReports:output_type -> google.maps.playablelocations.v3.LogPlayerReportsResponse
	5,  // 14: google.maps.playablelocations.v3.PlayableLocations.LogImpressions:output_type -> google.maps.playablelocations.v3.LogImpressionsResponse
	12, // [12:15] is the sub-list for method output_type
	9,  // [9:12] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_google_maps_playablelocations_v3_playablelocations_proto_init() }
func file_google_maps_playablelocations_v3_playablelocations_proto_init() {
	if File_google_maps_playablelocations_v3_playablelocations_proto != nil {
		return
	}
	file_google_maps_playablelocations_v3_resources_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SamplePlayableLocationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SamplePlayableLocationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogPlayerReportsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogPlayerReportsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogImpressionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogImpressionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_maps_playablelocations_v3_playablelocations_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_google_maps_playablelocations_v3_playablelocations_proto_goTypes,
		DependencyIndexes: file_google_maps_playablelocations_v3_playablelocations_proto_depIdxs,
		MessageInfos:      file_google_maps_playablelocations_v3_playablelocations_proto_msgTypes,
	}.Build()
	File_google_maps_playablelocations_v3_playablelocations_proto = out.File
	file_google_maps_playablelocations_v3_playablelocations_proto_rawDesc = nil
	file_google_maps_playablelocations_v3_playablelocations_proto_goTypes = nil
	file_google_maps_playablelocations_v3_playablelocations_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// PlayableLocationsClient is the client API for PlayableLocations service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PlayableLocationsClient interface {
	// Returns a set of playable locations that lie within a specified area,
	// that satisfy optional filter criteria.
	//
	// Note: Identical `SamplePlayableLocations` requests can return different
	// results as the state of the world changes over time.
	SamplePlayableLocations(ctx context.Context, in *SamplePlayableLocationsRequest, opts ...grpc.CallOption) (*SamplePlayableLocationsResponse, error)
	// Logs bad playable location reports submitted by players.
	//
	// Reports are not partially saved; either all reports are saved and this
	// request succeeds, or no reports are saved, and this request fails.
	LogPlayerReports(ctx context.Context, in *LogPlayerReportsRequest, opts ...grpc.CallOption) (*LogPlayerReportsResponse, error)
	// Logs new events when playable locations are displayed, and when they are
	// interacted with.
	//
	// Impressions are not partially saved; either all impressions are saved and
	// this request succeeds, or no impressions are saved, and this request fails.
	LogImpressions(ctx context.Context, in *LogImpressionsRequest, opts ...grpc.CallOption) (*LogImpressionsResponse, error)
}

type playableLocationsClient struct {
	cc grpc.ClientConnInterface
}

func NewPlayableLocationsClient(cc grpc.ClientConnInterface) PlayableLocationsClient {
	return &playableLocationsClient{cc}
}

func (c *playableLocationsClient) SamplePlayableLocations(ctx context.Context, in *SamplePlayableLocationsRequest, opts ...grpc.CallOption) (*SamplePlayableLocationsResponse, error) {
	out := new(SamplePlayableLocationsResponse)
	err := c.cc.Invoke(ctx, "/google.maps.playablelocations.v3.PlayableLocations/SamplePlayableLocations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *playableLocationsClient) LogPlayerReports(ctx context.Context, in *LogPlayerReportsRequest, opts ...grpc.CallOption) (*LogPlayerReportsResponse, error) {
	out := new(LogPlayerReportsResponse)
	err := c.cc.Invoke(ctx, "/google.maps.playablelocations.v3.PlayableLocations/LogPlayerReports", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *playableLocationsClient) LogImpressions(ctx context.Context, in *LogImpressionsRequest, opts ...grpc.CallOption) (*LogImpressionsResponse, error) {
	out := new(LogImpressionsResponse)
	err := c.cc.Invoke(ctx, "/google.maps.playablelocations.v3.PlayableLocations/LogImpressions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PlayableLocationsServer is the server API for PlayableLocations service.
type PlayableLocationsServer interface {
	// Returns a set of playable locations that lie within a specified area,
	// that satisfy optional filter criteria.
	//
	// Note: Identical `SamplePlayableLocations` requests can return different
	// results as the state of the world changes over time.
	SamplePlayableLocations(context.Context, *SamplePlayableLocationsRequest) (*SamplePlayableLocationsResponse, error)
	// Logs bad playable location reports submitted by players.
	//
	// Reports are not partially saved; either all reports are saved and this
	// request succeeds, or no reports are saved, and this request fails.
	LogPlayerReports(context.Context, *LogPlayerReportsRequest) (*LogPlayerReportsResponse, error)
	// Logs new events when playable locations are displayed, and when they are
	// interacted with.
	//
	// Impressions are not partially saved; either all impressions are saved and
	// this request succeeds, or no impressions are saved, and this request fails.
	LogImpressions(context.Context, *LogImpressionsRequest) (*LogImpressionsResponse, error)
}

// UnimplementedPlayableLocationsServer can be embedded to have forward compatible implementations.
type UnimplementedPlayableLocationsServer struct {
}

func (*UnimplementedPlayableLocationsServer) SamplePlayableLocations(context.Context, *SamplePlayableLocationsRequest) (*SamplePlayableLocationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SamplePlayableLocations not implemented")
}
func (*UnimplementedPlayableLocationsServer) LogPlayerReports(context.Context, *LogPlayerReportsRequest) (*LogPlayerReportsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LogPlayerReports not implemented")
}
func (*UnimplementedPlayableLocationsServer) LogImpressions(context.Context, *LogImpressionsRequest) (*LogImpressionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LogImpressions not implemented")
}

func RegisterPlayableLocationsServer(s *grpc.Server, srv PlayableLocationsServer) {
	s.RegisterService(&_PlayableLocations_serviceDesc, srv)
}

func _PlayableLocations_SamplePlayableLocations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SamplePlayableLocationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlayableLocationsServer).SamplePlayableLocations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.maps.playablelocations.v3.PlayableLocations/SamplePlayableLocations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlayableLocationsServer).SamplePlayableLocations(ctx, req.(*SamplePlayableLocationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlayableLocations_LogPlayerReports_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogPlayerReportsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlayableLocationsServer).LogPlayerReports(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.maps.playablelocations.v3.PlayableLocations/LogPlayerReports",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlayableLocationsServer).LogPlayerReports(ctx, req.(*LogPlayerReportsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlayableLocations_LogImpressions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogImpressionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlayableLocationsServer).LogImpressions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.maps.playablelocations.v3.PlayableLocations/LogImpressions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlayableLocationsServer).LogImpressions(ctx, req.(*LogImpressionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PlayableLocations_serviceDesc = grpc.ServiceDesc{
	ServiceName: "google.maps.playablelocations.v3.PlayableLocations",
	HandlerType: (*PlayableLocationsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SamplePlayableLocations",
			Handler:    _PlayableLocations_SamplePlayableLocations_Handler,
		},
		{
			MethodName: "LogPlayerReports",
			Handler:    _PlayableLocations_LogPlayerReports_Handler,
		},
		{
			MethodName: "LogImpressions",
			Handler:    _PlayableLocations_LogImpressions_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "google/maps/playablelocations/v3/playablelocations.proto",
}
